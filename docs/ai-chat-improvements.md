# AI Chat Service Schema Improvements

## Overview

The AI Chat Service has been enhanced to provide comprehensive database schema information to the AI model, significantly improving the accuracy and usefulness of generated SQL queries.

## Key Improvements

### 1. Database Schema Information

**Before**: The AI only received a list of allowed table names.

**After**: The AI now receives detailed schema information including:
- Column names and data types
- Nullable/NOT NULL constraints
- Primary keys and indexes
- Foreign key relationships
- Domain-specific context about the Umrah service system

### 2. Enhanced System Prompt

The system prompt now includes:
- Complete database schema with column details
- Foreign key relationships for proper joins
- Data type information for accurate conditions
- Domain context explaining the business concepts
- More specific rules about using schema information

### 3. Performance Optimization

- **Caching**: Schema information is cached for 1 hour to avoid repeated database queries
- **Cache Management**: Added methods to clear and refresh the cache
- **Artisan Command**: Added `ai-chat:cache` command for cache management

### 4. Better Validation

- Enhanced raw expression validation
- Improved join condition parsing
- Foreign key relationship validation

## Usage Examples

### Cache Management

```bash
# Refresh the schema cache
php artisan ai-chat:cache refresh

# Clear the schema cache
php artisan ai-chat:cache clear

# Show current schema information
php artisan ai-chat:cache show
```

### Programmatic Cache Management

```php
$aiChatService = new AiChatService();

// Clear the cache
$aiChatService->clearSchemaCache();
```

## Benefits

1. **More Accurate Queries**: AI can now generate queries with correct column names and data types
2. **Better Joins**: AI understands foreign key relationships and can create proper join conditions
3. **Domain Awareness**: AI understands the business context (customers, groups, pilgrims, etc.)
4. **Performance**: Cached schema information reduces database overhead
5. **Maintainability**: Clear separation of schema building and caching logic

## Example Schema Information Provided to AI

```
Available Tables and Schema:

Table: customers
Columns:
  - id: bigint(20) unsigned NOT NULL (PRI)
  - name: varchar(255) NOT NULL
  - email: varchar(255) NULL
  - phone: varchar(20) NULL
  - created_at: timestamp NULL
  - updated_at: timestamp NULL
Foreign Keys:
  - user_id -> users.id

Table: groups
Columns:
  - id: bigint(20) unsigned NOT NULL (PRI)
  - name: varchar(255) NOT NULL
  - customer_id: bigint(20) unsigned NOT NULL
  - departure_date: date NULL
  - return_date: date NULL
  - created_at: timestamp NULL
  - updated_at: timestamp NULL
Foreign Keys:
  - customer_id -> customers.id

Domain Context:
This is an Umrah service management system with the following key concepts:
- Customers: People who book Umrah services
- Groups: Collections of pilgrims traveling together
- Pilgrims: Individual travelers in a group
- Hotels: Accommodation for pilgrims
- Flights: Air transportation
- Itineraries: Daily schedules and activities
- Invoices/Bills: Financial transactions
- Rooms: Hotel room assignments for pilgrims
- Vendors: Service providers (hotels, transport, etc.)
```

## Testing

The improvements include comprehensive tests covering:
- Schema information generation
- Caching functionality
- Raw expression validation
- Join condition parsing

Run tests with:
```bash
php artisan test tests/Feature/AiChatServiceTest.php
```

## Future Enhancements

Potential future improvements could include:
1. **Index Information**: Include database indexes for query optimization hints
2. **Sample Data**: Provide sample values for better context
3. **Relationship Descriptions**: More detailed explanations of table relationships
4. **Query Templates**: Pre-built query patterns for common use cases
5. **Performance Metrics**: Track query performance and suggest optimizations
