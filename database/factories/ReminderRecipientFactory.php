<?php

namespace Database\Factories;

use App\Enums\ReminderRecipientType;
use App\Models\Reminder;
use App\Models\ReminderRecipient;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Spatie\Permission\Models\Role;

class ReminderRecipientFactory extends Factory
{
    protected $model = ReminderRecipient::class;

    public function definition(): array
    {
        $recipientTypes = [
            ReminderRecipientType::User->value,
            ReminderRecipientType::Role->value,
        ];
        $recipientType = $this->faker->randomElement($recipientTypes);

        return [
            'reminder_id' => Reminder::factory(),
            'recipient_type' => $recipientType,
            'recipient_id' => $recipientType === ReminderRecipientType::User->value
                ? User::factory()
                : Role::first()->id ?? 1,
        ];
    }

    public function user(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'recipient_type' => ReminderRecipientType::User->value,
                'recipient_id' => User::factory(),
            ];
        });
    }

    public function role(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'recipient_type' => ReminderRecipientType::Role->value,
                'recipient_id' => Role::first()->id ?? 1,
            ];
        });
    }

    public function handler(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'recipient_type' => ReminderRecipientType::Handler->value,
                'recipient_id' => null, // Handler doesn't need a recipient_id
            ];
        });
    }
}
