<?php

namespace Database\Factories;

use App\Models\Agent;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->company(),
            'owner_name' => $this->faker->name(),
            'email' => $this->faker->unique()->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'password' => 'password', // Default password for testing
            'permit_no' => $this->faker->numerify('PPIU-####/####'),
            'region' => $this->faker->city(),
            'address' => $this->faker->address(),
        ];
    }

    /**
     * Assign a user to the customer.
     *
     * @return static
     */
    public function withUser(?User $user = null)
    {
        return $this->state(function (array $attributes) use ($user) {
            return [
                'user_id' => $user ?? User::factory()->create()->id,
            ];
        });
    }

    /**
     * Assign an agent to the customer.
     *
     * @return static
     */
    public function withAgent(?Agent $agent = null)
    {
        return $this->state(function (array $attributes) use ($agent) {
            return [
                'agent_id' => $agent ?? Agent::factory()->create()->id,
            ];
        });
    }

    /**
     * Set the customer's logo.
     *
     * @return static
     */
    public function withLogo(string $logoPath = 'customers/default-logo.png')
    {
        return $this->state(function (array $attributes) use ($logoPath) {
            return [
                'logo' => $logoPath,
            ];
        });
    }

    /**
     * Set the customer's square logo.
     *
     * @return static
     */
    public function withSquareLogo(string $logoPath = 'customers/default-square-logo.png')
    {
        return $this->state(function (array $attributes) use ($logoPath) {
            return [
                'logo_square' => $logoPath,
            ];
        });
    }

    /**
     * Create a customer with groups.
     *
     * @return static
     */
    public function withGroups(int $count = 1)
    {
        return $this->afterCreating(function (Customer $customer) use ($count) {
            \App\Models\Group::factory()
                ->count($count)
                ->create(['customer_id' => $customer->id]);
        });
    }

    /**
     * Create a customer with users.
     *
     * @return static
     */
    public function withUsers(int $count = 1)
    {
        return $this->afterCreating(function (Customer $customer) use ($count) {
            \App\Models\CustomerUser::factory()
                ->count($count)
                ->create(['customer_id' => $customer->id]);
        });
    }
}
