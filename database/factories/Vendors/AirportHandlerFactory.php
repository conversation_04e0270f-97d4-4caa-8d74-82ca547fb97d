<?php

namespace Database\Factories\Vendors;

use App\Enums\VendorType;
use App\Models\Vendors\AirportHandler;
use Illuminate\Database\Eloquent\Factories\Factory;

class AirportHandlerFactory extends Factory
{
    protected $model = AirportHandler::class;

    public function definition(): array
    {
        return [
            'vendor_type' => VendorType::AirportHandler,
            'company_name' => $this->faker->company() . ' Airport Services',
            'contact_name' => $this->faker->name(),
            'contact_email' => $this->faker->safeEmail(),
            'contact_phone' => $this->faker->phoneNumber(),
            'meta' => [],
        ];
    }
}
