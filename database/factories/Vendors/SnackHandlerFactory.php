<?php

namespace Database\Factories\Vendors;

use App\Enums\VendorType;
use App\Models\Vendors\SnackHandler;
use Illuminate\Database\Eloquent\Factories\Factory;

class SnackHandlerFactory extends Factory
{
    protected $model = SnackHandler::class;

    public function definition(): array
    {
        return [
            'vendor_type' => VendorType::Snack,
            'company_name' => $this->faker->company() . ' Snacks',
            'contact_name' => $this->faker->name(),
            'contact_email' => $this->faker->safeEmail(),
            'contact_phone' => $this->faker->phoneNumber(),
            'meta' => [],
        ];
    }
}
