<?php

namespace Database\Factories;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'email_verified_at' => now(),
            'password' => 'password',
            'remember_token' => Str::random(10),
            'last_login_at' => null,
            'last_login_ip' => null,
            'meta' => [],
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return static
     */
    public function unverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }

    /**
     * Assign a role to the user.
     *
     * @return static
     */
    public function role(string | UserRole $role)
    {
        if ($role instanceof UserRole) {
            $role = $role->value;
        }

        return $this->afterCreating(function (User $user) use ($role) {
            $roleModel = Role::firstOrCreate(['name' => $role]);
            $user->assignRole($roleModel);
        });
    }

    /**
     * Create an admin user.
     *
     * @return static
     */
    public function admin()
    {
        return $this->role('Admin');
    }

    /**
     * Create a mutawif user.
     *
     * @return static
     */
    public function mutawif()
    {
        return $this->role('Mutawif');
    }

    /**
     * Create a finance user.
     *
     * @return static
     */
    public function finance()
    {
        return $this->role('Finance');
    }

    /**
     * Create an operator user.
     *
     * @return static
     */
    public function operator()
    {
        return $this->role('Operator');
    }
}
