<?php

namespace Database\Factories;

use App\Enums\NotificationType;
use App\Models\Reminder;
use App\ReminderTypes\Departure;
use App\ReminderTypes\SnackReminder;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReminderFactory extends Factory
{
    protected $model = Reminder::class;

    public function definition(): array
    {
        $reminderTypes = [
            'Departure',
            'SnackReminder',
        ];

        $reminderType = $this->faker->randomElement($reminderTypes);
        $defaultTemplate = $reminderType === 'Departure' 
            ? Departure::getDefaultTemplate() 
            : SnackReminder::getDefaultTemplate();

        return [
            'reminder_type' => $reminderType,
            'subject' => $this->faker->sentence(),
            'notification_type' => NotificationType::Email,
            'template' => $defaultTemplate,
            'interval' => $this->faker->numberBetween(1, 7),
            'time' => $this->faker->time(),
            'active' => true,
            'additional_data' => [],
        ];
    }

    public function inactive(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => false,
            ];
        });
    }

    public function departure(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'reminder_type' => 'Departure',
                'template' => Departure::getDefaultTemplate(),
            ];
        });
    }

    public function snackReminder(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'reminder_type' => 'SnackReminder',
                'template' => SnackReminder::getDefaultTemplate(),
                'additional_data' => [
                    'city' => $this->faker->randomElement(['Makkah', 'Madinah']),
                ],
            ];
        });
    }

    public function whatsapp(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'notification_type' => NotificationType::WhatsApp,
            ];
        });
    }
}
