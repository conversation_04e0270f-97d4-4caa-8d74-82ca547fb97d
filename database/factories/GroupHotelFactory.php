<?php

namespace Database\Factories;

use App\Enums\HotelRoomType;
use App\Enums\VendorType;
use App\Models\Group;
use App\Models\GroupHotel;
use App\Models\Hotel;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GroupHotel>
 */
class GroupHotelFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = GroupHotel::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $checkIn = now()->addDays($this->faker->numberBetween(1, 30));
        $checkOut = (clone $checkIn)->addDays($this->faker->numberBetween(1, 7));

        return [
            'group_id' => Group::factory(),
            'hotel_id' => Hotel::factory(),

            'check_in' => $checkIn,
            'check_out' => $checkOut,

            'room_single_count' => $this->faker->numberBetween(0, 5),
            'room_double_count' => $this->faker->numberBetween(0, 10),
            'room_triple_count' => $this->faker->numberBetween(0, 10),
            'room_quad_count' => $this->faker->numberBetween(0, 10),
            'room_quint_count' => $this->faker->numberBetween(0, 5),

            'is_confirmed' => $this->faker->boolean(70),
            'confirmation_number' => $this->faker->boolean(70) ? $this->faker->bothify('??####') : null,
            'confirmation_file' => null,

            'broker_id' => null,

            'notes' => $this->faker->boolean(30) ? $this->faker->paragraph() : null,
            'more_info' => $this->faker->boolean(30) ? $this->faker->paragraph() : null,

            'sort' => $this->faker->numberBetween(1, 10),

            'meta' => [
                'room_type' => $this->faker->randomElement(array_column(HotelRoomType::cases(), 'value')),
                'meal' => $this->faker->randomElement(['Breakfast', 'Half Board', 'Full Board', 'All Inclusive']),
            ],
        ];
    }

    /**
     * Set the check-in and check-out dates.
     *
     * @param  \DateTime|\Carbon\Carbon|string  $checkIn
     * @param  \DateTime|\Carbon\Carbon|string|null  $checkOut
     * @return static
     */
    public function dates($checkIn, $checkOut = null)
    {
        return $this->state(function (array $attributes) use ($checkIn, $checkOut) {
            $in = $checkIn instanceof \DateTime ? $checkIn : new \DateTime($checkIn);

            if ($checkOut === null) {
                $out = (clone $in)->modify('+3 days');
            } else {
                $out = $checkOut instanceof \DateTime ? $checkOut : new \DateTime($checkOut);
            }

            return [
                'check_in' => $in,
                'check_out' => $out,
            ];
        });
    }

    /**
     * Set the room counts.
     *
     * @return static
     */
    public function rooms(int $single = 0, int $double = 0, int $triple = 0, int $quad = 0, int $quint = 0)
    {
        return $this->state(function (array $attributes) use ($single, $double, $triple, $quad, $quint) {
            return [
                'room_single_count' => $single,
                'room_double_count' => $double,
                'room_triple_count' => $triple,
                'room_quad_count' => $quad,
                'room_quint_count' => $quint,
            ];
        });
    }

    /**
     * Set the confirmation status.
     *
     * @return static
     */
    public function confirmed(bool $confirmed = true, ?string $confirmationNumber = null)
    {
        return $this->state(function (array $attributes) use ($confirmed, $confirmationNumber) {
            return [
                'is_confirmed' => $confirmed,
                'confirmation_number' => $confirmationNumber ?? ($confirmed ? $this->faker->bothify('??####') : null),
            ];
        });
    }

    /**
     * Set the room type.
     *
     * @return static
     */
    public function roomType(HotelRoomType $roomType)
    {
        return $this->state(function (array $attributes) use ($roomType) {
            $meta = $attributes['meta'] ?? [];
            $meta['room_type'] = $roomType->value;

            return [
                'meta' => $meta,
            ];
        });
    }

    /**
     * Set the meal plan.
     *
     * @return static
     */
    public function meal(string $meal)
    {
        return $this->state(function (array $attributes) use ($meal) {
            $meta = $attributes['meta'] ?? [];
            $meta['meal'] = $meal;

            return [
                'meta' => $meta,
            ];
        });
    }

    /**
     * Associate with a broker.
     *
     * @return static
     */
    public function withBroker()
    {
        return $this->state(function (array $attributes) {
            // Create a hotel broker if none exists
            $broker = Vendor::where('vendor_type', VendorType::Hotel)->inRandomOrder()->first();

            if (! $broker) {
                $broker = Vendor::create([
                    'vendor_type' => VendorType::Hotel,
                    'company_name' => $this->faker->company() . ' Hotels',
                    'contact_name' => $this->faker->name(),
                    'contact_email' => $this->faker->safeEmail(),
                    'contact_phone' => $this->faker->phoneNumber(),
                ]);
            }

            return [
                'broker_id' => $broker->id,
            ];
        });
    }
}
