<?php

namespace Database\Factories;

use App\Models\Group;
use App\Models\Itinerary;
use App\Models\Vendors\SnackHandler;
use Illuminate\Database\Eloquent\Factories\Factory;

class ItineraryFactory extends Factory
{
    protected $model = Itinerary::class;

    public function definition(): array
    {
        return [
            'group_id' => Group::factory(),
            'date' => $this->faker->dateTimeBetween('now', '+3 months'),
            'city' => $this->faker->randomElement(['Makkah', 'Madinah']),
            'location' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(2),
            'is_arrival' => $this->faker->boolean(20),
            'has_snack' => $this->faker->boolean(50),
            'snack_details' => $this->faker->sentence(4),
            'snack_handler_id' => null,
            'sort' => $this->faker->numberBetween(1, 100),
        ];
    }

    public function withSnack(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'has_snack' => true,
                'snack_handler_id' => SnackHandler::factory(),
            ];
        });
    }

    public function withoutSnack(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'has_snack' => false,
                'snack_handler_id' => null,
            ];
        });
    }
}
