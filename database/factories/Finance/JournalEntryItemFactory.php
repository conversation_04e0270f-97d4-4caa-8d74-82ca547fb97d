<?php

namespace Database\Factories\Finance;

use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntry;
use App\Models\Finance\JournalEntryItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\JournalEntryItem>
 */
class JournalEntryItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JournalEntryItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'entry_id' => JournalEntry::factory(),
            'account_id' => CashAccount::factory(),
            'type' => fake()->randomElement(['d', 'c']),
            'amount' => fake()->randomFloat(2, 10, 1000),
            'owner_type' => null,
            'owner_id' => null,
        ];
    }

    /**
     * Configure the factory to use an existing journal entry.
     *
     * @param  \App\Models\Finance\JournalEntry|int  $entry
     * @return static
     */
    public function forEntry($entry)
    {
        return $this->state(function (array $attributes) use ($entry) {
            return [
                'entry_id' => $entry instanceof JournalEntry ? $entry->id : $entry,
            ];
        });
    }

    /**
     * Configure the factory to use an existing cash account.
     *
     * @param  \App\Models\Finance\CashAccount|int  $account
     * @return static
     */
    public function forAccount($account)
    {
        return $this->state(function (array $attributes) use ($account) {
            return [
                'account_id' => $account instanceof CashAccount ? $account->id : $account,
            ];
        });
    }

    /**
     * Configure the factory to create a debit item.
     *
     * @return static
     */
    public function debit()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'd',
            ];
        });
    }

    /**
     * Configure the factory to create a credit item.
     *
     * @return static
     */
    public function credit()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'c',
            ];
        });
    }

    /**
     * Configure the factory to create an item with a specific amount.
     *
     * @return static
     */
    public function amount(float $amount)
    {
        return $this->state(function (array $attributes) use ($amount) {
            return [
                'amount' => $amount,
            ];
        });
    }

    /**
     * Configure the factory to associate with a specific owner.
     *
     * @return static
     */
    public function forOwner(string $ownerType, int $ownerId)
    {
        return $this->state(function (array $attributes) use ($ownerType, $ownerId) {
            return [
                'owner_type' => $ownerType,
                'owner_id' => $ownerId,
            ];
        });
    }
}
