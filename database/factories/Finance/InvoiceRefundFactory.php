<?php

namespace Database\Factories\Finance;

use App\Enums\PaymentMethod;
use App\Models\Finance\CashAccount;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoiceRefund;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\InvoiceRefund>
 */
class InvoiceRefundFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = InvoiceRefund::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'invoice_id' => Invoice::factory(),
            'payment_method' => fake()->randomElement(PaymentMethod::cases()),
            'cash_account_id' => null, // Will be set automatically in the model's boot method
            'customer_cash_id' => null, // Will be set automatically in the model's boot method
            'refunded_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'amount' => fake()->randomFloat(2, 50, 1000),
            'currency_code' => fake()->randomElement(['SAR', 'IDR']),
            'exchange_rate' => fake()->randomFloat(2, 0.5, 1.5),
            'description' => fake()->sentence(),
            'attachment' => null,
        ];
    }

    /**
     * Configure the factory to use an existing invoice.
     *
     * @param  \App\Models\Finance\Invoice|int  $invoice
     * @return static
     */
    public function forInvoice($invoice)
    {
        return $this->state(function (array $attributes) use ($invoice) {
            $invoiceId = $invoice instanceof Invoice ? $invoice->id : $invoice;
            $invoiceModel = $invoice instanceof Invoice ? $invoice : Invoice::find($invoiceId);

            return [
                'invoice_id' => $invoiceId,
                'currency_code' => $invoiceModel ? $invoiceModel->currency_code : $attributes['currency_code'],
                'exchange_rate' => $invoiceModel ? $invoiceModel->exchange_rate : $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to use a specific payment method.
     *
     * @return static
     */
    public function method(PaymentMethod $method)
    {
        return $this->state(function (array $attributes) use ($method) {
            return [
                'payment_method' => $method,
            ];
        });
    }

    /**
     * Configure the factory to use a specific cash account.
     *
     * @param  \App\Models\Finance\CashAccount|int  $cashAccount
     * @return static
     */
    public function cashAccount($cashAccount)
    {
        return $this->state(function (array $attributes) use ($cashAccount) {
            return [
                'cash_account_id' => $cashAccount instanceof CashAccount ? $cashAccount->id : $cashAccount,
            ];
        });
    }

    /**
     * Configure the factory to create a refund with a specific amount.
     *
     * @return static
     */
    public function amount(float $amount)
    {
        return $this->state(function (array $attributes) use ($amount) {
            return [
                'amount' => $amount,
            ];
        });
    }

    /**
     * Configure the factory to create a refund with a specific currency.
     *
     * @return static
     */
    public function currency(string $currencyCode, ?float $exchangeRate = null)
    {
        return $this->state(function (array $attributes) use ($currencyCode, $exchangeRate) {
            return [
                'currency_code' => $currencyCode,
                'exchange_rate' => $exchangeRate ?? $attributes['exchange_rate'],
            ];
        });
    }

    /**
     * Configure the factory to create a refund with a specific date.
     *
     * @param  \DateTime|\DateTimeImmutable|string  $date
     * @return static
     */
    public function refundedAt($date)
    {
        return $this->state(function (array $attributes) use ($date) {
            return [
                'refunded_at' => $date,
            ];
        });
    }

    /**
     * Configure the factory to create a refund for a percentage of the invoice's paid amount.
     *
     * @param  float  $percentage  Percentage as a decimal (e.g., 0.5 for 50%)
     * @return static
     */
    public function percentageOfPaid(float $percentage)
    {
        return $this->state(function (array $attributes) use ($percentage) {
            $invoice = Invoice::find($attributes['invoice_id']);

            if (! $invoice || $invoice->paid <= 0) {
                return $attributes;
            }

            return [
                'amount' => $invoice->paid * $percentage,
            ];
        });
    }
}
