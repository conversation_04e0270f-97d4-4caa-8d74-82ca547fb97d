<?php

namespace Database\Factories\Finance;

use App\Models\Finance\JournalEntry;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\JournalEntry>
 */
class JournalEntryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = JournalEntry::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'transaction_type' => null,
            'transaction_id' => null,
            'entry_date' => fake()->dateTimeBetween('-1 month', 'now'),
            'details' => fake()->sentence(),
        ];
    }

    /**
     * Configure the factory to associate with a specific transaction.
     *
     * @return static
     */
    public function forTransaction(string $transactionType, int $transactionId)
    {
        return $this->state(function (array $attributes) use ($transactionType, $transactionId) {
            return [
                'transaction_type' => $transactionType,
                'transaction_id' => $transactionId,
            ];
        });
    }

    /**
     * Configure the factory to create a journal entry with items.
     *
     * @param  array  $items  Array of item data with keys: type, account_id, amount
     * @return static
     */
    public function withItems(array $items)
    {
        return $this->afterCreating(function (JournalEntry $journalEntry) use ($items) {
            foreach ($items as $item) {
                $journalEntry->items()->create($item);
            }
        });
    }

    /**
     * Configure the factory to create a balanced journal entry with debit and credit items.
     *
     * @return static
     */
    public function balanced(int $debitAccountId, int $creditAccountId, float $amount)
    {
        return $this->withItems([
            [
                'type' => 'd',
                'account_id' => $debitAccountId,
                'amount' => $amount,
            ],
            [
                'type' => 'c',
                'account_id' => $creditAccountId,
                'amount' => $amount,
            ],
        ]);
    }
}
