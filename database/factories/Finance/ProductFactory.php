<?php

namespace Database\Factories\Finance;

use App\Models\Finance\Product;
use App\Models\Finance\ProductCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $cost = fake()->randomFloat(2, 10, 1000);

        return [
            'category_id' => ProductCategory::factory(),
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(1),
            'cost' => $cost,
            'unit_price' => round($cost * fake()->randomFloat(1, 1.1, 1.5), 2), // Add a markup between 10% and 50%
        ];
    }

    /**
     * Configure the factory to use an existing category.
     *
     * @param  \App\Models\Finance\ProductCategory|int  $category
     * @return static
     */
    public function forCategory($category)
    {
        return $this->state(function (array $attributes) use ($category) {
            return [
                'category_id' => $category instanceof ProductCategory ? $category->id : $category,
            ];
        });
    }

    /**
     * Configure the factory to create a product with a specific cost.
     *
     * @return static
     */
    public function cost(float $cost)
    {
        return $this->state(function (array $attributes) use ($cost) {
            return [
                'cost' => $cost,
                'unit_price' => $cost, // Default unit_price to cost
            ];
        });
    }

    /**
     * Configure the factory to create a product with a specific unit price.
     *
     * @return static
     */
    public function unitPrice(float $unitPrice)
    {
        return $this->state(function (array $attributes) use ($unitPrice) {
            return [
                'unit_price' => $unitPrice,
            ];
        });
    }

    /**
     * Configure the factory to create a product with a specific markup percentage.
     *
     * @param  float  $markupPercentage  Percentage as a decimal (e.g., 0.2 for 20%)
     * @return static
     */
    public function markup(float $markupPercentage)
    {
        return $this->state(function (array $attributes) use ($markupPercentage) {
            return [
                'unit_price' => $attributes['cost'] * (1 + $markupPercentage),
            ];
        });
    }
}
