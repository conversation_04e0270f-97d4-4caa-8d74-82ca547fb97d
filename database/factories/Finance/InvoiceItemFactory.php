<?php

namespace Database\Factories\Finance;

use App\Models\Finance\Invoice;
use App\Models\Finance\InvoiceItem;
use App\Models\Finance\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Finance\InvoiceItem>
 */
class InvoiceItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = InvoiceItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $unitPrice = fake()->randomFloat(2, 10, 1000);

        return [
            'invoice_id' => Invoice::factory(),
            'product_id' => null, // Will be set if withProduct() is called
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(1),
            'quantity' => fake()->numberBetween(1, 10),
            'unit_price' => $unitPrice,
            'order_column' => 0, // Will be automatically set by SortableTrait
        ];
    }

    /**
     * Configure the factory to use an existing invoice.
     *
     * @param  \App\Models\Finance\Invoice|int  $invoice
     * @return static
     */
    public function forInvoice($invoice)
    {
        return $this->state(function (array $attributes) use ($invoice) {
            return [
                'invoice_id' => $invoice instanceof Invoice ? $invoice->id : $invoice,
            ];
        });
    }

    /**
     * Configure the factory to use an existing product.
     *
     * @param  \App\Models\Finance\Product|int  $product
     * @return static
     */
    public function forProduct($product)
    {
        return $this->state(function (array $attributes) use ($product) {
            $productId = $product instanceof Product ? $product->id : $product;
            $productModel = $product instanceof Product ? $product : Product::find($productId);

            return [
                'product_id' => $productId,
                'name' => $productModel ? $productModel->name : $attributes['name'],
                'description' => $productModel ? $productModel->description : $attributes['description'],
                'unit_price' => $productModel ? $productModel->unit_price : $attributes['unit_price'],
            ];
        });
    }

    /**
     * Configure the factory to create a new product and use it.
     *
     * @return static
     */
    public function withProduct()
    {
        return $this->state(function (array $attributes) {
            $product = Product::factory()->create();

            return [
                'product_id' => $product->id,
                'name' => $product->name,
                'description' => $product->description,
                'unit_price' => $product->unit_price,
            ];
        });
    }

    /**
     * Configure the factory to create an item with a specific quantity.
     *
     * @return static
     */
    public function quantity(int $quantity)
    {
        return $this->state(function (array $attributes) use ($quantity) {
            return [
                'quantity' => $quantity,
            ];
        });
    }
}
