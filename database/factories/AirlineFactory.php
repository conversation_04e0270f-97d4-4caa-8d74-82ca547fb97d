<?php

namespace Database\Factories;

use App\Models\Airline;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Airline>
 */
class AirlineFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => fake()->company() . ' Airlines',
        ];
    }

    /**
     * Create an airline with flights.
     *
     * @param int $count
     * @return static
     */
    public function withFlights(int $count = 1)
    {
        return $this->afterCreating(function (Airline $airline) use ($count) {
            \App\Models\GroupFlight::factory()
                ->count($count)
                ->create(['airline_id' => $airline->id]);
        });
    }

    /**
     * Create an airline with a specific name.
     *
     * @param string $name
     * @return static
     */
    public function named(string $name)
    {
        return $this->state(function (array $attributes) use ($name) {
            return [
                'name' => $name,
            ];
        });
    }

    /**
     * Create common airlines.
     *
     * @return static
     */
    public function common()
    {
        return $this->state(function (array $attributes) {
            return [
                'name' => fake()->randomElement([
                    'Saudi Arabian Airlines',
                    'Emirates',
                    'Qatar Airways',
                    'Etihad Airways',
                    'Turkish Airlines',
                    'Garuda Indonesia',
                    'Malaysia Airlines',
                    'Singapore Airlines',
                ]),
            ];
        });
    }
}