<?php

namespace Database\Factories;

use App\Models\Period;
use GeniusTS\HijriDate\Hijri;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Period>
 */
class PeriodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $year = $this->faker->numberBetween(1443, 1450);
        $startDate = now()->startOfYear();
        $endDate = now()->endOfYear();

        return [
            'name' => $year . ' H',
            'date_start' => $startDate,
            'date_end' => $endDate,
        ];
    }

    /**
     * Set this period as the current active period.
     *
     * @return static
     */
    public function active()
    {
        return $this->afterCreating(function (Period $period) {
            app(\App\Settings\GeneralSettings::class)->period_id = $period->id;
            app(\App\Settings\GeneralSettings::class)->save();
        });
    }

    /**
     * Create a period for the current year.
     *
     * @return static
     */
    public function currentYear()
    {
        return $this->state(function (array $attributes) {
            $currentYear = Hijri::convertToHijri(today())->year;

            return [
                'name' => $currentYear . ' H',
                'date_start' => Hijri::convertToGregorian(1, 1, $currentYear)->format('Y-m-d'),
                'date_end' => Hijri::convertToGregorian(30, 12, $currentYear)->format('Y-m-d'),
            ];
        });
    }

    /**
     * Create a period with specific date range.
     *
     * @param  \DateTime|\Carbon\Carbon|string  $startDate
     * @param  \DateTime|\Carbon\Carbon|string  $endDate
     * @return static
     */
    public function dateRange($startDate, $endDate)
    {
        return $this->state(function (array $attributes) use ($startDate, $endDate) {
            $start = $startDate instanceof \DateTime ? $startDate : new \DateTime($startDate);
            $end = $endDate instanceof \DateTime ? $endDate : new \DateTime($endDate);

            $year = $start->format('Y');

            return [
                'name' => $year . ' H',
                'date_start' => $start,
                'date_end' => $end,
            ];
        });
    }
}
