<?php

namespace Database\Factories;

use App\Models\GroupFlight;
use App\Models\Itinerary;
use App\Models\Reminder;
use App\Models\ReminderSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;

class ReminderScheduleFactory extends Factory
{
    protected $model = ReminderSchedule::class;

    public function definition(): array
    {
        // $reminder = Reminder::factory()->create();
        // $modelClass = $reminder->reminder_type === 'Departure'
        //     ? GroupFlight::class
        //     : Itinerary::class;

        // $model = $modelClass::factory()->create();
        // $modelMorphAlias = Relation::getMorphAlias($modelClass);

        return [
            // 'reminder_id' => $reminder->id,
            // 'model_type' => $modelMorphAlias,
            // 'model_id' => $model->id,
            'reminder_id' => Reminder::factory(),
            'scheduled_at' => $this->faker->dateTimeBetween('now', '+7 days'),
            'sent_at' => null,
        ];
    }

    public function sent(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'sent_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            ];
        });
    }

    public function forReminder(Reminder $reminder, ?Model $model = null): self
    {
        return $this->state(function (array $attributes) use ($reminder, $model) {
            $modelClass = $reminder->reminder_type === 'Departure'
                ? GroupFlight::class
                : Itinerary::class;

            $model ??= $modelClass::factory()->create();
            $modelMorphAlias = Relation::getMorphAlias($modelClass);

            return [
                'reminder_id' => $reminder->id,
                'model_type' => $modelMorphAlias,
                'model_id' => $model->id,
            ];
        });
    }

    public function forDeparture(): self
    {
        return $this->state(function (array $attributes) {
            $reminder = Reminder::factory()->departure()->create();
            $model = GroupFlight::factory()->create();
            $modelMorphAlias = Relation::getMorphAlias(GroupFlight::class);

            return [
                'reminder_id' => $reminder->id,
                'model_type' => $modelMorphAlias,
                'model_id' => $model->id,
            ];
        });
    }

    public function forSnackReminder(): self
    {
        return $this->state(function (array $attributes) {
            $reminder = Reminder::factory()->snackReminder()->create();
            $model = Itinerary::factory()->withSnack()->create();
            $modelMorphAlias = Relation::getMorphAlias(Itinerary::class);

            return [
                'reminder_id' => $reminder->id,
                'model_type' => $modelMorphAlias,
                'model_id' => $model->id,
            ];
        });
    }

    public function scheduled(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'scheduled_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
                'sent_at' => null,
            ];
        });
    }
}
