<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Group;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Umrahservice\Groups\Enums\GroupProgress;
use Umrahservice\Groups\Enums\GroupStatus;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Group>
 */
class GroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $arrivalDate = now()->addDays($this->faker->numberBetween(10, 60));
        $departureDate = (clone $arrivalDate)->addDays($this->faker->numberBetween(5, 15));
        $totalPax = $this->faker->numberBetween(10, 50);
        
        return [
            'token' => $this->faker->uuid(),
            'customer_id' => Customer::factory(),
            'name' => $this->faker->company() . ' Group',
            'number' => $this->faker->unique()->numerify('GRP-####'),
            'arrival_date' => $arrivalDate,
            'departure_date' => $departureDate,
            'total_pax' => $totalPax,
            'status' => GroupStatus::Draft,
            'progress' => GroupProgress::Waiting,
            'created_by_id' => User::factory(),
            'updated_by_id' => null,
            'services' => json_encode([]),
            'meta' => json_encode([]),
        ];
    }

    /**
     * Set the group status to confirmed.
     *
     * @return static
     */
    public function confirmed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => GroupStatus::Confirmed,
            ];
        });
    }

    /**
     * Set the group status to cancelled.
     *
     * @return static
     */
    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => GroupStatus::Cancelled,
            ];
        });
    }

    /**
     * Set the group progress.
     *
     * @param GroupProgress $progress
     * @return static
     */
    public function progress(GroupProgress $progress)
    {
        return $this->state(function (array $attributes) use ($progress) {
            return [
                'progress' => $progress,
            ];
        });
    }

    /**
     * Set the arrival and departure dates.
     *
     * @param \DateTime|\Carbon\Carbon|string $arrivalDate
     * @param \DateTime|\Carbon\Carbon|string|null $departureDate
     * @return static
     */
    public function dates($arrivalDate, $departureDate = null)
    {
        return $this->state(function (array $attributes) use ($arrivalDate, $departureDate) {
            $arrival = $arrivalDate instanceof \DateTime ? $arrivalDate : new \DateTime($arrivalDate);
            
            if ($departureDate === null) {
                $departure = (clone $arrival)->modify('+7 days');
            } else {
                $departure = $departureDate instanceof \DateTime ? $departureDate : new \DateTime($departureDate);
            }
            
            return [
                'arrival_date' => $arrival,
                'departure_date' => $departure,
                'name' => $arrival->format('j F Y'),
            ];
        });
    }

    /**
     * Set the total number of pilgrims.
     *
     * @param int $count
     * @return static
     */
    public function pilgrims(int $count)
    {
        return $this->state(function (array $attributes) use ($count) {
            return [
                'total_pax' => $count,
            ];
        });
    }

    /**
     * Create a group with rooms generated.
     *
     * @return static
     */
    public function withRooms()
    {
        return $this->afterCreating(function (Group $group) {
            $group->generateRooms();
        });
    }
}