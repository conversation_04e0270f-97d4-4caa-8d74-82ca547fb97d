<?php

namespace Database\Factories;

use App\Models\Hotel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Hotel>
 */
class HotelFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $cities = array_keys(Hotel::CITIES);

        return [
            'name' => $this->faker->company() . ' Hotel',
            'city' => $this->faker->randomElement($cities),
            'stars' => $this->faker->numberBetween(3, 5),
            'distance' => $this->faker->numberBetween(100, 5000),
            'price_quad' => $this->faker->numberBetween(500, 2000),
            'price_triple' => $this->faker->numberBetween(400, 1800),
            'price_double' => $this->faker->numberBetween(300, 1500),
        ];
    }

    /**
     * Set the hotel's star rating.
     *
     * @return static
     */
    public function stars(int $stars)
    {
        return $this->state(function (array $attributes) use ($stars) {
            return [
                'stars' => $stars,
            ];
        });
    }

    /**
     * Set the hotel's distance from Haram/Masjid.
     *
     * @return static
     */
    public function distance(int $meters)
    {
        return $this->state(function (array $attributes) use ($meters) {
            return [
                'distance' => $meters,
            ];
        });
    }

    /**
     * Set the hotel's location to Makkah.
     *
     * @return static
     */
    public function inMakkah()
    {
        return $this->state(function (array $attributes) {
            return [
                'city' => 'Makkah',
            ];
        });
    }

    /**
     * Set the hotel's location to Madinah.
     *
     * @return static
     */
    public function inMadinah()
    {
        return $this->state(function (array $attributes) {
            return [
                'city' => 'Madinah',
            ];
        });
    }

    /**
     * Set custom prices for all room types.
     *
     * @return static
     */
    public function prices(int $double, int $triple, int $quad)
    {
        return $this->state(function (array $attributes) use ($double, $triple, $quad) {
            return [
                'price_double' => $double,
                'price_triple' => $triple,
                'price_quad' => $quad,
            ];
        });
    }
}
