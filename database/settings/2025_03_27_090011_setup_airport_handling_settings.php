<?php

use App\Models\Airport;
use App\Models\GroupFlight;
use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $handlingRates = Airport::query()
            ->where('country', 'Saudi Arabia')
            ->orderBy('code')
            ->get()
            ->map(fn ($a) => [
                'airport_code' => $a->code,
                'arrival' => 0,
                'departure' => 0,
            ])
            ->toArray();

        GroupFlight::query()
            ->whereJsonContainsKey('meta->mealbox')
            ->get()
            ->each(function (GroupFlight $flight) {
                $meals = str($flight->mealbox)
                    ->replace('Zam zam', 'Zamzam')
                    ->replace('Nasi box', 'Nasi Box')
                    ->replace('Zamzan', 'Zamzam')
                    ->replace('AL BAIK', 'Al Baik')
                    ->split('/(,|&|\+)/')
                    ->map(fn ($value) => trim($value))
                    ->filter(fn ($m) => ! blank($m) && $m !== '-')
                    ->toArray();

                $flight->update(['meta' => [
                    ...$flight->meta,
                    'mealbox' => $meals,
                ]]);
            });

        $mealPrices = GroupFlight::query()
            ->whereJsonContainsKey('meta->mealbox')
            ->get()
            ->map(fn (GroupFlight $flight) => $flight->mealbox)
            ->flatten()
            ->unique()
            ->mapWithKeys(fn ($m) => [$m => 0])
            ->toArray();

        $this->migrator->add('airport_handling.handling_rates', $handlingRates);
        $this->migrator->add('airport_handling.meal_prices', $mealPrices);
    }
};
