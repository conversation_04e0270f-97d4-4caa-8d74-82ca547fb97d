<?php

use App\Models\Itinerary;
use App\Models\Vendors\SnackHandler;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $makkahSnackHandler = SnackHandler::query()
                ->where('meta->city', 'Makkah')
                ->first();

            Itinerary::query()
                ->where('has_snack', true)
                ->where('city', 'Makkah')
                ->whereNull('snack_handler_id')
                ->get()
                ->each(function ($i) use ($makkahSnackHandler) {
                    $i->update(['snack_handler_id' => $makkahSnackHandler->id]);
                });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            //
        });
    }
};
