<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_vouchers', function (Blueprint $table) {
            $table->id();

            // Voucher identification
            $table->string('voucher_no')->unique();

            // Payment information
            $table->datetime('paid_at');
            $table->foreignId('paid_to_id')->constrained('users');

            // Amount and currency
            $table->decimal('amount', 15, 2);
            $table->char('currency_code', 3)->default('SAR');
            $table->double('exchange_rate')->unsigned()->default(1);

            // Payment method and details
            $table->string('payment_method')->default('cash');
            $table->string('check_no')->nullable();

            // Description
            $table->text('description')->nullable();

            // Approval workflow
            $table->foreignId('created_by_id')->nullable()->constrained('users');
            $table->foreignId('checked_by_id')->nullable()->constrained('users');
            $table->foreignId('approved_by_id')->nullable()->constrained('users');

            $table->timestamps();

            // Indexes
            $table->index('voucher_no');
            $table->index('paid_at');
            $table->index('payment_method');
            $table->index(['currency_code', 'paid_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_vouchers');
    }
};
