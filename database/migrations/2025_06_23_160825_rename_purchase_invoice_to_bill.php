<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('activity_log')
            ->where('subject_type', 'purchase_invoice')
            ->update(['subject_type' => 'bill']);
        DB::table('journal_entries')
            ->where('transaction_type', 'purchase_invoice')
            ->update([
                'transaction_type' => 'bill',
                'details' => DB::raw("REPLACE(`details`, 'Purchase Invoice PI', 'Bill BI')"),
            ]);
        DB::table('journal_entries')
            ->where('transaction_type', 'purchase_invoice_payment')
            ->update([
                'transaction_type' => 'bill_payment',
                'details' => DB::raw("REPLACE(`details`, 'Purchase Payment PI', 'Bill Payment BI')"),
            ]);

        Schema::table('purchase_invoice_items', function (Blueprint $table) {
            $table->dropForeign('purchase_invoice_items_invoice_id_foreign');
            $table->renameColumn('invoice_id', 'bill_id');
        });
        Schema::rename('purchase_invoice_items', 'bill_items');

        Schema::table('purchase_invoice_payments', function (Blueprint $table) {
            $table->dropForeign('purchase_invoice_payments_invoice_id_foreign');
            $table->renameColumn('invoice_id', 'bill_id');
        });
        Schema::rename('purchase_invoice_payments', 'bill_payments');

        DB::table('purchase_invoices')
            ->update([
                'invoice_number' => DB::raw("REPLACE(`invoice_number`, 'PI-', 'BI-')"),
            ]);
        Schema::table('purchase_invoices', function (Blueprint $table) {
            $table->renameColumn('invoice_date', 'bill_date');
            $table->renameColumn('invoice_number', 'bill_number');
        });
        Schema::rename('purchase_invoices', 'bills');

        DB::table('user_cashes')
            ->where('related_type', 'purchase_invoice')
            ->update(['related_type' => 'bill']);

        Schema::table('bill_items', function (Blueprint $table) {
            $table->foreign('bill_id')
                ->references('id')
                ->on('bills')
                ->cascadeOnDelete();
        });
        Schema::table('bill_payments', function (Blueprint $table) {
            $table->foreign('bill_id')
                ->references('id')
                ->on('bills')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
