<?php

use App\Enums\UserRole;
use App\Models\User;
use App\Models\Vendors\SnackHandler;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->json('meta')->nullable()->after('contact_phone');
        });

        Schema::table('itineraries', function (Blueprint $table) {
            $table->foreignId('snack_handler_id')->nullable()->after('snack_details')->constrained('vendors')->nullOnDelete();
        });

        if (Role::query()->where('name', UserRole::SnackHandler->value)->exists()) {
            User::query()->role(UserRole::SnackHandler->value)
                ->get()
                ->each(function ($user) {
                    $vendor = SnackHandler::query()
                        ->updateOrCreate([
                            'company_name' => $user->name,
                        ], [
                            'contact_name' => $user->name,
                            'contact_email' => $user->email,
                            'contact_phone' => $user->phone,
                            'meta' => $user->meta,
                        ]);

                    $vendor->users()->sync([$user->id]);
                });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            //
        });
    }
};
