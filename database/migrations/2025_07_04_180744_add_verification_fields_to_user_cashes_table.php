<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_cashes', function (Blueprint $table) {
            $table->dateTime('verified_at')->nullable();
            $table->foreignIdFor(User::class, 'verified_by_id')->nullable()->constrained()->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_cashes', function (Blueprint $table) {
            $table->dropColumn('verified_at');
            $table->dropColumn('verified_by_id');
        });
    }
};
