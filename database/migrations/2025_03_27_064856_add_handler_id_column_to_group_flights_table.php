<?php

use App\Models\Group;
use App\Models\GroupFlight;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('group_flights', function (Blueprint $table) {
            $table->unsignedBigInteger('handler_id')->nullable()->after('via_etd');
        });

        Group::query()
            ->whereJsonContainsKey('meta->airport_handlers_arr')
            ->orWhereJsonContainsKey('meta->airport_handlers_dep')
            ->get()
            ->each(function (Group $group) {
                $meta = $group->meta;

                if (filled($meta['airport_handlers_arr'] ?? [])) {
                    $handlerIds = $meta['airport_handlers_arr'];
                    $group->flights()->where('type', 'arrival')->get()
                        ->each(function (GroupFlight $flight, $index) use ($handlerIds) {
                            $flight->update(['handler_id' => $handlerIds[$index] ?? $handlerIds[0]]);
                        });
                    unset($meta['airport_handlers_arr']);
                }
                if (filled($meta['airport_handlers_dep'] ?? [])) {
                    $handlerIds = $meta['airport_handlers_dep'];
                    $group->flights()->where('type', 'departure')->get()
                        ->each(function (GroupFlight $flight, $index) use ($handlerIds) {
                            $flight->update(['handler_id' => $handlerIds[$index] ?? $handlerIds[0]]);
                        });
                    unset($meta['airport_handlers_dep']);
                }
                $group->update(['meta' => $meta]);
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('group_flights', function (Blueprint $table) {
            $table->dropColumn('handler_id');
        });
    }
};
