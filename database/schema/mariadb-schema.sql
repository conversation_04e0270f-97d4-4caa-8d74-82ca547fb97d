/*M!999999\- enable the sandbox mode */ 
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*M!100616 SET @OLD_NOTE_VERBOSITY=@@NOTE_VERBOSITY, NOTE_VERBOSITY=0 */;
DROP TABLE IF EXISTS `activity_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `log_name` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `subject_type` varchar(255) DEFAULT NULL,
  `event` varchar(255) DEFAULT NULL,
  `subject_id` bigint(20) unsigned DEFAULT NULL,
  `causer_type` varchar(255) DEFAULT NULL,
  `causer_id` bigint(20) unsigned DEFAULT NULL,
  `properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`properties`)),
  `batch_uuid` char(36) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `subject` (`subject_type`,`subject_id`),
  KEY `causer` (`causer_type`,`causer_id`),
  KEY `activity_log_log_name_index` (`log_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `agents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `agents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `airlines`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `airlines` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `airport_handler`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `airport_handler` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `airport_code` varchar(10) NOT NULL,
  `handler_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `airports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `airports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `city` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `code` varchar(10) DEFAULT NULL,
  `iata` char(3) DEFAULT NULL,
  `icao` char(4) DEFAULT NULL,
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `timezone` tinyint(4) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `balances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `balances` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cash_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cash_accounts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(128) DEFAULT NULL,
  `category` varchar(128) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_fixed` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cash_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cash_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) unsigned DEFAULT NULL,
  `group` varchar(255) DEFAULT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'out',
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `account_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cash_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cash_transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_date` date NOT NULL,
  `type` enum('in','out','transfer') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `amount_c` decimal(15,2) GENERATED ALWAYS AS (`amount` * `exchange_rate`) VIRTUAL,
  `currency_code` varchar(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `details` text DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `debit_account_id` bigint(20) unsigned NOT NULL,
  `credit_account_id` bigint(20) unsigned NOT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cash_transactions_debit_account_id_foreign` (`debit_account_id`),
  KEY `cash_transactions_credit_account_id_foreign` (`credit_account_id`),
  CONSTRAINT `cash_transactions_credit_account_id_foreign` FOREIGN KEY (`credit_account_id`) REFERENCES `cash_accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `cash_transactions_debit_account_id_foreign` FOREIGN KEY (`debit_account_id`) REFERENCES `cash_accounts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'general',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `customer_cashes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_cashes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL,
  `cashed_at` datetime NOT NULL DEFAULT current_timestamp(),
  `cash_account_id` bigint(20) unsigned DEFAULT NULL,
  `type` enum('d','c') NOT NULL DEFAULT 'd',
  `amount` decimal(15,2) NOT NULL,
  `currency` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `details` text DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `related_type` varchar(255) DEFAULT NULL,
  `related_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_cashes_related_type_related_id_index` (`related_type`,`related_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `customer_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `last_login_ip` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `customer_users_email_unique` (`email`),
  KEY `customer_users_customer_id_foreign` (`customer_id`),
  CONSTRAINT `customer_users_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `owner_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `logo_square` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `agent_id` bigint(20) unsigned DEFAULT NULL,
  `permit_no` varchar(100) DEFAULT NULL,
  `region` varchar(100) DEFAULT NULL,
  `address` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customers_agent_id_foreign` (`agent_id`),
  CONSTRAINT `customers_agent_id_foreign` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `estimate_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `estimate_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `estimate_id` bigint(20) unsigned NOT NULL,
  `product_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `quantity` int(10) unsigned NOT NULL DEFAULT 1,
  `unit_price` decimal(15,2) NOT NULL,
  `order_column` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `estimate_items_estimate_id_foreign` (`estimate_id`),
  KEY `estimate_items_product_id_foreign` (`product_id`),
  CONSTRAINT `estimate_items_estimate_id_foreign` FOREIGN KEY (`estimate_id`) REFERENCES `estimates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `estimate_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `estimates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `estimates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `estimate_date` date NOT NULL,
  `estimate_number` varchar(255) NOT NULL,
  `customer_id` bigint(20) unsigned NOT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `package_id` bigint(20) unsigned DEFAULT NULL,
  `valid_until` date NOT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `subject` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `terms` text DEFAULT NULL,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `status` varchar(255) NOT NULL DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `invoice_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `estimates_estimate_number_unique` (`estimate_number`),
  KEY `estimates_customer_id_foreign` (`customer_id`),
  KEY `estimates_group_id_foreign` (`group_id`),
  KEY `estimates_invoice_id_foreign` (`invoice_id`),
  CONSTRAINT `estimates_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `estimates_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL,
  CONSTRAINT `estimates_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `exports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `exports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `completed_at` timestamp NULL DEFAULT NULL,
  `file_disk` varchar(255) NOT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `exporter` varchar(255) NOT NULL,
  `processed_rows` int(10) unsigned NOT NULL DEFAULT 0,
  `total_rows` int(10) unsigned NOT NULL,
  `successful_rows` int(10) unsigned NOT NULL DEFAULT 0,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `exports_user_id_foreign` (`user_id`),
  CONSTRAINT `exports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_import_rows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_import_rows` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`data`)),
  `import_id` bigint(20) unsigned NOT NULL,
  `validation_error` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `failed_import_rows_import_id_foreign` (`import_id`),
  CONSTRAINT `failed_import_rows_import_id_foreign` FOREIGN KEY (`import_id`) REFERENCES `imports` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `flights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `flights` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `airline_id` bigint(20) unsigned DEFAULT NULL,
  `number` varchar(10) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `from` varchar(10) DEFAULT NULL,
  `to` varchar(10) DEFAULT NULL,
  `etd` varchar(10) DEFAULT NULL,
  `eta` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `date_etd` datetime DEFAULT NULL,
  `date_eta` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `flights_airline_id_foreign` (`airline_id`),
  CONSTRAINT `flights_airline_id_foreign` FOREIGN KEY (`airline_id`) REFERENCES `airlines` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `group_cashes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_cashes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `division` varchar(32) NOT NULL,
  `cashed_at` datetime NOT NULL,
  `category_id` bigint(20) unsigned DEFAULT NULL,
  `cash_in` decimal(15,2) DEFAULT NULL,
  `cash_in_c` decimal(15,2) GENERATED ALWAYS AS (`cash_in` / `exchange_rate`) VIRTUAL,
  `cash_out` decimal(15,2) DEFAULT NULL,
  `cash_out_c` decimal(15,2) GENERATED ALWAYS AS (`cash_out` / `exchange_rate`) VIRTUAL,
  `currency` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `description` varchar(255) DEFAULT NULL,
  `is_excluded` tinyint(1) NOT NULL DEFAULT 0,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `hotel_broker_id` bigint(20) unsigned DEFAULT NULL,
  `institution_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_cashes_group_id_foreign` (`group_id`),
  KEY `group_cashes_user_id_foreign` (`user_id`),
  CONSTRAINT `group_cashes_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE,
  CONSTRAINT `group_cashes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `group_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) unsigned NOT NULL,
  `visa` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `ticket` varchar(255) DEFAULT NULL,
  `roomlist` varchar(255) DEFAULT NULL,
  `manifest` varchar(255) DEFAULT NULL,
  `files` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_visas_group_id_foreign` (`group_id`),
  CONSTRAINT `group_visas_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE,
  CONSTRAINT `group_data_chk_1` CHECK (json_valid(`files`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `group_flights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_flights` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `airline_id` bigint(20) unsigned DEFAULT NULL,
  `group_id` bigint(20) unsigned NOT NULL,
  `type` enum('arrival','departure') NOT NULL DEFAULT 'arrival',
  `flight_number` varchar(10) DEFAULT NULL,
  `pax` tinyint(3) unsigned DEFAULT NULL,
  `from` varchar(10) NOT NULL,
  `via` varchar(10) DEFAULT NULL,
  `to` varchar(10) NOT NULL,
  `date_etd` datetime NOT NULL,
  `date_eta` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  `via_number` varchar(10) DEFAULT NULL,
  `via_eta` datetime DEFAULT NULL,
  `via_etd` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `group_flights_airline_id_foreign` (`airline_id`),
  KEY `group_flights_group_id_foreign` (`group_id`),
  CONSTRAINT `group_flights_airline_id_foreign` FOREIGN KEY (`airline_id`) REFERENCES `airlines` (`id`) ON DELETE SET NULL,
  CONSTRAINT `group_flights_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `group_hotel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_hotel` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `hotel_id` bigint(20) unsigned NOT NULL,
  `check_in` date DEFAULT NULL,
  `check_out` date DEFAULT NULL,
  `handler_name` varchar(255) DEFAULT NULL,
  `handler_phone` varchar(255) DEFAULT NULL,
  `room_single_count` tinyint(4) NOT NULL DEFAULT 0,
  `room_double_count` tinyint(4) NOT NULL DEFAULT 0,
  `room_triple_count` tinyint(4) NOT NULL DEFAULT 0,
  `room_quad_count` tinyint(4) NOT NULL DEFAULT 0,
  `room_quint_count` tinyint(4) NOT NULL DEFAULT 0,
  `is_confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `broker_id` bigint(20) unsigned DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `confirmation_file` varchar(255) DEFAULT NULL,
  `more_info` text DEFAULT NULL,
  `sort` tinyint(4) NOT NULL DEFAULT 0,
  `confirmation_number` varchar(255) DEFAULT NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  PRIMARY KEY (`id`),
  KEY `group_hotel_hotel_id_foreign` (`hotel_id`),
  KEY `group_hotel_broker_id_foreign` (`broker_id`),
  KEY `group_hotel_group_id_foreign` (`group_id`),
  CONSTRAINT `group_hotel_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL,
  CONSTRAINT `group_hotel_hotel_id_foreign` FOREIGN KEY (`hotel_id`) REFERENCES `hotels` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `group_pilgrim`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_pilgrim` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `room_id` bigint(20) unsigned DEFAULT NULL,
  `group_id` bigint(20) unsigned NOT NULL,
  `pilgrim_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_tour_leader` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `group_vehicle`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_vehicle` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) unsigned NOT NULL,
  `vehicle_id` bigint(20) unsigned NOT NULL,
  `pax_count` int(10) unsigned NOT NULL DEFAULT 0,
  `count` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `groups` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `token` varchar(255) DEFAULT NULL,
  `invoice_number` varchar(20) DEFAULT NULL,
  `invoice_amount` decimal(10,2) DEFAULT NULL,
  `customer_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `number` varchar(128) DEFAULT NULL,
  `arrival_date` datetime DEFAULT NULL,
  `departure_date` datetime DEFAULT NULL,
  `total_pax` smallint(5) unsigned NOT NULL DEFAULT 0,
  `tour_leader_name` varchar(255) DEFAULT NULL,
  `tour_leader_phone` varchar(255) DEFAULT NULL,
  `muthowwif_name` varchar(255) DEFAULT NULL,
  `muthowwif_phone` varchar(255) DEFAULT NULL,
  `tour_leader_id` bigint(20) unsigned DEFAULT NULL,
  `flight_arr_id` bigint(20) unsigned DEFAULT NULL,
  `flight_dep_id` bigint(20) unsigned DEFAULT NULL,
  `airport_handler_arr_name` varchar(255) DEFAULT NULL,
  `airport_handler_arr_phone` varchar(255) DEFAULT NULL,
  `airport_handler_dep_name` varchar(255) DEFAULT NULL,
  `airport_handler_dep_phone` varchar(255) DEFAULT NULL,
  `room_double_count` smallint(5) unsigned NOT NULL DEFAULT 0,
  `room_triple_count` smallint(5) unsigned NOT NULL DEFAULT 0,
  `room_quad_count` smallint(5) unsigned NOT NULL DEFAULT 0,
  `transport_id` bigint(20) unsigned DEFAULT NULL,
  `transport_bus_count` smallint(5) unsigned NOT NULL DEFAULT 0,
  `transport_van_count` tinyint(4) NOT NULL DEFAULT 0,
  `transport_contact_phone` varchar(32) DEFAULT NULL,
  `institution_id` bigint(20) unsigned DEFAULT NULL,
  `institution_contact_name` varchar(255) DEFAULT NULL,
  `institution_contact_phone` varchar(32) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `transport_contact_name` varchar(255) DEFAULT NULL,
  `hotel_handlers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `services` varchar(255) DEFAULT NULL,
  `airport_handler_arr_id` bigint(20) unsigned DEFAULT NULL,
  `airport_handler_dep_id` bigint(20) unsigned DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'confirmed',
  `progress` varchar(255) NOT NULL DEFAULT 'waiting',
  `mutawif_id` bigint(20) unsigned DEFAULT NULL,
  `mutawif_2_id` bigint(20) unsigned DEFAULT NULL,
  `mutawif_3_id` bigint(20) unsigned DEFAULT NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  `meals` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meals`)),
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `groups_customer_id_foreign` (`customer_id`),
  KEY `groups_flight_arr_id_foreign` (`flight_arr_id`),
  KEY `groups_flight_dep_id_foreign` (`flight_dep_id`),
  KEY `groups_transport_id_foreign` (`transport_id`),
  KEY `groups_institution_id_foreign` (`institution_id`),
  KEY `groups_airport_handler_arr_id_foreign` (`airport_handler_arr_id`),
  KEY `groups_airport_handler_dep_id_foreign` (`airport_handler_dep_id`),
  CONSTRAINT `groups_airport_handler_arr_id_foreign` FOREIGN KEY (`airport_handler_arr_id`) REFERENCES `staff` (`id`) ON DELETE SET NULL,
  CONSTRAINT `groups_airport_handler_dep_id_foreign` FOREIGN KEY (`airport_handler_dep_id`) REFERENCES `staff` (`id`) ON DELETE SET NULL,
  CONSTRAINT `groups_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  CONSTRAINT `groups_flight_arr_id_foreign` FOREIGN KEY (`flight_arr_id`) REFERENCES `flights` (`id`),
  CONSTRAINT `groups_flight_dep_id_foreign` FOREIGN KEY (`flight_dep_id`) REFERENCES `flights` (`id`),
  CONSTRAINT `groups_institution_id_foreign` FOREIGN KEY (`institution_id`) REFERENCES `institutions` (`id`),
  CONSTRAINT `groups_transport_id_foreign` FOREIGN KEY (`transport_id`) REFERENCES `transports` (`id`),
  CONSTRAINT `groups_chk_1` CHECK (json_valid(`hotel_handlers`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `hotels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `hotels` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `city` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `stars` smallint(6) NOT NULL DEFAULT 0,
  `distance` smallint(6) NOT NULL DEFAULT 0,
  `price_quad` decimal(10,2) DEFAULT NULL,
  `price_triple` decimal(10,2) DEFAULT NULL,
  `price_double` decimal(10,2) DEFAULT NULL,
  `fullname` varchar(255) GENERATED ALWAYS AS (concat(`city`,' - ',`name`)) VIRTUAL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `imports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `imports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `completed_at` timestamp NULL DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `importer` varchar(255) NOT NULL,
  `processed_rows` int(10) unsigned NOT NULL DEFAULT 0,
  `total_rows` int(10) unsigned NOT NULL,
  `successful_rows` int(10) unsigned NOT NULL DEFAULT 0,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `imports_user_id_foreign` (`user_id`),
  CONSTRAINT `imports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `institutions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `institutions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invoice_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint(20) unsigned NOT NULL,
  `product_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `quantity` int(10) unsigned NOT NULL DEFAULT 1,
  `unit_price` decimal(15,2) NOT NULL,
  `order_column` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `invoice_items_invoice_id_foreign` (`invoice_id`),
  KEY `invoice_items_product_id_foreign` (`product_id`),
  CONSTRAINT `invoice_items_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `invoice_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invoice_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint(20) unsigned NOT NULL,
  `payment_method` enum('cash','bank','deposit') NOT NULL DEFAULT 'cash',
  `cash_account_id` bigint(20) unsigned DEFAULT NULL,
  `customer_cash_id` bigint(20) unsigned DEFAULT NULL,
  `paid_at` datetime NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double unsigned NOT NULL DEFAULT 1,
  `description` varchar(255) DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `ref_id` bigint(20) unsigned DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invoice_refunds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice_refunds` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint(20) unsigned NOT NULL,
  `payment_method` enum('cash','bank','deposit') NOT NULL DEFAULT 'cash',
  `cash_account_id` bigint(20) unsigned DEFAULT NULL,
  `customer_cash_id` bigint(20) unsigned DEFAULT NULL,
  `refunded_at` date NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `description` varchar(255) DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `invoice_refunds_invoice_id_foreign` (`invoice_id`),
  CONSTRAINT `invoice_refunds_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_date` datetime NOT NULL,
  `invoice_number` varchar(255) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `customer_id` bigint(20) unsigned DEFAULT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `package_id` bigint(20) unsigned DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double unsigned NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `terms` text DEFAULT NULL,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `paid` decimal(15,2) NOT NULL DEFAULT 0.00,
  `status` varchar(255) NOT NULL DEFAULT 'unpaid',
  `cancellation_note` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `invoices_customer_id_foreign` (`customer_id`),
  KEY `invoices_group_id_foreign` (`group_id`),
  CONSTRAINT `invoices_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `invoices_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `itineraries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `itineraries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) unsigned NOT NULL,
  `date` datetime DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_arrival` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `sort` tinyint(4) NOT NULL DEFAULT 0,
  `has_snack` tinyint(1) NOT NULL DEFAULT 0,
  `snack_details` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `itineraries_group_id_foreign` (`group_id`),
  CONSTRAINT `itineraries_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `itinerary_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `itinerary_contacts` (
  `itinerary_id` bigint(20) unsigned NOT NULL,
  `contact_id` bigint(20) unsigned NOT NULL,
  KEY `itinerary_contacts_itinerary_id_foreign` (`itinerary_id`),
  KEY `itinerary_contacts_contact_id_foreign` (`contact_id`),
  CONSTRAINT `itinerary_contacts_contact_id_foreign` FOREIGN KEY (`contact_id`) REFERENCES `contacts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `itinerary_contacts_itinerary_id_foreign` FOREIGN KEY (`itinerary_id`) REFERENCES `itineraries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `journal_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `journal_entries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_type` varchar(255) DEFAULT NULL,
  `transaction_id` bigint(20) unsigned DEFAULT NULL,
  `entry_date` datetime NOT NULL DEFAULT current_timestamp(),
  `details` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `journal_entry_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `journal_entry_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `entry_id` bigint(20) unsigned NOT NULL,
  `account_id` bigint(20) unsigned NOT NULL,
  `type` char(1) NOT NULL DEFAULT 'c',
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `owner_type` varchar(255) DEFAULT NULL,
  `owner_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `journal_entry_items_entry_id_foreign` (`entry_id`),
  KEY `journal_entry_items_owner_type_owner_id_index` (`owner_type`,`owner_id`),
  CONSTRAINT `journal_entry_items_entry_id_foreign` FOREIGN KEY (`entry_id`) REFERENCES `journal_entries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `manasiks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `manasiks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `date` datetime NOT NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `sort` tinyint(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `manasiks_group_id_foreign` (`group_id`),
  CONSTRAINT `manasiks_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `media`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `media` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  `uuid` uuid DEFAULT NULL,
  `collection_name` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `mime_type` varchar(255) DEFAULT NULL,
  `disk` varchar(255) NOT NULL,
  `conversions_disk` varchar(255) DEFAULT NULL,
  `size` bigint(20) unsigned NOT NULL,
  `manipulations` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`manipulations`)),
  `custom_properties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`custom_properties`)),
  `generated_conversions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`generated_conversions`)),
  `responsive_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`responsive_images`)),
  `order_column` int(10) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `media_uuid_unique` (`uuid`),
  KEY `media_model_type_model_id_index` (`model_type`,`model_id`),
  KEY `media_order_column_index` (`order_column`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `metas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `metas` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `metable_type` varchar(255) NOT NULL,
  `metable_id` bigint(20) unsigned NOT NULL,
  `key` varchar(255) NOT NULL,
  `value` longtext NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `metas_metable_type_metable_id_key_unique` (`metable_type`,`metable_id`,`key`),
  KEY `metas_key_metable_type_index` (`key`,`metable_type`),
  KEY `metas_key_index` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) unsigned NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) unsigned NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `quantity` int(10) unsigned NOT NULL DEFAULT 1,
  `unit_price` decimal(15,2) NOT NULL,
  `order_column` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_items_order_id_foreign` (`order_id`),
  CONSTRAINT `order_items_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_date` datetime NOT NULL,
  `order_number` varchar(255) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `customer_id` bigint(20) unsigned DEFAULT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `status` enum('open','closed') NOT NULL DEFAULT 'open',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `orders_customer_id_foreign` (`customer_id`),
  KEY `orders_group_id_foreign` (`group_id`),
  CONSTRAINT `orders_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  CONSTRAINT `orders_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `periods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `periods` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `date_start` datetime NOT NULL,
  `date_end` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `pilgrims`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pilgrims` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `fullname` varchar(255) NOT NULL,
  `title` char(3) NOT NULL DEFAULT 'mr',
  `passport_number` varchar(255) DEFAULT NULL,
  `national_id` varchar(255) DEFAULT NULL,
  `gender` char(1) NOT NULL DEFAULT 'm',
  `birthplace` varchar(255) DEFAULT NULL,
  `birthdate` date DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `product_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `cost_account_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_categories_cost_account_id_foreign` (`cost_account_id`),
  CONSTRAINT `product_categories_cost_account_id_foreign` FOREIGN KEY (`cost_account_id`) REFERENCES `cash_accounts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `cost` decimal(15,2) DEFAULT NULL,
  `unit_price` decimal(15,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `products_category_id_foreign` (`category_id`),
  CONSTRAINT `products_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `purchase_invoice_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_invoice_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint(20) unsigned NOT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `product_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `quantity` int(10) unsigned NOT NULL DEFAULT 1,
  `unit_price` decimal(15,2) NOT NULL,
  `vat` double unsigned NOT NULL DEFAULT 0,
  `order_column` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `purchase_invoice_items_invoice_id_foreign` (`invoice_id`),
  KEY `purchase_invoice_items_product_id_foreign` (`product_id`),
  KEY `purchase_invoice_items_group_id_foreign` (`group_id`),
  CONSTRAINT `purchase_invoice_items_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL,
  CONSTRAINT `purchase_invoice_items_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `purchase_invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_invoice_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `purchase_invoice_payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_invoice_payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `cash_account_id` bigint(20) unsigned NOT NULL,
  `user_cash_id` bigint(20) unsigned DEFAULT NULL,
  `paid_at` datetime NOT NULL DEFAULT current_timestamp(),
  `amount` decimal(15,2) NOT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double unsigned NOT NULL DEFAULT 1,
  `description` varchar(255) DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `purchase_invoice_payments_invoice_id_foreign` (`invoice_id`),
  KEY `purchase_invoice_payments_user_id_foreign` (`user_id`),
  KEY `purchase_invoice_payments_cash_account_id_foreign` (`cash_account_id`),
  KEY `purchase_invoice_payments_user_cash_id_foreign` (`user_cash_id`),
  CONSTRAINT `purchase_invoice_payments_cash_account_id_foreign` FOREIGN KEY (`cash_account_id`) REFERENCES `cash_accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_invoice_payments_invoice_id_foreign` FOREIGN KEY (`invoice_id`) REFERENCES `purchase_invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_invoice_payments_user_cash_id_foreign` FOREIGN KEY (`user_cash_id`) REFERENCES `user_cashes` (`id`) ON DELETE SET NULL,
  CONSTRAINT `purchase_invoice_payments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `purchase_invoices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_invoices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned DEFAULT NULL,
  `invoice_date` datetime NOT NULL DEFAULT current_timestamp(),
  `invoice_number` varchar(255) NOT NULL,
  `vendor_id` bigint(20) unsigned DEFAULT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `due_date` datetime DEFAULT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double unsigned NOT NULL DEFAULT 1,
  `subject` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `paid` decimal(15,2) NOT NULL DEFAULT 0.00,
  `status` varchar(255) NOT NULL DEFAULT 'unpaid',
  `cancellation_note` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `purchase_invoices_vendor_id_foreign` (`vendor_id`),
  KEY `purchase_invoices_order_id_foreign` (`order_id`),
  CONSTRAINT `purchase_invoices_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `purchase_orders` (`id`) ON DELETE SET NULL,
  CONSTRAINT `purchase_invoices_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `purchase_order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `product_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `quantity` int(10) unsigned NOT NULL DEFAULT 1,
  `unit_price` decimal(15,2) NOT NULL,
  `vat` double unsigned NOT NULL DEFAULT 0,
  `order_column` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `purchase_order_items_order_id_foreign` (`order_id`),
  KEY `purchase_order_items_group_id_foreign` (`group_id`),
  KEY `purchase_order_items_product_id_foreign` (`product_id`),
  CONSTRAINT `purchase_order_items_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL,
  CONSTRAINT `purchase_order_items_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `purchase_orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `purchase_order_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `purchase_orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `purchase_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_date` date NOT NULL,
  `order_number` varchar(255) NOT NULL,
  `vendor_id` bigint(20) unsigned DEFAULT NULL,
  `currency_code` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double unsigned NOT NULL DEFAULT 1,
  `subject` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `status` varchar(255) NOT NULL DEFAULT 'open',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `purchase_orders_order_number_unique` (`order_number`),
  KEY `purchase_orders_vendor_id_foreign` (`vendor_id`),
  CONSTRAINT `purchase_orders_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `remember_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `remember_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `host` varchar(100) NOT NULL,
  `token` varchar(100) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `reminder_recipients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reminder_recipients` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `reminder_id` bigint(20) unsigned NOT NULL,
  `recipient_type` varchar(255) NOT NULL,
  `recipient_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reminder_recipients_reminder_id_foreign` (`reminder_id`),
  CONSTRAINT `reminder_recipients_reminder_id_foreign` FOREIGN KEY (`reminder_id`) REFERENCES `reminders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `reminder_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reminder_schedules` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `reminder_id` bigint(20) unsigned NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  `scheduled_at` datetime NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `model_type` varchar(255) NOT NULL DEFAULT 'group',
  PRIMARY KEY (`id`),
  KEY `reminder_schedules_reminder_id_foreign` (`reminder_id`),
  KEY `reminder_schedules_group_id_foreign` (`model_id`),
  KEY `reminder_schedules_model_type_model_id_index` (`model_type`,`model_id`),
  CONSTRAINT `reminder_schedules_reminder_id_foreign` FOREIGN KEY (`reminder_id`) REFERENCES `reminders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `reminders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `reminders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `reminder_type` varchar(255) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `notification_type` varchar(255) NOT NULL DEFAULT 'email',
  `template` text NOT NULL,
  `interval` tinyint(4) NOT NULL,
  `time` time NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `additional_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`additional_data`)),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `resource_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `resource_locks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `lockable_type` varchar(255) NOT NULL,
  `lockable_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `resource_locks_user_id_foreign` (`user_id`),
  KEY `resource_locks_lockable_type_lockable_id_index` (`lockable_type`,`lockable_id`),
  CONSTRAINT `resource_locks_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `role_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `rooms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rooms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `number` varchar(255) DEFAULT NULL,
  `capacity` int(11) NOT NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `mutawifs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`mutawifs`)),
  PRIMARY KEY (`id`),
  KEY `rooms_group_id_foreign` (`group_id`),
  CONSTRAINT `rooms_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `group` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT 0,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`payload`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `settings_group_index` (`group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `staff`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `staff` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `transports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transports` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_cashes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_cashes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `group_id` bigint(20) unsigned DEFAULT NULL,
  `category_id` bigint(20) unsigned DEFAULT NULL,
  `cashed_at` datetime NOT NULL DEFAULT current_timestamp(),
  `type` enum('d','c') NOT NULL DEFAULT 'c',
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `amount_c` decimal(15,2) GENERATED ALWAYS AS (`amount` * `exchange_rate`) VIRTUAL,
  `currency` char(3) NOT NULL DEFAULT 'SAR',
  `exchange_rate` double NOT NULL DEFAULT 1,
  `details` text DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `updated_by_id` bigint(20) unsigned DEFAULT NULL,
  `is_fixed` tinyint(1) NOT NULL DEFAULT 0,
  `related_type` varchar(255) DEFAULT NULL,
  `related_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_cashes_user_id_foreign` (`user_id`),
  KEY `user_cashes_group_id_foreign` (`group_id`),
  KEY `user_cashes_related_type_related_id_index` (`related_type`,`related_id`),
  CONSTRAINT `user_cashes_group_id_foreign` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE SET NULL,
  CONSTRAINT `user_cashes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_vendor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_vendor` (
  `user_id` bigint(20) unsigned NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  KEY `user_vendor_user_id_foreign` (`user_id`),
  KEY `user_vendor_vendor_id_foreign` (`vendor_id`),
  CONSTRAINT `user_vendor_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_vendor_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `two_factor_secret` text DEFAULT NULL,
  `two_factor_recovery_codes` text DEFAULT NULL,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `staff_id` bigint(20) unsigned DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(255) DEFAULT NULL,
  `meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`meta`)),
  `name_with_phone` varchar(255) GENERATED ALWAYS AS (concat(`name`,case when `phone` is not null then concat(' (',`phone`,')') else '' end)) VIRTUAL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_staff_id_foreign` (`staff_id`),
  CONSTRAINT `users_staff_id_foreign` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vehicles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vehicles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `capacity` int(10) unsigned NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vendors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vendors` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `original_id` bigint(20) unsigned DEFAULT NULL,
  `vendor_type` varchar(255) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `contact_name` varchar(255) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `contact_phone` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*M!100616 SET NOTE_VERBOSITY=@OLD_NOTE_VERBOSITY */;

/*M!999999\- enable the sandbox mode */ 
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2014_10_12_200000_add_two_factor_columns_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2022_04_06_005902_create_permission_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2022_04_07_000000_create_staff_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2022_04_07_000001_create_airlines_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2022_04_07_000002_create_flights_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2022_04_07_000003_create_hotels_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2022_04_07_000004_create_institutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2022_04_07_000005_create_transports_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2022_04_07_000006_create_customers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2022_04_07_000007_create_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2022_04_07_000008_create_group_hotel_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2022_04_09_012313_create_itineraries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2022_07_14_213134_add_sort_column_to_itineraries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2022_07_14_213718_add_is_arrival_column_to_itineraries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2022_07_18_211022_add_transport_contact_name_to_groups_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2022_07_23_222229_create_manasiks_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2022_08_02_065104_add_hotel_handlers_to_groups_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2022_08_03_214036_add_room_counts_columns_to_group_hotel_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2022_08_10_065240_create_group_visas_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2022_08_18_143723_rename_group_visas_table_to_group_data',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2022_08_19_065131_add_columns_to_group_data_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2022_08_20_074250_add_room_quint_count_to_group_hotel_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2022_08_21_051106_add_contact_details_to_institutions_table',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2022_09_04_162420_update_groups_table_int_columns',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2022_09_06_082034_add_is_confirmed_column_to_group_hotel_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2022_09_10_113747_create_hotel_brokers_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2022_09_10_121922_add_broker_id_to_group_hotel_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2022_09_10_122852_add_confirmation_file_to_group_hotel_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2022_09_10_125128_add_more_info_to_group_hotel_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2022_09_29_211309_change_number_column_size_in_groups_table',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2022_10_17_150915_add_group_service_to_groups_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2022_11_04_133240_add_invoice_number_to_groups_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2022_11_04_152355_add_group_service_to_services_in_groups_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2023_01_04_155223_add_airport_handler_columns_to_groups_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2023_01_04_225900_create_metas_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2023_01_05_002154_add_sort_column_to_group_hotel_table',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2023_01_07_164933_add_password_column_to_customers_table',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2023_01_08_000853_create_activity_log_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2023_01_08_000854_add_event_column_to_activity_log_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2023_01_08_000855_add_batch_uuid_column_to_activity_log_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2023_01_08_011939_modify_flights_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2023_01_08_013015_add_staff_id_to_users_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2023_01_09_203709_add_status_to_groups_table',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2023_01_11_105709_create_customer_users_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2023_01_11_203028_add_logo_column_to_customers_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2023_01_16_004343_create_agents_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2023_01_16_004530_add_agent_id_to_customers_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2023_01_16_005817_add_has_snack_to_itineraries_table',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2022_12_14_083707_create_settings_table',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2023_01_17_233000_create_whats_app_settings',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2023_01_09_155717_add_details_columns_to_hotels_table',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2023_01_20_020048_create_group_cashes_table',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2023_01_20_124207_add_mutawif_ids_columns_to_groups_table',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2023_01_20_125845_add_phone_to_users_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2023_01_23_211836_add_owner_name_to_customers_table',28);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2023_01_24_001544_add_train_details_columns_to_groups_table',29);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2023_01_24_162654_add_user_id_to_group_cashes_table',30);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2023_01_25_114312_add_invoice_amount_to_groups_table',31);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2023_01_26_123227_add_attachment_to_group_cashes_table',32);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2023_01_26_135125_create_cash_categories_table',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2023_01_30_145225_add_category_id_to_group_cashes_table',33);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2023_01_30_173007_add_is_excluded_to_group_cashes_table',34);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2023_02_25_104617_add_soft_delete_to_group_cashes_table',35);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2023_03_15_102152_add_meta_column_to_groups_table',36);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2023_03_18_073547_add_meals_columns_to_groups_table',37);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2023_03_19_165444_create_customer_cashes_table',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2023_03_22_204557_add_token_to_groups_table',38);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2023_03_30_105716_add_login_fields_to_users_table',39);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2023_04_26_065212_add_customer_details_fields_to_customers_table',40);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2023_05_08_081828_create_invoices_table',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2023_05_08_081929_create_invoice_items_table',41);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2023_05_28_065238_create_group_flights_table',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2023_05_28_092510_add_arrival_date_field_to_groups_table',42);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2023_05_28_164706_add_pax_to_group_flights_table',43);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2023_05_30_181911_add_sort_column_to_manasiks_table',44);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2023_07_16_144629_create_invoice_payments_table',45);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2023_07_26_075230_create_vehicles_table',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2023_07_26_092032_create_group_vehicle_table',46);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2023_07_31_210014_add_hotel_broker_id_to_group_cashes_table',47);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2023_08_01_100000_create_resource_lock_table',48);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2023_08_02_102526_add_institution_id_to_group_cashes_table',49);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2023_08_03_093929_add_confirmation_number_field_to_group_hotel_table',50);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2023_08_16_154952_create_periods_table',51);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2023_08_17_184522_create_general_settings',52);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2023_08_27_140249_add_progress_field_to_groups_table',53);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2023_09_14_201850_add_user_id_to_customers_table',54);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2023_09_15_135521_make_group_id_nullable_in_group_hotel_table',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2023_09_15_162944_add_fullname_field_to_hotels_table',55);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2023_09_26_202648_update_general_settings',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2023_09_26_204404_update_settings_table',56);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2023_09_28_064917_add_subject_field_to_invoices_table',57);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2023_10_02_083627_remove_train_fields_in_groups_table',58);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2023_10_10_101738_add_departure_date_field_to_groups_table',59);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2023_10_16_104517_move_customer_users_to_its_own_table',60);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2023_12_27_172827_create_pilgrims_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2023_12_27_173651_create_group_pilgrim_table',61);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2023_12_29_100024_add_phone_field_to_pilgrims_table',62);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (102,'2023_12_30_115158_add_created_by_id_and_updated_by_id_fields_to_groups_table',63);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (103,'2024_01_02_142042_create_sales_items_table',64);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (104,'2024_01_02_170626_add_notes_field_to_group_hotel_table',65);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (105,'2024_01_07_210653_create_tour_leaders_table',66);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (106,'2024_01_13_171723_create_cash_accounts_table',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2024_01_13_171737_create_journal_entries_table',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (108,'2024_01_13_171754_create_journal_entry_items_table',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (109,'2024_01_13_172131_add_exchange_rate_fields_to_group_cashes_table',67);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (110,'2024_01_20_180636_add_currency_fields_to_invoices_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (111,'2024_01_23_191704_add_terms_field_to_invoices_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (112,'2024_01_24_141120_create_job_batches_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (113,'2024_01_24_141126_create_notifications_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (114,'2024_01_24_141133_create_imports_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (115,'2024_01_24_141134_create_exports_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (116,'2024_01_24_141135_create_failed_import_rows_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (117,'2024_01_24_192438_recreate_cash_accounts_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (118,'2024_01_24_202441_add_currency_fields_to_invoice_payments_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (119,'2024_01_24_203408_add_cash_account_id_field_to_invoice_payments_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2024_01_24_205721_update_decimal_fields',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2024_01_24_211649_add_ref_id_field_to_invoice_payments_table',68);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2024_01_31_113059_add_room_order_field_to_group_pilgrim_table',69);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2024_02_06_171456_create_vendors_table',70);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2024_02_07_104639_add_amount_virtual_fields_to_group_cashes_table',71);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2024_02_07_132409_create_user_cashes_table',72);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2024_03_03_060900_add_invoice_id_to_groups_table',73);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2024_03_10_134726_add_payment_method_field_to_invoice_payments_table',74);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2024_03_10_200504_add_userstamps_fields_to_invoice_payments_table',74);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2024_04_23_205905_create_airports_table',75);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2024_04_25_091858_create_airport_handler_table',75);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2024_06_15_210430_add_is_fixed_field_to_cash_accounts_table',76);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2024_06_16_062035_add_owner_fields_to_journal_entry_items',76);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2024_06_16_075947_add_original_id_field_to_vendors_table',76);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (134,'2024_06_19_200413_make_owner_fields_nullable_in_journal_entry_items',77);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (135,'2024_06_26_170020_create_company_settings',78);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (136,'2024_06_26_200928_create_balances_table',79);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (137,'2024_06_26_210608_add_account_id_fields_to_cash_categories_table',80);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (138,'2024_07_01_063350_create_jobs_table',81);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (139,'2024_07_08_140428_add_order_column_field_to_invoice_items_table',82);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (140,'2024_07_08_141402_make_name_field_nullable_in_invoice_items_table',82);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (141,'2024_07_09_023056_create_purchase_orders_table',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (142,'2024_07_09_023103_create_purchase_order_items_table',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (143,'2024_07_11_075028_create_purchase_order_payments_table',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (144,'2024_07_11_091204_add_is_fixed_field_to_user_cashes_table',83);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (145,'2024_07_17_025610_add_group_id_field_to_purchase_order_items_table',84);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (146,'2024_07_17_130059_add_group_id_field_to_user_cashes_table',84);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (147,'2024_07_27_054347_add_group_field_to_cash_categories_table',85);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (148,'2024_08_31_100540_add_related_fields_to_user_cashes_table',86);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (149,'2024_09_02_114500_setup_reminders_tables',87);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (150,'2024_09_08_014344_create_contacts_table',88);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (151,'2024_09_08_100005_add_model_morph_field_to_reminder_schedules_table',89);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (152,'2024_09_19_051925_add_additional_data_field_to_reminders_table',90);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (153,'2024_09_19_055244_fix_reminder_schedules_table',90);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (154,'2024_09_22_064624_create_rooms_table',91);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (155,'2024_09_22_070709_add_room_id_field_to_group_pilgrim_table',91);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (156,'2024_09_24_202101_create_cash_transactions_table',92);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (157,'2024_09_25_125915_add_subject_field_to_reminders_table',93);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (158,'2024_09_29_183134_reverse_group_invoice_relation',94);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (159,'2024_10_02_204616_add_photo_field_to_pilgrims_table',95);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (160,'2024_10_06_200259_add_number_field_to_rooms_table',96);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (161,'2024_10_15_163341_add_name_with_phone_virtual_field_to_users_table',97);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (162,'2024_10_15_164330_add_use_transmitter_field_to_itineraries_table',97);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (163,'2024_10_15_164750_add_snack_details_field_to_itineraries_table',97);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (164,'2024_10_17_054259_add_last_login_fields_to_customer_users_table',98);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (165,'2024_10_19_104933_add_logo_square_field_to_customers_table',99);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (166,'2024_10_31_130106_add_is_tour_leader_field_to_group_pilgrim_table',100);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2024_10_31_135536_add_mutawifs_field_to_rooms_table',101);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2024_11_02_135424_add_vat_column_to_purchase_order_items_table',102);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2024_11_09_084939_refactor_roomlist',103);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2024_11_17_182957_add_via_column_to_group_flights_table',104);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2024_11_17_204411_add_room_single_count_column_to_group_hotel_table',105);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2024_11_19_163446_add_meta_column_to_group_hotel_table',106);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2024_11_19_164624_add_meta_column_to_group_flights_table',106);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2024_11_30_171634_add_city_column_to_itineraries_table',107);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2024_11_30_172831_add_meta_column_to_users_table',107);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (176,'2024_12_02_133927_remove_use_transmitter_column_in_itineraries_table',108);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (177,'2024_12_02_134238_add_meta_column_to_manasiks_table',108);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (178,'2024_12_08_200651_create_customer_cashes_table',109);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2024_12_09_072613_update_payment_method_column_in_invoice_payments_table',109);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2024_12_09_072808_add_customer_cash_id_column_to_invoice_payments_table',109);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2024_12_10_222439_update_unit_price_in_sales_items_table',110);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2024_12_14_195749_create_sessions_table',111);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (183,'2024_12_25_154911_create_remember_tokens_table',112);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (184,'2024_12_27_084910_create_orders_table',113);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (185,'2025_01_14_183538_add_total_and_paid_fields_to_invoices_and_purchase_orders_table',114);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (186,'2025_01_22_074502_add_package_id_field_to_invoices_table',115);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (187,'2025_01_29_154227_create_invoice_refunds_table',116);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (188,'2025_01_31_094943_add_via_columns_to_group_flights_table',117);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (189,'2025_02_20_182741_rename_sales_items_table_to_products',118);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (190,'2025_02_20_191242_add_cost_field_to_products_table',118);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2025_02_20_193025_create_product_categories_table',118);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2025_02_20_195547_add_category_id_field_to_products_table',118);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2025_02_20_201607_add_product_id_field_to_invoice_items_table',118);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2025_02_20_204259_add_product_id_field_to_purchase_order_items_table',118);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2025_02_21_133632_rename_purchase_order_to_purchase_invoice',119);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2025_02_21_183117_create_purchase_orders_table',120);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2025_02_21_203200_add_order_id_field_to_purchase_invoices_table',120);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2025_02_24_135125_add_normal_balance_field_to_cash_accounts_table',121);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2025_02_24_164056_add_status_field_to_purchase_orders_table',122);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2025_02_24_164724_add_cancellation_note_field_to_invoices_table',122);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2025_02_25_111956_rename_group_to_category_in_cash_accounts_table',123);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2025_03_01_072149_update_type_field_in_cash_transactions_table',123);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (203,'2025_03_01_165129_remove_normal_balance_field_in_cash_accounts_table',123);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2025_03_03_113219_migrate_tour_leaders_to_contacts_table',124);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2025_03_03_155104_drop_tour_leaders_table',124);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2025_03_03_160713_create_media_table',124);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (207,'2025_03_04_075137_add_cash_account_id_to_customer_cashes_table',125);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (208,'2025_03_04_145421_create_estimates_table',126);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (209,'2025_03_05_105746_add_invoice_id_to_estimates_table',126);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (210,'2025_03_06_131139_fix_airport_code_fields',127);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (211,'2025_03_07_062758_migrate_hotel_brokers_to_vendors_table',128);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (212,'2025_03_07_123347_migrate_airport_handlers',129);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (213,'2025_03_09_135921_migrate_airport_handlers_2',130);
