<?php

namespace Database\Seeders;

use App\Models\Group;
use Illuminate\Database\Seeder;

class GroupsArrivalDateMigrator extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Group::query()
            ->with(['flights' => fn ($q) => $q->where('type', 'arrival')])
            ->each(function ($group) {
                $group->update([
                    'arrival_date' => count($group->flights)
                        ? $group->flights[0]->date_eta
                        : null,
                ]);
            });
    }
}
