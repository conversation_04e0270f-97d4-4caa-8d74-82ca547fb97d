<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();
        if (! app()->environment('production')) {
            User::updateOrCreate([
                'email' => '<EMAIL>',
            ], [
                'name' => 'Fauzie',
                'password' => Hash::make('bismillah'),
            ]);

            $this->call(SampleDataSeeder::class);
        }
    }
}
