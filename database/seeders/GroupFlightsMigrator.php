<?php

namespace Database\Seeders;

use App\Models\Group;
use App\Models\GroupFlight;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class GroupFlightsMigrator extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $group_flights = [];

        $groups = Group::query()
            ->with(['flight_arr', 'flight_dep'])
            ->get();

        $now = Carbon::now()->toDateTimeString();

        foreach ($groups as $group) {
            if ($group->flight_arr) {
                $date_etd = $group->flight_arr->date_etd
                    ? $group->flight_arr->date_etd->toDateTimeString()
                    : $group->flight_arr->date->setTimeFromTimeString($group->flight_arr->getAttributes()['etd'] ?? '00:00')->toDateTimeString();
                $date_eta = $group->flight_arr->date_eta
                    ? $group->flight_arr->date_eta->toDateTimeString()
                    : $group->flight_arr->date->setTimeFromTimeString($group->flight_arr->getAttributes()['eta'] ?? '00:00')->toDateTimeString();

                $group_flights[] = [
                    'airline_id' => $group->flight_arr->airline_id,
                    'group_id' => $group->id,
                    'type' => 'arrival',

                    'flight_number' => $group->flight_arr->number,

                    'from' => $group->flight_arr->from,
                    'to' => $group->flight_arr->to,

                    'date_etd' => $date_etd,
                    'date_eta' => $date_eta,

                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            if ($group->flight_dep) {
                $date_etd = $group->flight_dep->date_etd
                    ? $group->flight_dep->date_etd->toDateTimeString()
                    : $group->flight_dep->date->setTimeFromTimeString($group->flight_dep->getAttributes()['etd'] ?? '00:00')->toDateTimeString();
                $date_eta = $group->flight_dep->date_eta
                    ? $group->flight_dep->date_eta->toDateTimeString()
                    : $group->flight_dep->date->setTimeFromTimeString($group->flight_dep->getAttributes()['eta'] ?? '00:00')->toDateTimeString();

                $group_flights[] = [
                    'airline_id' => $group->flight_dep->airline_id,
                    'group_id' => $group->id,
                    'type' => 'departure',

                    'flight_number' => $group->flight_dep->number,

                    'from' => $group->flight_dep->from,
                    'to' => $group->flight_dep->to,

                    'date_etd' => $date_etd,
                    'date_eta' => $date_eta,

                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        GroupFlight::query()->insert($group_flights);
    }
}
