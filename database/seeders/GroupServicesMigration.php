<?php

namespace Database\Seeders;

use App\Models\Group;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GroupServicesMigration extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('groups')
            ->where('services', 'la')
            ->update(['services' => implode(',', array_keys(Group::GROUP_SERVICES))]);
    }
}
