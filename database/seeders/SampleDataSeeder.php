<?php

namespace Database\Seeders;

use App\Models\Airline;
use App\Models\Customer;
use App\Models\Hotel;
use App\Models\Institution;
use App\Models\Transport;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        collect([
            'Emirates',
            'Lion Air',
            'Garuda Indonesia',
        ])->each(function ($name) {
            Airline::firstOrCreate([
                'name' => $name,
            ]);
        });

        collect([
            'AZKA AL SAFA',
            'MAKKAH TOWER',
            'OLAYAN GOLDEN',
            'ANJUM MAKKAH',
            'DAR AL GHUFRAN',
            'PULLMAN ZAMZAM',
            'ROY<PERSON> MAJESTIC',
            'LAMAR AJYAD',
        ])
            ->each(function ($hotelName) {
                Hotel::updateOrCreate(['name' => $hotelName, 'city' => 'Makkah'], ['name' => Str::title($hotelName)]);
            });

        collect([
            'RU<PERSON> AL MADINAH',
            'FRONTEL AL HARITHIA',
            'ANDALUS PALACE 3',
            'ANWAR MOVENPICK',
            'ELAF TAIBA',
            'SHAM PROVINCE',
            'ELAF TAIBA',
        ])
            ->each(function ($hotelName) {
                Hotel::updateOrCreate(['name' => $hotelName, 'city' => 'Madinah'], ['name' => Str::title($hotelName)]);
            });

        collect([
            'Muassasah Al Muahedeen',
            'Muassasah Banaa | فرع مؤسسة بنا ',
            'Muassasah Ufud Al Bait | شركة وفود البيت',
        ])
            ->each(function ($institutionName) {
                Institution::firstOrCreate(['name' => $institutionName]);
            });

        collect([
            'Rawahel Al Mashaer',
            'Gaza Al Hamdi',
            'Al Sharia for Transport Pilgrims',
        ])->each(function ($name) {
            Transport::firstOrCreate(['company_name' => $name]);
        });

        collect([
            'PT. Madinah Mitra Mandiri',
            'PT. Allia Tour & Travel',
            'PT. Mudaris Mandiri Wisata',
            'PT. Naila Syafaah Mandiri',
            'RAMANI',
            'PT. Dava Mandiri Wisata',
            'Intanaya',
            'Adib Haramain Tour',
        ])->each(function ($name) {
            Customer::firstOrCreate(['name' => $name]);
        });

        $airlines = [
            'Emirates' => [
                [
                    'number' => 'EK-2809',
                    'date' => '2022-02-21',
                    'from' => 'DXB',
                    'to' => 'MED',
                    'etd' => '14:20',
                    'eta' => '16:10',
                ],
                [
                    'number' => 'EK-2804',
                    'date' => '2022-03-02',
                    'from' => 'JED',
                    'to' => 'DXB',
                    'etd' => '20:20',
                    'eta' => '00:30',
                ],
                [
                    'number' => 'EK-2807',
                    'date' => '2022-03-11',
                    'from' => 'DXB',
                    'to' => 'MED',
                    'etd' => '08:20',
                    'eta' => '10:10',
                ],
            ],
            'Lion Air' => [
                [
                    'number' => 'JT-1110',
                    'date' => '2022-02-21',
                    'from' => 'CGK',
                    'to' => 'MED',
                    'etd' => '13:30',
                    'eta' => '19:50',
                ],
                [
                    'number' => 'JT-1115',
                    'date' => '2022-03-03',
                    'from' => 'JED',
                    'to' => 'CGK',
                    'etd' => '21:20',
                    'eta' => '11:40',
                ],
            ],
            'Garuda Indonesia' => [
                [
                    'number' => 'GA-9868',
                    'date' => '2022-03-12',
                    'from' => 'CGK',
                    'to' => 'MED',
                    'etd' => '07:04',
                    'eta' => '13:45',
                ],
                [
                    'number' => 'GA-9818',
                    'date' => '2022-03-23',
                    'from' => 'JED',
                    'to' => 'CGK',
                    'etd' => '21:40',
                    'eta' => '12:20',
                ],
                [
                    'number' => 'GA-9868',
                    'date' => '2022-04-07',
                    'from' => 'CGK',
                    'to' => 'MED',
                    'etd' => '07:04',
                    'eta' => '13:30',
                ],
                [
                    'number' => 'GA-9818',
                    'date' => '2022-04-18',
                    'from' => 'JED',
                    'to' => 'CGK',
                    'etd' => '19:40',
                    'eta' => '12:20',
                ],
            ],
            'Tigerair' => [
                [
                    'number' => 'TR-596',
                    'date' => '2022-03-31',
                    'from' => 'CGK',
                    'to' => 'JED',
                    'etd' => null,
                    'eta' => '20:40',
                ],
                [
                    'number' => 'TR-597',
                    'date' => '2022-04-09',
                    'from' => 'JED',
                    'to' => 'CGK',
                    'etd' => '22:10',
                    'eta' => null,
                ],
            ],
        ];
        collect($airlines)->each(function ($flights, $airline) {
            /** @var Airline $a */
            $a = Airline::firstOrCreate(['name' => $airline]);
            $a->flights()->createMany($flights);
        });
    }
}
