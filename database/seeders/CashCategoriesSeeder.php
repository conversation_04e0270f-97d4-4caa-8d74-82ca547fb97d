<?php

namespace Database\Seeders;

use App\Models\Finance\CashCategory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CashCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /** @var CashCategory */
        $hotel = CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Hotel']);
        $hotel->children()->createMany([
            ['type' => 'out', 'name' => 'Hotel Makkah'],
            ['type' => 'out', 'name' => 'Hotel Madinah'],
            ['type' => 'out', 'name' => 'Hotel Jeddah'],
            ['type' => 'out', 'name' => 'Hotel Thaif'],
            ['type' => 'out', 'name' => 'Hotel Manasik'],
        ]);

        CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Katering']);
        CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Visa']);

        /** @var CashCategory */
        $fee = CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Fee']);
        $fee->children()->createMany([
            ['type' => 'out', 'name' => 'Fee Handling Bandara Jeddah'],
            ['type' => 'out', 'name' => 'Fee Handling Bandra Madinah'],
            ['type' => 'out', 'name' => 'Fee Handling Bandara Indonesia'],
            ['type' => 'out', 'name' => 'Fee Agent'],
            ['type' => 'out', 'name' => 'Fee Operator Makkah'],
            ['type' => 'out', 'name' => 'Fee Operator Madinah'],
            ['type' => 'out', 'name' => 'Fee Mutawwif'],
        ]);

        CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Tiket Pesawat']);
        CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Tiket Keret']);
        CashCategory::create(['type' => 'out', 'name' => 'Pembayaran Perlengkapan']);
        CashCategory::create(['type' => 'out', 'name' => 'Siskopatuh']);
    }
}
