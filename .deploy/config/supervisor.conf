[supervisord]
logfile=/dev/null
logfile_maxbytes=0
logfile_backups=0
loglevel=info
nodaemon=true

[program:octane]
command=php %(ENV_LARAVEL_PATH)s/artisan octane:frankenphp --max-requests=250
autostart=true
autorestart=true
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:cron]
command=crond -l 2 -f
autorestart=true

[program:laravel-queue-worker]
command=php %(ENV_LARAVEL_PATH)s/artisan queue:work --sleep=3 --tries=3
numprocs=1
autostart=true
autorestart=true
startsecs=0
stdout_events_enabled=true
stderr_events_enabled=true
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

; [program:horizon]
; command=php %(ENV_LARAVEL_PATH)s/artisan horizon
; autorestart=true
; stdout_events_enabled=true
; stderr_events_enabled=true
; stdout_logfile_maxbytes=0
; stderr_logfile_maxbytes=0
; stdout_logfile=/dev/stdout
; stderr_logfile=/dev/stderr
; stopwaitsecs=3600

; [program:websockets]
; command=php %(ENV_LARAVEL_PATH)s/artisan websockets:serve --port=6001
; numprocs=1
; autorestart=true
; stdout_events_enabled=true
; stderr_events_enabled=true
; stdout_logfile_maxbytes=0
; stderr_logfile_maxbytes=0
; stdout_logfile=/dev/stdout
; stderr_logfile=/dev/stderr
