<p align="center"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400"></p>

# Arrahmah Umrah Service

A comprehensive management system for Umrah service operations built with Laravel and Filament.

## Features

- **Customer Management** - Track and manage customer information
- **Group Management** - Organize customers into travel groups
- **Finance Module** - Complete financial management including:
  - Invoices and payments
  - Purchase orders and invoices
  - Estimates
  - Journal entries
  - Starting balances
- **Snack Management** - Track and manage snack distribution
- **Reporting** - Comprehensive reporting tools
- **User Management** - Role-based access control

## Tech Stack

- PHP 8.2+
- Laravel 10+
- Filament 3.2+
- TailwindCSS 3.4+
- MySQL/PostgreSQL
- Vite

## Installation

1. Clone the repository
```bash
git clone [repository-url]
```

2. Install PHP dependencies
```bash
composer install
```

3. Install JavaScript dependencies
```bash
npm install
```

4. Copy environment file and configure
```bash
cp .env.example .env
```

5. Generate application key
```bash
php artisan key:generate
```

6. Run migrations and seed the database
```bash
php artisan migrate --seed
```

7. Create symbolic link for storage
```bash
php artisan storage:link
```

8. Build assets
```bash
npm run build
```

## Development

```bash
# Start development server
php artisan serve

# Watch for asset changes
npm run dev
```

## Deployment

The application includes Docker configuration for easy deployment:

```bash
# Build Docker image
docker build -f .deploy/Dockerfile -t umrahservice .

# Run Docker container
docker run -p 8000:8000 umrahservice
```

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
