<x-layouts.document :title="'Group #'.$group->id.' - Roomlist'" margin="1cm 1.5cm" orientation="landscape" auto-print>
    <h1 class="text-[14pt] font-semibold text-center">Roomlist {{ $group->fullname }}</h1>
    <p class="text-[11pt] text-center text-gray-500">
        {{ $group->arrival_date?->format('d F Y') }} - {{ $group->departure_date?->format('d F Y') }}
    </p>
    @php
        // Group rooms by their hotel combinations
        $roomsByHotels = $group->rooms->groupBy(function($room) {
            return implode('-', $room->getAssignedHotelIds());
        });
        $groupHotels = $group->group_hotels()->with('hotel')->orderBy('check_in')->get();
    @endphp

    @foreach ($roomsByHotels as $groupHotelStr => $rooms)
        @if(!empty($groupHotelStr))
            <table class="w-full mt-4">
                @foreach(explode('-', $groupHotelStr) as $groupHotelId)
                    @php $groupHotel = $groupHotels->firstWhere('id', $groupHotelId); @endphp
                    @if ($groupHotel)
                    <tr>
                        <td>
                            <strong>Hotel {{ $groupHotel->hotel->city }}</strong> -
                            {{ $groupHotel->hotel->name }}
                            @php $roomType = App\Enums\HotelRoomType::tryFrom($groupHotel->meta['room_type'] ?? ''); @endphp
                            @if ($roomType)
                                ({{ $roomType?->getShortLabel() }})
                            @endif
                        </td>
                        <td>
                            {{ $groupHotel->check_in?->format('d F Y') ?? 'n/a' }} -
                            {{ $groupHotel->check_out?->format('d F Y') ?? 'n/a' }}
                        </td>
                    </tr>
                    @endif
                @endforeach
            </table>
        @endif

        <table class="border border-black mt-4 text-[9pt] w-full">
            <thead>
                <tr>
                    <th class="px-2 py-1 border border-black">NO</th>
                    <th class="px-2 py-1 border border-black" style="width: 30%;">NAMA JAMAAH</th>
                    <th class="px-2 py-1 border border-black">UMUR</th>
                    <th class="px-2 py-1 border border-black">JENIS<br>KELAMIN</th>
                    <th class="px-2 py-1 border border-black">NO PASPOR</th>
                    <th class="px-2 py-1 border border-black">TIPE KAMAR</th>
                    @foreach(explode('-', $groupHotelStr) as $groupHotelId)
                        <th class="px-2 py-1 uppercase border border-black">NOMOR<br>KAMAR<br>{{ $groupHotels->firstWhere('id', $groupHotelId)?->hotel->city }}</th>
                    @endforeach
                    <th class="px-2 py-1 border border-black" style="width: 20%;">KETERANGAN</th>
                </tr>
            </thead>
            <tbody>
                @php $no = 1; @endphp
                @foreach ($rooms as $room)
                    @php
                        $capacity = $room->capacity->value;
                        $filled = count($room->pilgrims) + count($room->mutawifs ?? []);
                        $max = max($capacity, $filled);
                    @endphp
                    @foreach ($room->pilgrims as $pilgrim)
                        <tr>
                            <td class="px-2 py-1 border border-black">{{ $no++ }}</td>
                            <td class="px-2 py-1 border border-black">{{ $pilgrim->fullname }} {{ $pilgrim->pivot->is_tour_leader ? '(TL)' : '' }}</td>
                            <td class="px-2 py-1 text-center border border-black">{{ $pilgrim->age }}</td>
                            <td class="px-2 py-1 text-center border border-black">{{ $pilgrim->gender->getLabel() }}</td>
                            <td class="px-2 py-1 text-center border border-black">{{ $pilgrim->passport_number }}</td>
                            @if ($loop->index == 0)
                                <td class="px-2 py-1 text-center border border-black" rowspan="{{ $max }}">{{ $room->name }}</td>
                                @foreach(explode('-', $groupHotelStr) as $groupHotelId)
                                    <td class="relative px-2 py-1 text-center border border-black" rowspan="{{ $max }}">
                                        <p class="absolute left-1 top-0 font-bold text-[7pt]">{{ intval($room->number) }}</p>
                                        {{ $room->getRoomNumberForHotel($groupHotelId) }}
                                    </td>
                                @endforeach
                                <td class="px-2 py-1 border border-black" rowspan="{{ $max }}">{{ $room->meta['details'] ?? null }}</td>
                            @endif
                        </tr>
                    @endforeach
                    @if (filled($room->mutawifs))
                        @foreach ($room->mutawifs as $index)
                            @php
                                $mutawifProperty = "mutawif" . ($index > 1 ? "_$index" : "");
                                $mutawif = $room->group->$mutawifProperty;
                            @endphp
                            <tr>
                                <td class="px-2 py-1 border border-black">{{ $no++ }}</td>
                                <td class="px-2 py-1 border border-black">{{ $mutawif->name }} (Mutawif{{ $index > 1 ? " $index" : '' }})</td>
                                <td class="px-2 py-1 text-center border border-black"></td>
                                <td class="px-2 py-1 text-center border border-black"></td>
                                <td class="px-2 py-1 text-center border border-black"></td>
                                @if ($index == 0 && count($room->pilgrims) == 0)
                                    <td class="px-2 py-1 text-center border border-black" rowspan="{{ $max }}">{{ $room->name }}</td>
                                    @foreach(explode('-', $groupHotelStr) as $groupHotelId)
                                        <td class="relative px-2 py-1 text-center border border-black" rowspan="{{ $max }}">
                                            <p class="absolute left-1 top-0 font-bold text-[7pt]">{{ intval($room->number) }}</p>
                                            {{ $room->getRoomNumberForHotel($groupHotelId) }}
                                        </td>
                                    @endforeach
                                    <td class="px-2 py-1 border border-black" rowspan="{{ $max }}">{{ $room->meta['details'] ?? null }}</td>
                                @endif
                            </tr>
                        @endforeach
                    @endif
                    @if ($max > $filled)
                        @for ($i = 0; $i < $max - $filled; $i++)
                            <tr>
                                <td class="px-2 py-1 border border-black">{{ $no++ }}</td>
                                <td class="px-2 py-1 border border-black"></td>
                                <td class="px-2 py-1 text-center border border-black"></td>
                                <td class="px-2 py-1 text-center border border-black"></td>
                                <td class="px-2 py-1 text-center border border-black"></td>
                                @if ($i == 0 && $filled == 0)
                                    <td class="px-2 py-1 text-center border border-black" rowspan="{{ $max }}">{{ $room->name }}</td>
                                    @foreach(explode('-', $groupHotelStr) as $groupHotelId)
                                        <td class="relative px-2 py-1 text-center border border-black" rowspan="{{ $max }}">
                                            <p class="absolute left-1 top-0 font-bold text-[7pt]">{{ intval($room->number) }}</p>
                                            {{ $room->getRoomNumberForHotel($groupHotelId) }}
                                        </td>
                                    @endforeach
                                    <td class="px-2 py-1 border border-black" rowspan="{{ $max }}">{{ $room->meta['details'] ?? null }}</td>
                                @endif
                            </tr>
                        @endfor
                    @endif
                @endforeach
            </tbody>
        </table>
    @endforeach
</x-layouts.document>
