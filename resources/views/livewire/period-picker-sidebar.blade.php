<div class="md:hidden">
    @hasanyrole(['Admin', 'Finance'])
    <x-filament::dropdown placement="bottom-start">
        <x-slot name="trigger">
            <x-filament::button
                color="gray"
                icon="heroicon-o-chevron-down"
                icon-position="after"
            >
                {{ $current_period->name }}
            </x-filament::button>
        </x-slot>

        <x-filament::dropdown.list>
            @foreach ($periods as $period)
                <x-filament::dropdown.list.item
                    tag="button"
                    :icon="$current_period->id == $period->id ? 'heroicon-o-check' : 'heroicon-o-minus-small'"
                    wire:click="setPeriod({{ $period->id }})"
                    badge-color="success"
                    :color="$current_period->id == $period->id ? 'primary' : 'gray'"
                >
                    {{ $period->name }}
                    @if ($period->isActive())
                    <x-slot name="badge">
                        Active
                    </x-slot>
                    @endif
                </x-filament::dropdown.list.item>
            @endforeach
        </x-filament::dropdown.list>
    </x-filament::dropdown>
    @else
    <div class="mr-4 rtl:mr-0 rtl:ml-4">
        <x-filament::button
            color="gray"
            disabled
        >
            {{ $current_period->name }}
        </x-filament::button>
    </div>
    @endhasanyrole
</div>
