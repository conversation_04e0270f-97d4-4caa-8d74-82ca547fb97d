<div
    class="fixed z-50 bottom-4 right-4"
    x-data="aiChatAssistant()"
    x-init="init()"
    @start-ai-response.window="startAiResponse($event.detail.queryText)"
>
    <!-- Chat toggle button -->
    <button
        wire:click="toggleChat"
        class="ai-chat-toggle flex items-center justify-center w-12 h-12 text-white transition-all duration-200 rounded-full shadow-lg bg-primary-600 hover:bg-primary-700 focus:outline-none hover:scale-110"
        :class="{ 'animate-pulse': $wire.isLoading || $wire.isTyping }"
    >
        @if($isOpen)
            <x-heroicon-s-x-mark class="w-6 h-6" />
        @else
            <x-heroicon-s-sparkles class="w-6 h-6" />
        @endif
    </button>

    <!-- Chat panel -->
    <div
        x-show="$wire.isOpen"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform translate-y-4 scale-95"
        x-transition:enter-end="opacity-100 transform translate-y-0 scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform translate-y-0 scale-100"
        x-transition:leave-end="opacity-0 transform translate-y-4 scale-95"
        class="ai-chat-panel absolute right-0 overflow-hidden bg-white rounded-lg shadow-xl bottom-16 w-96 max-w-[calc(100vw-2rem)] max-h-[600px] flex flex-col"
        style="display: none;"
    >
        <!-- Chat header -->
        <div class="flex items-center justify-between flex-shrink-0 px-4 py-3 text-white bg-primary-600">
            <div class="flex items-center space-x-2">
                <h3 class="text-lg font-medium">AI Database Assistant</h3>
                <div
                    x-show="$wire.isLoading || $wire.isTyping"
                    class="flex items-center space-x-1"
                >
                    <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                    <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                    <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
                </div>
            </div>
            <div class="flex space-x-2">
                <button
                    wire:click="clearConversation"
                    class="text-white transition-colors duration-200 hover:text-gray-200 focus:outline-none"
                    title="Clear conversation"
                    :disabled="$wire.isLoading || $wire.isTyping"
                >
                    <x-heroicon-s-trash class="w-5 h-5" />
                </button>
                <button
                    wire:click="toggleChat"
                    class="text-white transition-colors duration-200 hover:text-gray-200 focus:outline-none"
                >
                    <x-heroicon-s-x-mark class="w-5 h-5" />
                </button>
            </div>
        </div>

        <!-- Chat messages -->
        <div
            class="ai-chat-messages flex-1 p-4 overflow-y-auto bg-gray-50 scroll-smooth"
            id="chat-messages"
            x-ref="chatMessages"
        >
            @if(empty($conversation))
                <div class="flex items-center justify-center h-full text-gray-500">
                    <div class="text-center">
                        <x-heroicon-o-sparkles class="w-12 h-12 mx-auto mb-2 text-primary-300" />
                        <p class="text-sm">Ask me anything about your database!</p>
                        <p class="mt-1 text-xs text-gray-400">I can help you query and understand your data</p>
                    </div>
                </div>
            @else
                @foreach($conversation as $index => $message)
                    <div
                        class="message-container animate-fade-in"
                        x-data="{ isVisible: false }"
                        x-intersect="isVisible = true"
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0"
                    >
                        @include('livewire.partials.chat-message', [
                            'message' => $message,
                            'isStreaming' => isset($message['streaming']) ? $message['streaming'] : false
                        ])
                    </div>
                @endforeach
            @endif

            @if($isLoading)
                <div class="flex items-center space-x-2 text-gray-500 animate-fade-in">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
                    </div>
                    <span class="text-sm">AI is thinking...</span>
                </div>
            @endif

            @if($isTyping)
                <div class="flex items-center space-x-2 text-gray-500 animate-fade-in">
                    <div class="flex space-x-1">
                        <div class="w-1 h-1 rounded-full bg-primary-400 animate-bounce" style="animation-delay: 0ms;"></div>
                        <div class="w-1 h-1 rounded-full bg-primary-400 animate-bounce" style="animation-delay: 150ms;"></div>
                        <div class="w-1 h-1 rounded-full bg-primary-400 animate-bounce" style="animation-delay: 300ms;"></div>
                    </div>
                    <span class="text-sm">AI is typing...</span>
                </div>
            @endif
        </div>

        <!-- Chat input -->
        <div class="flex-shrink-0 p-4 bg-white border-t">
            <form wire:submit.prevent="sendQuery" @submit="scrollToBottom()">
                <div class="flex space-x-2">
                    <input
                        type="text"
                        wire:model="query"
                        placeholder="Ask about your database..."
                        class="flex-1 transition-all duration-200 border-gray-300 rounded-md shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                        autocomplete="off"
                        :disabled="$wire.isLoading || $wire.isTyping"
                        x-ref="queryInput"
                        @keydown.enter="if (!$event.shiftKey) { $event.preventDefault(); $wire.sendQuery(); }"
                    >
                    <button
                        type="submit"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium leading-4 text-white transition-all duration-200 border border-transparent rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:border-primary-700 focus:ring focus:ring-primary-200 active:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        :disabled="$wire.isLoading || $wire.isTyping || !$wire.query.trim()"
                        :class="{ 'animate-pulse': $wire.isLoading || $wire.isTyping }"
                    >
                        <div x-show="!($wire.isLoading || $wire.isTyping)">
                            <x-heroicon-s-paper-airplane class="w-4 h-4" />
                        </div>
                        <div x-show="$wire.isLoading || $wire.isTyping" class="loading-dots">
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function aiChatAssistant() {
            return {
                init() {
                    // Auto-scroll to bottom when new messages arrive
                    this.$watch('$wire.conversation', (newConversation, oldConversation) => {
                        this.$nextTick(() => {
                            this.scrollToBottom();

                            // Check if a new AI message was added
                            if (newConversation.length > oldConversation.length) {
                                const lastMessage = newConversation[newConversation.length - 1];
                                if (lastMessage.role === 'assistant') {
                                    this.animateTyping(lastMessage.content);
                                }
                            }
                        });
                    });

                    // Focus input when chat opens
                    this.$watch('$wire.isOpen', (isOpen) => {
                        if (isOpen) {
                            this.$nextTick(() => {
                                this.$refs.queryInput?.focus();
                            });
                        }
                    });
                },

                scrollToBottom() {
                    this.$nextTick(() => {
                        const chatMessages = this.$refs.chatMessages;
                        if (chatMessages) {
                            chatMessages.scrollTop = chatMessages.scrollHeight;
                        }
                    });
                },

                startAiResponse(queryText) {
                    // Dispatch the processing event after a small delay to show loading state
                    setTimeout(() => {
                        this.$wire.dispatch('process-ai-query', { queryText });
                    }, 800); // Slightly longer delay for better UX
                },

                animateTyping(text) {
                    // Add a subtle typing animation effect
                    const lastMessageContainer = this.$refs.chatMessages.lastElementChild;
                    if (lastMessageContainer) {
                        lastMessageContainer.style.animation = 'messageSlideIn 0.5s ease-out';
                    }

                    // Play a subtle notification sound (if chat is not open)
                    if (!this.$wire.isOpen) {
                        this.playNotificationSound();
                    }
                },

                playNotificationSound() {
                    // Create a subtle notification sound using Web Audio API
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                        gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.2);
                    } catch (e) {
                        // Fallback: do nothing if audio context is not supported
                        console.log('Audio notification not supported');
                    }
                }
            }
        }

        // Add some keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Escape to close chat
            if (e.key === 'Escape' && window.Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id')).$wire.isOpen) {
                window.Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id')).$wire.toggleChat();
            }
        });
    </script>
</div>
