<div class="mb-4 {{ $message['role'] === 'user' ? 'text-right' : '' }}">
    <div class="inline-block max-w-xs sm:max-w-md rounded-lg p-3 {{ $message['role'] === 'user' ? 'bg-primary-100 text-primary-900' : 'bg-white border border-gray-200' }}">
        <div class="prose-sm prose max-w-none">
            @if($message['role'] === 'assistant' && $isStreaming)
                <div class="flex items-center space-x-2">
                    <span>{!! \Illuminate\Support\Str::markdown($message['content']) !!}</span>
                    <div class="flex space-x-1 animate-pulse">
                        <div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                        <div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                        <div class="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
                    </div>
                </div>
            @else
                {!! \Illuminate\Support\Str::markdown($message['content']) !!}
            @endif
        </div>

        @if(isset($message['data']) && !empty($message['data']) && !$isStreaming)
            <div class="mt-2 text-xs" x-data="{show: false}">
                <button
                    @click="show = !show"
                    class="text-primary-600 hover:underline"
                >
                    <span x-show="!show">Show data</span>
                    <span x-show="show" style="display: none">Hide data</span>
                </button>

                <div x-show="show" style="display: none" class="mt-2 overflow-x-auto">
                    <pre class="p-2 text-xs bg-gray-100 rounded">{{ json_encode($message['data'], JSON_PRETTY_PRINT) }}</pre>
                </div>
            </div>
        @endif
    </div>
    <div class="mt-1 text-xs text-gray-500">
        {{ \Carbon\Carbon::parse($message['timestamp'])->format('g:i A') }}
    </div>
</div>
