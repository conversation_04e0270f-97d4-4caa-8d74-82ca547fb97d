<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div x-data="{ state: $wire.$entangle('{{ $getStatePath() }}') }">
        <!-- Interact with the `state` property in Alpine.js -->
        Lorem ipsum dolor sit amet consectetur, adipisicing elit. Labore optio accusantium laboriosam aliquam voluptate? Repellat iste recusandae reiciendis atque fugiat culpa, cum aliquid quae aliquam consectetur? Sint quae ex maiores?
    </div>
</x-dynamic-component>
