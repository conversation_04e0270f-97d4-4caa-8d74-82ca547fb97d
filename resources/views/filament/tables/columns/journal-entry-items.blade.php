@php
    $record = $getRecord();
@endphp

<table class="text-sm leading-6 text-gray-950">
    <tbody class="divide-y">
    @foreach ($record->items as $item)
        <tr>
            <td class="px-3 py-3">{{ $item->account->name }}</td>
            <td class="px-3 py-3">
                @if ($item->type === 'd')
                    <x-filament::badge color="success">Debit</x-filament::badge>
                @else
                    <x-filament::badge color="warning">Credit</x-filament::badge>
                @endif
            </td>
            <td class="px-3 py-3 font-medium tabular-nums">{{ $item->type === 'd' ? money($item->amount, 'SAR', true) : '' }}</td>
            <td class="px-3 py-3 font-medium tabular-nums">{{ $item->type === 'c' ? money($item->amount, 'SAR', true) : '' }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
