@php
    $state = $getState();
    $record = $getRecord();

    $stateIcon = $state ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle';
    $stateColor = $state ? 'success' : 'warning';

    $stateColor = match ($stateColor) {
        'warning' => 'text-warning-500',
        'success' => 'text-success-500',
    };
@endphp

<div {{ $attributes->merge($getExtraAttributes())->class(['px-4 py-3']) }}>
    <div class="flex items-center gap-2">
        @if ($state !== null)
            <x-dynamic-component :component="$stateIcon" :class="'w-6 h-6' . ' ' . $stateColor" />
        @endif
        @if ($state === true && $record->confirmation_file !== null)
            @php $url = route('admin.group-hotel.confirmation', ['groupHotel' => $record, 'file' => $record->confirmation_file]); @endphp
            <x-filament::link
                icon="heroicon-o-document-magnifying-glass"
                href="{{ $url }}"
                x-on:click="SimpleLightBox.open(event, '{{ $record->confirmation_file_url }}')"
                target="_blank" tooltip="Confirmation file" />
        @endif
        @if ($state === true)
            @php
                $confirmationFiles = $record->getMedia('confirmation-files');
            @endphp
            @foreach ($confirmationFiles as $file)
                <x-filament::link
                    icon="heroicon-o-document-magnifying-glass"
                    href="{{ $file->getFullUrl() }}"
                    x-on:click="SimpleLightBox.open(event, '{{ $file->getFullUrl() }}')"
                    target="_blank" tooltip="Confirmation file" />
            @endforeach
        @endif
    </div>
    @if ($state === true && $record->confirmation_number != null)
        <p class="mt-2 text-sm">{{ $record->confirmation_number }}</p>
    @endif
</div>
