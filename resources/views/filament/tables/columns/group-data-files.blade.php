@php
    $record = $getRecord();
    $files = [];
    if ($record->visa) {
        $files[] = [
            'name' => 'Visa',
            'url' => route('admin.groups.file', ['group' => $record->group_id, 'name' => 'visa', 'file' => $record->visa]),
        ];
    }
    if ($record->ticket) {
        $files[] = [
            'name' => 'Ticket',
            'url' => route('admin.groups.file', ['group' => $record->group_id, 'name' => 'ticket', 'file' => $record->ticket]),
        ];
    }
    if ($record->roomlist) {
        $files[] = [
            'name' => 'Roomlist',
            'url' => route('admin.groups.file', ['group' => $record->group_id, 'name' => 'roomlist', 'file' => $record->roomlist]),
        ];
    }
    if ($record->manifest) {
        $files[] = [
            'name' => 'Manifest',
            'url' => route('admin.groups.file', ['group' => $record->group_id, 'name' => 'manifest', 'file' => $record->manifest]),
        ];
    }
    if (filled($record->files)) {
        foreach ($record->files as $file) {
            if (filled($file['file'])) {
                $files[] = [
                    'name' => $file['name'],
                    'url' => route('admin.groups.file', ['group' => $record->group_id, 'name' => $file['name'], 'file' => $file['file']]),
                ];
            }
        }
    }
@endphp
<div {{ $attributes->merge($getExtraAttributes())->class(['p-3']) }}>
    @if (filled($files))
        <x-filament::dropdown placement="bottom-end">
            <x-slot name="trigger">
                <x-filament::button size="sm" color="gray" icon="heroicon-o-arrow-down-tray"
                    label-sr-only>Download</x-filament::button>
            </x-slot>
            <x-filament::dropdown.list>
                @foreach ($files as $file)
                    <x-filament::dropdown.list.item icon="heroicon-o-document-magnifying-glass" tag="a"
                        href="{{ $file['url'] }}" target="_blank">{{ $file['name'] }}</x-filament::dropdown.item>
                @endforeach
            </x-filament::dropdown.list>
        </x-filament::dropdown>
    @endif
</div>
