@php
    $record = $getRecord();
@endphp

<div {{ $attributes->merge($getExtraAttributes())->class([
    'px-4 py-3 flex flex-wrap items-center gap-1',
    match ($getAlignment()) {
        'left' => 'justify-start',
        'center' => 'justify-center',
        'right' => 'justify-end',
        default => null,
    },
]) }}>
    @if ($record->confirmed)
        <span @class([
            'inline-flex items-center justify-center min-h-6 px-2 py-0.5 text-sm font-medium tracking-tight rounded-xl text-success-700 bg-success-500/10 whitespace-normal',
            'dark:text-success-500' => config('tables.dark_mode'),
        ])>
            {{ $record->confirmed }} confirmed
        </span>
    @endif
    @if ($record->unconfirmed)
        <span @class([
            'inline-flex items-center justify-center min-h-6 px-2 py-0.5 text-sm font-medium tracking-tight rounded-xl text-warning-700 bg-warning-500/10 whitespace-normal',
            'dark:text-warning-500' => config('tables.dark_mode'),
        ])>
            {{ $record->unconfirmed }} unconfirmed
        </span>
    @endif
</div>
