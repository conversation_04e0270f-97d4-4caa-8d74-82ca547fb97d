@php
    $record = $getRecord();
@endphp
<div class="grid gap-y-1 px-3 py-4 w-full fi-ta-text">
    <ul class="flex flex-col">
        @foreach ($record->pilgrims as $pilgrim)
        <li class="flex max-w-max">
            <div class="inline-flex gap-1.5 items-center fi-ta-text-item">
                <span class="text-sm leading-6 fi-ta-text-item-label text-gray-950 dark:text-white">
                    {{ $pilgrim->fullname_with_title }}
                    @if ($pilgrim->pivot->is_tour_leader)
                    <span style="opacity: 0.375;">—</span>
                    <span style="display:inline-flex;gap:0.375rem;margin-inline-start:0.25rem;">
                        <x-filament::badge color="success">Tour Leader</x-filament::badge>
                    </span>
                    @endif
                </span>
            </div>
        </li>
        @endforeach
        @if (filled($record->mutawifs))
            @foreach ($record->mutawifs as $index)
                @php
                    $mutawifProperty = "mutawif" . ($index > 1 ? "_$index" : "");
                    $mutawif = $record->group->$mutawifProperty;
                @endphp
                <li class="flex max-w-max">
                    <div class="inline-flex gap-1.5 items-center fi-ta-text-item">
                        <span class="text-sm leading-6 fi-ta-text-item-label text-gray-950 dark:text-white">
                            {{ $mutawif->name }}
                            <span style="opacity: 0.375;">—</span>
                            <span style="display:inline-flex;gap:0.375rem;margin-inline-start:0.25rem;">
                                <x-filament::badge color="warning">Mutawif {{ $index > 1 ? $index : '' }}</x-filament::badge>
                            </span>
                        </span>
                    </div>
                </li>
            @endforeach
        @endif
    </ul>
</div>