@php
    $record = $getRecord();
@endphp

<div {{ $attributes->merge($getExtraAttributes())->class([
    'px-4 py-3 flex flex-col items-start gap-1',
    match ($getAlignment()) {
        'left' => 'justify-start',
        'center' => 'justify-center',
        'right' => 'justify-end',
        default => null,
    },
]) }}>
    @foreach ($record->hotels as $hotel)
        <div>
            <p @class([
                'min-h-6 px-2 py-0.5 text-sm font-medium tracking-tight rounded-xl whitespace-normal',
                'text-success-700 bg-success-500/10' => $hotel->pivot->is_confirmed,
                'text-warning-700 bg-warning-500/10' => !$hotel->pivot->is_confirmed,
                'dark:text-success-500' => $hotel->pivot->is_confirmed && config('tables.dark_mode'),
                'dark:text-warning-500' => !$hotel->pivot->is_confirmed && config('tables.dark_mode'),
            ])>
                <span class="whitespace-nowrap">{{ $hotel->city }} - {{ $hotel->name }}</span> <br>
                <span class="whitespace-nowrap">{{ collect([
                    $hotel->pivot->room_single_count ? $hotel->pivot->room_single_count . ' SG' : null,
                    $hotel->pivot->room_double_count ? $hotel->pivot->room_double_count . ' DB' : null,
                    $hotel->pivot->room_triple_count ? $hotel->pivot->room_triple_count . ' TP' : null,
                    $hotel->pivot->room_quad_count ? $hotel->pivot->room_quad_count . ' QD' : null,
                    $hotel->pivot->room_quint_count ? $hotel->pivot->room_quint_count . ' QT' : null,
                ])->filter()->join(', ') }}</span> |
                <span class="whitespace-nowrap">{{ $hotel->pivot->check_in->format('d/m/Y') }} - {{ $hotel->pivot->check_out->format('d/m/Y') }}</span>
            </p>
        </div>
    @endforeach
</div>
