<div class="quick-create-component">
@if ($resources && $this->shouldBeHidden() === false)
    <x-filament::dropdown placement="bottom-start" :teleport="true">
        <x-slot name="trigger">
            @if ($label)
                <x-filament::button
                    outlined
                    icon="heroicon-o-plus"
                    size="sm"
                >
                    <span class="hidden md:block">
                        {{ $label }}
                    </span>
                </x-filament::button>
            @else
                <x-filament::icon-button
                    icon="heroicon-o-plus"
                />
            @endif
        </x-slot>
        <x-filament::dropdown.list>
            @foreach($resources as $resource)
                <x-filament::dropdown.list.item
                    :icon="$hiddenIcons ? null : $resource['icon']"
                    :wire:click="$resource['action']"
                    :href="$resource['url']"
                    :tag="$resource['url'] ? 'a' : 'button'"
                >
                    {{ $resource['label'] }}
                </x-filament::dropdown.list.item>
            @endforeach
        </x-filament::dropdown.list>
    </x-filament::dropdown>

    <x-filament-actions::modals />
@endif
</div>
