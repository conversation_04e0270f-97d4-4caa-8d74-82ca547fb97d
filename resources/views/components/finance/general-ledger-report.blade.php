@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">General Ledger</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
        @if($data['selected_account_id'])
            <p class="text-sm text-gray-600">Filtered for specific account</p>
        @endif
    </div>

    @if(count($data['accounts']) > 0)
        @foreach($data['accounts'] as $accountData)
            <div class="space-y-4 border border-gray-200 rounded-lg p-4">
                {{-- Account Header --}}
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold">
                            {{ $accountData['account']['code'] }} - {{ $accountData['account']['name'] }}
                        </h2>
                        <span class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                            {{ \App\Enums\Finance\AccountCategory::from($accountData['account']['category'])->getLabel() }}
                        </span>
                    </div>
                    
                    @if($accountData['opening_balance'] != 0)
                        <div class="text-sm text-gray-600">
                            Opening Balance: 
                            <span class="font-medium tabular-nums">
                                {{ money($data['exchange_rate'] * $accountData['opening_balance'], $data['currency_code'], true) }}
                            </span>
                        </div>
                    @endif
                </div>

                {{-- Transactions Table --}}
                @if(count($accountData['transactions']) > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b bg-gray-50">
                                    <th class="py-2 px-3 text-left">Date</th>
                                    <th class="py-2 px-3 text-left">Description</th>
                                    <th class="py-2 px-3 text-right">Debit</th>
                                    <th class="py-2 px-3 text-right">Credit</th>
                                    <th class="py-2 px-3 text-right">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{-- Opening Balance Row --}}
                                @if($accountData['opening_balance'] != 0)
                                    <tr class="border-b bg-blue-50">
                                        <td class="py-2 px-3 text-gray-600">{{ \Carbon\Carbon::parse($period['start'])->subDay()->format('d M Y') }}</td>
                                        <td class="py-2 px-3 font-medium text-blue-700">Opening Balance</td>
                                        <td class="py-2 px-3 text-right">-</td>
                                        <td class="py-2 px-3 text-right">-</td>
                                        <td class="py-2 px-3 text-right font-medium tabular-nums">
                                            {{ money($data['exchange_rate'] * $accountData['opening_balance'], $data['currency_code'], true) }}
                                        </td>
                                    </tr>
                                @endif

                                {{-- Transaction Rows --}}
                                @foreach($accountData['transactions'] as $transaction)
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="py-2 px-3">{{ \Carbon\Carbon::parse($transaction['date'])->format('d M Y') }}</td>
                                        <td class="py-2 px-3">{{ $transaction['details'] }}</td>
                                        <td class="py-2 px-3 text-right tabular-nums">
                                            @if($transaction['debit'] > 0)
                                                {{ number_format($transaction['debit'] * $data['exchange_rate'], 2) }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="py-2 px-3 text-right tabular-nums">
                                            @if($transaction['credit'] > 0)
                                                {{ number_format($transaction['credit'] * $data['exchange_rate'], 2) }}
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td class="py-2 px-3 text-right font-medium tabular-nums">
                                            {{ money($data['exchange_rate'] * $transaction['balance'], $data['currency_code'], true) }}
                                        </td>
                                    </tr>
                                @endforeach

                                {{-- Closing Balance Row --}}
                                <tr class="border-t-2 bg-green-50 font-medium">
                                    <td class="py-2 px-3 text-gray-600">{{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</td>
                                    <td class="py-2 px-3 font-semibold text-green-700">Closing Balance</td>
                                    <td class="py-2 px-3 text-right">-</td>
                                    <td class="py-2 px-3 text-right">-</td>
                                    <td class="py-2 px-3 text-right font-semibold tabular-nums">
                                        {{ money($data['exchange_rate'] * $accountData['closing_balance'], $data['currency_code'], true) }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8 text-gray-500">
                        <p>No transactions found for this account in the selected period.</p>
                        @if($accountData['opening_balance'] != 0)
                            <p class="mt-2">
                                Account balance remains: 
                                <span class="font-medium tabular-nums">
                                    {{ money($data['exchange_rate'] * $accountData['opening_balance'], $data['currency_code'], true) }}
                                </span>
                            </p>
                        @endif
                    </div>
                @endif

                {{-- Account Summary --}}
                @if(count($accountData['transactions']) > 0)
                    @php
                        $totalDebits = collect($accountData['transactions'])->sum('debit');
                        $totalCredits = collect($accountData['transactions'])->sum('credit');
                    @endphp
                    <div class="grid grid-cols-3 gap-4 pt-4 border-t bg-gray-50 rounded p-3">
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Total Debits</div>
                            <div class="font-medium tabular-nums">{{ money($data['exchange_rate'] * $totalDebits, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Total Credits</div>
                            <div class="font-medium tabular-nums">{{ money($data['exchange_rate'] * $totalCredits, $data['currency_code'], true) }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-600">Net Change</div>
                            <div class="font-medium tabular-nums">{{ money($data['exchange_rate'] * ($accountData['closing_balance'] - $accountData['opening_balance']), $data['currency_code'], true) }}</div>
                        </div>
                    </div>
                @endif
            </div>
        @endforeach
    @else
        <div class="text-center py-12 text-gray-500">
            <div class="text-lg font-medium">No transactions found</div>
            <p class="mt-2">No journal entries were found for the selected period and criteria.</p>
        </div>
    @endif
</div>
