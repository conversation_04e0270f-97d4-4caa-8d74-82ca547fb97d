@props([
    'record' => null,
    'isInline' => false
])

@if (!$isInline)
<style>
    html {
        color: var(--arm-blue);
        font-size: 10pt;
    }

    #order-page .page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    #order-page .footer-space {
        height: 1cm;
    }

    @media screen {
        body {
            position: relative;
        }

        #order-page .page-footer {
            left: 1cm;
            right: 1cm;
            padding-bottom: 1cm;
            position: absolute;
        }
    }
</style>
@endif
<style>
    :root {
        --arm-blue: #1a2b48;
    }

    #order-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }

    #order-page .w-full {
        width: 100%;
    }

    #order-page .order-table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: .5rem 1rem;
    }

    #order-page .order-table td {
        padding: .5rem 1rem;
        vertical-align: top;
        page-break-inside: avoid;
    }

    #order-page .order-table tr.bordered td {
        border-bottom: 1px solid #ddd;
    }

    #order-page .flex {
        display: flex;
    }

    #order-page .flex-col {
        flex-direction: column;
    }

    #order-page .grow {
        flex-grow: 1;
    }

    #order-page .items-start {
        align-items: flex-start;
    }

    #order-page .items-end {
        align-items: flex-end;
    }

    #order-page .justify-content-end {
        justify-content: end;
    }

    #order-page .text-right {
        text-align: right;
    }

    #order-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }

    #order-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }

    #order-page .muted {
        color: #777;
    }

    #order-page .title {
        font-size: 30pt;
    }

    #order-page .subtitle {
        color: #666;
        font-weight: bold;
    }

    #order-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="order-page">
    <table class="w-full">
        <tbody><tr><td>
            <div class="flex items-start w-full">
                <div class="grow">
                    <h1 class="title">PURCHASE ORDER</h1>
                    <h2 class="subtitle"># {{ $record->order_number }}</h2>
                </div>
                @php
                    $company = app(App\Settings\CompanySettings::class);
                @endphp
                <div class="flex flex-col items-end text-right grow">
                    @inlinedImage(asset('images/logo-wide.svg'), 'logo')
                    <p><strong>{{ $company->name }}</strong></p>
                    <div class="muted">
                        {!! nl2br($company->address) !!}<br>
                        {{ $company->email }} | {{ $company->phone }}<br>
                        {{ $company->website }}
                    </div>
                </div>
            </div>

            <div class="flex" style="padding: 0.5cm 0;">
                <div style="width: 25%; line-height: 200%;">
                    <p class="muted">Order Date :</p>
                </div>
                <div style="width: 35%; line-height: 200%;">
                    <p>{{ $record->order_date->format('j M Y') }}</p>
                </div>
                <div style="width: 40%">
                    <p class="muted">Vendor</p>
                    @if ($record->vendor)
                        <p><strong>{{ $record->vendor->company_name }}</strong></p>
                        @if ($record->vendor->contact_name || $record->vendor->contact_phone)
                        <div>
                            {{ $record->vendor->contact_name }}
                            @if ($record->vendor->contact_phone)
                            ({{ $record->vendor->contact_phone }})
                            @endif
                        </div>
                        @endif
                        {{-- <div>{{ $record->vendor->address }}</div> --}}
                    @else
                        <p><strong>[vendor dihapus]</strong></p>
                    @endif
                </div>
            </div>

            <div style="padding: 0.5cm 0;">
                <p class="muted">Subject :</p>
                <p>{{ $record->subject }}</p>
            </div>

            <table class="w-full order-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item & Description</th>
                        <th class="text-right">Qty</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">VAT</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($record->items as $item)
                        <tr class="bordered">
                            <td>{{ $loop->index + 1 }}</td>
                            <td>
                                <p>{{ $item->name }}</p>
                                <div class="item-description">{!! nl2br($item->description) !!}</div>
                            </td>
                            <td class="text-right">{{ $item->quantity }}</td>
                            <td class="text-right currency">{{ number_format($item->unit_price, 2, '.', ',') }}</td>
                            <td class="text-right currency">{{ $item->vat }}%</td>
                            <td class="text-right currency">
                                {{ number_format($item->unit_price * $item->quantity * (1 + $item->vat / 100), 2, '.', ',') }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div class="flex justify-content-end">
                <div style="width: 60%">
                    <table class="w-full order-table">
                        <tbody>
                            <tr style="background-color: #f2f2f2">
                                <td class="text-right"><strong>Total</strong></td>
                                <td class="text-right currency">
                                    <strong>{{ money($record->total, $record->currency_code, true) }}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="margin-top: 0.75cm;">
                <p class="muted">Notes</p>
                <div style="font-size: 9pt; margin-top: 0.25cm;">{!! nl2br($record->notes) !!}</div>
            </div>
        </td></tr></tbody>
        <tfoot><tr><td>
            <div class="footer-space">&nbsp;</div>
        </td></tr></tfoot>
    </table>

    {{-- <div class="page-footer" style="margin-top: 0.5cm;">
        <p>Thank you for choosing {{ $company->name }} for hotel reservation and handling service.</p>
    </div> --}}
</div>
