@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Profit & Loss Statement</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
    </div>

    {{-- Revenues --}}
    <div class="space-y-4">
        <div class="text-lg font-semibold">Revenues</div>
        @php
            $totalRevenue = 0;
        @endphp
        @foreach($data['revenues'] as $group => $accounts)
            <div class="space-y-2">
                <div class="font-medium">{{ $group }}</div>
                @foreach($accounts as $account)
                    @php
                        $totalRevenue += $account['amount'];
                    @endphp
                    <div class="flex justify-between pl-4">
                        <span>{{ $account['name'] }}</span>
                        <span class="tabular-nums">{{ money($data['exchange_rate'] * $account['amount'], $data['currency_code'], true) }}</span>
                    </div>
                @endforeach
            </div>
        @endforeach
        <div class="flex justify-between pt-2 font-medium border-t">
            <span>Total Revenue</span>
            <span class="tabular-nums">{{ money($data['exchange_rate'] * $totalRevenue, $data['currency_code'], true) }}</span>
        </div>
    </div>

    {{-- Expenses --}}
    <div class="space-y-4">
        <div class="text-lg font-semibold">Expenses</div>
        @php
            $totalExpense = 0;
        @endphp
        @foreach($data['expenses'] as $group => $accounts)
            <div class="space-y-2">
                <div class="font-medium">{{ $group }}</div>
                @foreach($accounts as $account)
                    @php
                        $totalExpense += $account['amount'];
                    @endphp
                    <div class="flex justify-between pl-4">
                        <span>{{ $account['name'] }}</span>
                        <span class="tabular-nums">{{ money($data['exchange_rate'] * $account['amount'], $data['currency_code'], true) }}</span>
                    </div>
                @endforeach
            </div>
        @endforeach
        <div class="flex justify-between pt-2 font-medium border-t">
            <span>Total Expenses</span>
            <span class="tabular-nums">{{ money($data['exchange_rate'] * $totalExpense, $data['currency_code'], true) }}</span>
        </div>
    </div>

    {{-- Net Income --}}
    <div class="flex justify-between pt-4 text-lg font-bold border-t-2">
        <span>Net Income</span>
        <span class="tabular-nums">{{ money($data['exchange_rate'] * ($totalRevenue - $totalExpense), $data['currency_code'], true) }}</span>
    </div>
</div>