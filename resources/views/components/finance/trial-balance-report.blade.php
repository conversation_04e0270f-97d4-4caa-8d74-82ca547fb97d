@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Trial Balance</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
    </div>

    <div class="space-y-4">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b-2">
                        <th class="py-2 text-left">Account Code</th>
                        <th class="py-2 text-left">Account Name</th>
                        <th class="py-2 text-right">Debit</th>
                        <th class="py-2 text-right">Credit</th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $totalDebit = 0;
                        $totalCredit = 0;

                        // Group accounts by category
                        $groupedAccounts = [];
                        $categoryGroups = \App\Enums\Finance\AccountCategory::groupedCases();

                        // Initialize group totals
                        $groupTotals = [];
                        foreach (array_keys($categoryGroups) as $groupName) {
                            $groupTotals[$groupName] = [
                                'debit' => 0,
                                'credit' => 0
                            ];
                        }

                        // Organize accounts into groups
                        foreach ($data['accounts'] as $account) {
                            $category = $account['category'] ?? null;
                            $groupFound = false;

                            if ($category) {
                                foreach ($categoryGroups as $groupName => $categories) {
                                    foreach ($categories as $categoryEnum) {
                                        if ($category === $categoryEnum->value) {
                                            if (!isset($groupedAccounts[$groupName])) {
                                                $groupedAccounts[$groupName] = [];
                                            }
                                            $groupedAccounts[$groupName][] = $account;
                                            $groupTotals[$groupName]['debit'] += $account['debit'];
                                            $groupTotals[$groupName]['credit'] += $account['credit'];
                                            $groupFound = true;
                                            break 2;
                                        }
                                    }
                                }
                            }

                            // If no group found, put in "Other" category
                            if (!$groupFound) {
                                if (!isset($groupedAccounts['Other'])) {
                                    $groupedAccounts['Other'] = [];
                                    $groupTotals['Other'] = ['debit' => 0, 'credit' => 0];
                                }
                                $groupedAccounts['Other'][] = $account;
                                $groupTotals['Other']['debit'] += $account['debit'];
                                $groupTotals['Other']['credit'] += $account['credit'];
                            }

                            $totalDebit += $account['debit'];
                            $totalCredit += $account['credit'];
                        }
                    @endphp

                    @foreach($groupedAccounts as $groupName => $accounts)
                        <tr class="font-semibold bg-gray-100">
                            <td class="px-2 py-2" colspan="4">{{ $groupName }}</td>
                        </tr>

                        @foreach($accounts as $account)
                            @php
                                // Use the balance field to determine which column to display the amount in
                                $category = \App\Enums\Finance\AccountCategory::from($account['category']);
                                $balance = $account['balance'] * $data['exchange_rate'];
                                $displayDebit = $balance > 0 && $category->isNormalDebitBalance() ? abs($balance) :
                                              ($balance < 0 && !$category->isNormalDebitBalance() ? abs($balance) : 0);
                                $displayCredit = $balance > 0 && !$category->isNormalDebitBalance() ? abs($balance) :
                                               ($balance < 0 && $category->isNormalDebitBalance() ? abs($balance) : 0);
                            @endphp
                            <tr class="border-b">
                                <td class="py-2 pl-4">{{ $account['code'] }}</td>
                                <td class="py-2">{{ $account['name'] }}</td>
                                <td class="py-2 text-right">{{ number_format($displayDebit, 2) }}</td>
                                <td class="py-2 text-right">{{ number_format($displayCredit, 2) }}</td>
                            </tr>
                        @endforeach

                        <tr class="font-medium border-b">
                            <td class="py-2 pl-4" colspan="2">Subtotal {{ $groupName }}</td>
                            <td class="py-2 text-right">{{ number_format($groupTotals[$groupName]['debit'] * $data['exchange_rate'], 2) }}</td>
                            <td class="py-2 text-right">{{ number_format($groupTotals[$groupName]['credit'] * $data['exchange_rate'], 2) }}</td>
                        </tr>
                    @endforeach

                    <tr class="font-bold border-t-2">
                        <td class="py-2" colspan="2">Total</td>
                        <td class="py-2 text-right">{{ number_format($totalDebit * $data['exchange_rate'], 2) }}</td>
                        <td class="py-2 text-right">{{ number_format($totalCredit * $data['exchange_rate'], 2) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
