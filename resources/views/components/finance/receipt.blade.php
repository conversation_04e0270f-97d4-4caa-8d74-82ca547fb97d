@props([
    'payment' => null,
    'isInline' => false
])

@php
    $invoice = $payment->invoice;
    $payments = $invoice->payments()->with(['invoice', 'created_by'])->orderBy('paid_at')->get();
    $company = app(App\Settings\CompanySettings::class);
@endphp

@if (!$isInline)
<style>
    html {
        color: var(--arm-blue);
        font-size: 9pt;
    }

    #invoice-page .text-sm {
        font-size: 9pt;
        line-height: 1.25rem;
    }

    #invoice-page .page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    @media screen {
        body { position: relative; }
        #invoice-page .page-footer {
            left: 1cm;
            right: 1cm;
            padding-bottom: 1cm;
            position: absolute;
        }
    }
</style>
@endif
<style>
    :root { --arm-blue: #1a2b48; }
    #invoice-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }
    #invoice-page .w-full { width: 100%; }
    #invoice-page .font-bold { font-weight: bold; }
    #invoice-page table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: .5rem 1rem;
    }
    #invoice-page table td {
        padding: .5rem 1rem;
        vertical-align: top;
    }
    #invoice-page table tr.bordered td { border-bottom: 1px solid #ddd; }
    #invoice-page .flex { display: flex; }
    #invoice-page .flex-col { flex-direction: column; }
    #invoice-page .grow { flex-grow: 1; }
    #invoice-page .items-start { align-items: flex-start; }
    #invoice-page .items-end { align-items: flex-end; }
    #invoice-page .justify-content-end { justify-content: end; }
    #invoice-page .text-right { text-align: right; }
    #invoice-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }
    #invoice-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }
    #invoice-page .muted { color: #777; }
    #invoice-page .title { font-size: 36pt; }
    #invoice-page .subtitle {
        color: #666;
        font-weight: bold;
    }
    #invoice-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="invoice-page">
    <div class="flex items-start w-full">
        <div class="grow">
            <h1 class="title">RECEIPT</h1>
            <h2 class="subtitle">Payment# {{ str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</h2>
        </div>
        <div class="flex flex-col items-end text-right grow">
            @inlinedImage(asset('images/logo-wide.svg'), 'logo')
            <p><strong>{{ $company->name }}</strong></p>
            <div class="muted">
                {!! nl2br($company->address) !!}<br>
                {{ $company->email }} | {{ $company->phone }}<br>
                {{ $company->website }}
            </div>
        </div>
    </div>

    <div class="flex" style="padding: 0.5cm 0;">
        <div style="width: 25%; line-height: 200%;">
            <p class="muted">Invoice# :</p>
            <p class="muted">Payment Date :</p>
            <p class="muted">Cashier :</p>
        </div>
        <div style="width: 35%; line-height: 200%;">
            <p>{{ $payment->invoice->invoice_number }}</p>
            <p>{{ $payment->paid_at->format('j M Y') }}</p>
            <p>{{ $payment->created_by?->name }}</p>
        </div>
        <div style="width: 40%">
            <p class="muted">Received From</p>
            <p><strong>{{ $payment->invoice->customer->name }}</strong></p>
            @if ($payment->invoice->customer->owner_name || $payment->invoice->customer->phone)
            <div>
                {{ $payment->invoice->customer->owner_name }}
                @if ($payment->invoice->customer->phone)
                ({{ $payment->invoice->customer->phone }})
                @endif
            </div>
            @endif
            <div>{{ $payment->invoice->customer->address }}</div>
        </div>
    </div>

    <table class="w-full">
        <thead>
            <tr>
                <th class="text-left">Date</th>
                <th class="text-left">Item & Description</th>
                <th class="text-left">Via</th>
                <th class="text-left">Cashier</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($payments as $p)
            <tr class="bordered">
                <td>{{ $p->paid_at->format('j M Y') }}</td>
                <td>
                    <p>Payment# {{ str_pad($p->id, 6, '0', STR_PAD_LEFT) }}</p>
                    <div class="item-description">{{ $p->description }}</div>
                </td>
                <td>
                    <p>{{ $p->payment_method->getLabel() }}</p>
                    @if ($p->payment_method == App\Enums\PaymentMethod::BankTransfer)
                    <p class="text-sm font-bold">{{ $p->cash_account->name }}</p>
                    <p class="text-sm muted">{{ $p->cash_account->description }}</p>
                    @endif
                </td>
                <td>{{ $p->created_by?->name }}</td>
                <td class="text-right currency">
                    <p>{{ money($p->amount, $p->currency_code, true) }}</p>
                    @if ($p->currency_code != $invoice->currency_code)
                    <p class="text-sm muted">{{ money($p->amount_converted, $invoice->currency_code, true) }}</p>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
    <div class="flex justify-content-end">
        <div style="width: 60%">
            <table class="w-full">
                <tbody>
                    <tr>
                        <td class="text-right"><strong>Total Invoice</strong></td>
                        <td class="text-right currency">
                            <strong>{{ money($invoice->total, $invoice->currency_code, true) }}</strong>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-right"><strong>Total Payments</strong></td>
                        <td class="text-right currency">
                            <strong>{{ money($invoice->paid, $invoice->currency_code, true) }}</strong>
                        </td>
                    </tr>
                    <tr style="background-color: #f2f2f2">
                        <td class="text-right"><strong>Balance</strong></td>
                        <td class="text-right currency">
                            <strong>{{ money($invoice->total - $invoice->paid, $invoice->currency_code, true) }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="page-footer" style="margin-top: 0.5cm;">
        <p>Thank you for your payment.</p>
    </div>
</div>
