@props([
    'refund' => null,
    'isInline' => false
])

@if (!$isInline)
<style>
    html {
        color: var(--arm-blue);
        font-size: 9pt;
    }

    #invoice-page .text-sm {
        font-size: 9pt;
        line-height: 1.25rem;
    }

    #invoice-page .page-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    @media screen {
        body {
            position: relative;
        }

        #invoice-page .page-footer {
            left: 1cm;
            right: 1cm;
            padding-bottom: 1cm;
            position: absolute;
        }
    }
</style>
@endif
<style>
    :root {
        --arm-blue: #1a2b48;
    }

    #invoice-page .page-footer {
        border-top: 1px solid #ddd;
        font-size: 8pt;
        font-style: italic;
        padding-top: 0.1cm;
    }

    #invoice-page .w-full {
        width: 100%;
    }

    #invoice-page .font-bold {
        font-weight: bold;
    }

    #invoice-page table th {
        background-color: var(--arm-blue);
        color: white;
        font-weight: normal;
        padding: .5rem 1rem;
    }

    #invoice-page table td {
        padding: .5rem 1rem;
        vertical-align: top;
    }

    #invoice-page table tr.bordered td {
        border-bottom: 1px solid #ddd;
    }

    #invoice-page .flex {
        display: flex;
    }

    #invoice-page .flex-col {
        flex-direction: column;
    }

    #invoice-page .grow {
        flex-grow: 1;
    }

    #invoice-page .items-start {
        align-items: flex-start;
    }

    #invoice-page .items-end {
        align-items: flex-end;
    }

    #invoice-page .justify-content-end {
        justify-content: end;
    }

    #invoice-page .text-right {
        text-align: right;
    }

    #invoice-page .logo {
        display: block;
        height: 1.5cm;
        width: auto;
    }

    #invoice-page .currency {
        font-variant-numeric: tabular-nums;
        white-space: nowrap;
    }

    #invoice-page .muted {
        color: #777;
    }

    #invoice-page .title {
        font-size: 36pt;
    }

    #invoice-page .subtitle {
        color: #666;
        font-weight: bold;
    }

    #invoice-page .item-description {
        color: #777;
        font-size: 10pt;
    }
</style>
<div id="invoice-page">
    <div class="flex items-start w-full">
        <div class="grow">
            <h1 class="title">RECEIPT</h1>
            <h2 class="subtitle">Refund# {{ str_pad($refund->id, 6, '0', STR_PAD_LEFT) }}</h2>
        </div>
        @php
            $company = app(App\Settings\CompanySettings::class);
        @endphp
        <div class="flex flex-col items-end text-right grow">
            @inlinedImage(asset('images/logo-wide.svg'), 'logo')
            <p><strong>{{ $company->name }}</strong></p>
            <div class="muted">
                {!! nl2br($company->address) !!}<br>
                {{ $company->email }} | {{ $company->phone }}<br>
                {{ $company->website }}
            </div>
        </div>
    </div>

    <div class="flex" style="padding: 0.5cm 0;">
        <div style="width: 25%; line-height: 200%;">
            <p class="muted">Refund Date :</p>
            <p class="muted">Cashier :</p>
            <p class="muted">Via :</p>
        </div>
        <div style="width: 35%; line-height: 200%;">
            <p>{{ $refund->refunded_at->format('j M Y') }}</p>
            <p>{{ $refund->created_by?->name }}</p>
            <p>{{ $refund->payment_method->getLabel() }}</p>
            @if ($refund->payment_method == App\Enums\PaymentMethod::BankTransfer)
            <p class="text-sm font-bold">{{ $refund->cash_account->name }}</p>
            @endif
        </div>
        <div style="width: 40%">
            <p class="muted">Refunded To</p>
            <p><strong>{{ $refund->invoice->customer->name }}</strong></p>
            @if ($refund->invoice->customer->owner_name || $refund->invoice->customer->phone)
            <div>
                {{ $refund->invoice->customer->owner_name }}
                @if ($refund->invoice->customer->phone)
                ({{ $refund->invoice->customer->phone }})
                @endif
            </div>
            @endif
            <div>{{ $refund->invoice->customer->address }}</div>
        </div>
    </div>

    <table class="w-full">
        <thead>
            <tr>
                <th class="text-left">Date</th>
                <th class="text-left">Item & Description</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr class="bordered">
                <td>{{ $refund->refunded_at->format('j M Y') }}</td>
                <td>
                    <p>Invoice# {{ $refund->invoice->invoice_number }}</p>
                    <div class="item-description">{{ nl2br($refund->invoice->subject) }}</div>
                </td>
                <td class="text-right currency">{{ money($refund->amount, $refund->currency_code, true) }}</td>
            </tr>
        </tbody>
    </table>
    <div class="flex justify-content-end">
        <div style="width: 60%">
            <table class="w-full">
                <tbody>
                    <tr style="background-color: #f2f2f2">
                        <td class="text-right"><strong>Total</strong></td>
                        <td class="text-right currency">
                            <strong>{{ money($refund->amount, $refund->currency_code, true) }}</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="page-footer" style="margin-top: 0.5cm;">
        {{-- <p>Thank you for your payment.</p> --}}
    </div>
</div>
