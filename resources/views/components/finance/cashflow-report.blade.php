@props(['data', 'period'])

<div class="space-y-6">
    <div class="space-y-2 text-center">
        <h1 class="text-2xl font-bold">Cash Flow Statement</h1>
        <p>Period: {{ \Carbon\Carbon::parse($period['start'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($period['end'])->format('d M Y') }}</p>
    </div>

    <div class="space-y-6">
        {{-- Operating Activities --}}
        <div class="space-y-4">
            <h2 class="text-lg font-semibold">Operating Activities</h2>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b-2">
                            <th class="py-2 text-left">Date</th>
                            <th class="py-2 text-left">Description</th>
                            <th class="py-2 text-left">Account</th>
                            <th class="py-2 text-right">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data['operating']['items'] as $item)
                            <tr class="border-b">
                                <td class="py-2">{{ \Carbon\Carbon::parse($item['date'])->format('d/m/Y') }}</td>
                                <td class="py-2">{{ $item['details'] }}</td>
                                <td class="py-2">{{ $item['account'] }}</td>
                                <td class="py-2 text-right {{ $item['amount'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                                    {{ number_format($item['amount'] * $data['exchange_rate'], 2) }}
                                </td>
                            </tr>
                        @endforeach
                        <tr class="font-bold border-t-2">
                            <td class="py-2" colspan="3">Net Cash from Operating Activities</td>
                            <td class="py-2 text-right {{ $data['operating']['total'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                                {{ number_format($data['operating']['total'] * $data['exchange_rate'], 2) }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        {{-- Investing Activities --}}
        <div class="space-y-4">
            <h2 class="text-lg font-semibold">Investing Activities</h2>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b-2">
                            <th class="py-2 text-left">Date</th>
                            <th class="py-2 text-left">Description</th>
                            <th class="py-2 text-left">Account</th>
                            <th class="py-2 text-right">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data['investing']['items'] as $item)
                            <tr class="border-b">
                                <td class="py-2">{{ \Carbon\Carbon::parse($item['date'])->format('d/m/Y') }}</td>
                                <td class="py-2">{{ $item['details'] }}</td>
                                <td class="py-2">{{ $item['account'] }}</td>
                                <td class="py-2 text-right {{ $item['amount'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                                    {{ number_format($item['amount'] * $data['exchange_rate'], 2) }}
                                </td>
                            </tr>
                        @endforeach
                        <tr class="font-bold border-t-2">
                            <td class="py-2" colspan="3">Net Cash from Investing Activities</td>
                            <td class="py-2 text-right {{ $data['investing']['total'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                                {{ number_format($data['investing']['total'] * $data['exchange_rate'], 2) }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        {{-- Financing Activities --}}
        <div class="space-y-4">
            <h2 class="text-lg font-semibold">Financing Activities</h2>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b-2">
                            <th class="py-2 text-left">Date</th>
                            <th class="py-2 text-left">Description</th>
                            <th class="py-2 text-left">Account</th>
                            <th class="py-2 text-right">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data['financing']['items'] as $item)
                            <tr class="border-b">
                                <td class="py-2">{{ \Carbon\Carbon::parse($item['date'])->format('d/m/Y') }}</td>
                                <td class="py-2">{{ $item['details'] }}</td>
                                <td class="py-2">{{ $item['account'] }}</td>
                                <td class="py-2 text-right {{ $item['amount'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                                    {{ number_format($item['amount'] * $data['exchange_rate'], 2) }}
                                </td>
                            </tr>
                        @endforeach
                        <tr class="font-bold border-t-2">
                            <td class="py-2" colspan="3">Net Cash from Financing Activities</td>
                            <td class="py-2 text-right {{ $data['financing']['total'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                                {{ number_format($data['financing']['total'] * $data['exchange_rate'], 2) }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        {{-- Net Change in Cash --}}
        <div class="pt-4 border-t-4">
            <table class="w-full">
                <tr class="text-xl font-bold">
                    <td class="py-2">Net Change in Cash</td>
                    <td class="py-2 text-right {{ $data['net_change'] > 0 ? 'text-success-600' : 'text-danger-600' }}">
                        {{ number_format($data['net_change'] * $data['exchange_rate'], 2) }}
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
