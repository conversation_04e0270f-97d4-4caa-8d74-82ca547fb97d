<div>
    <div class="flex items-start justify-between gap-2">
        <p>{{ $record->hotel->fullname }}</p>
        <p class="text-gray-500 dark:text-gray-400">{{ $record->check_in?->format('d-M-y') ?? '?' }} -
            {{ $record->check_out?->format('d-M-y') ?? '?' }}</p>
    </div>
    <div class="flex items-center gap-2 mt-2">
        @if ($record->room_single_count)
            <x-filament::badge size="sm">{{ $record->room_single_count }} single</x-filament::badge>
        @endif
        @if ($record->room_double_count)
            <x-filament::badge size="sm">{{ $record->room_double_count }} double</x-filament::badge>
        @endif
        @if ($record->room_triple_count)
            <x-filament::badge size="sm">{{ $record->room_triple_count }} triple</x-filament::badge>
        @endif
        @if ($record->room_quad_count)
            <x-filament::badge size="sm">{{ $record->room_quad_count }} quad</x-filament::badge>
        @endif
        @if ($record->room_quint_count)
            <x-filament::badge size="sm">{{ $record->room_quint_count }} quint</x-filament::badge>
        @endif
    </div>
</div>
