<div class="fixed z-50 flex items-center gap-3 px-3 py-2 transform -translate-x-1/2 bg-orange-400 border border-gray-200 rounded-lg shadow-lg bottom-3 left-1/2"
    x-data="{
        show: false,
        form: $el.closest('form'),
        maybeShow(e) {
            if (e.target.closest('form') === this.form) {
                this.show = true;
            }
        }
    }" x-show="show" x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 transform translate-y-4"
    x-transition:enter-end="opacity-100 transform translate-y-0"
    x-transition:leave="transition ease-in duration-300"
    x-transition:leave-start="opacity-100 transform translate-y-0"
    x-transition:leave-end="opacity-0 transform translate-y-4"
    @input.window="maybeShow"
    @change.window="maybeShow"
>
    <button @click.prevent="show = false">
        <x-heroicon-s-x-circle class="text-white size-5" />
    </button>
    <p class="text-sm font-medium text-white">Unsaved changes</p>
    @if (method_exists($this, 'getSaveFormAction'))
        {{ $this->getSaveFormAction()->label('Save')->size('xs') }}
    @elseif (method_exists($this, 'getCreateFormAction'))
        {{ $this->getCreateFormAction()->label('Create')->size('xs') }}
    @endif
</div>
