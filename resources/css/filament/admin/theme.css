@import '../../../../vendor/filament/filament/resources/css/theme.css';
/* @import '../../../../vendor/saade/filament-fullcalendar/resources/css/filament-fullcalendar.css'; */
@import '../../../../vendor/malzariey/filament-daterangepicker-filter/resources/css/filament-daterangepicker.css';
@import '../../../../vendor/awcodes/filament-table-repeater/resources/css/plugin.css';

@config './tailwind.config.js';

.fi-badge .truncate {
    overflow: unset;
    text-overflow: unset;
    white-space: unset;
}

.fc-dayGridDay-view .fc-daygrid-event {
    white-space: normal !important;
}

.filament-tables-row:nth-child(odd) {
    @apply bg-gray-50;
}

.fi-page-sub-navigation-tabs {
    @apply !flex;
}
.fi-page-sub-navigation-select {
    @apply !hidden;
}

.fi-print-preview .fi-section-content {
    padding: 0;
}

@media only screen {
    .page-break {
        @apply border-t -mx-[1cm] my-[1cm];
    }
}

.fi-sidebar-nav-groups {
    @apply gap-y-3;
}

/* AI Chat Assistant Custom Animations */
@layer utilities {
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    .animate-slide-up {
        animation: slideUp 0.3s ease-out;
    }

    .animate-typing {
        animation: typing 1.5s infinite;
    }

    .animate-pulse-soft {
        animation: pulseSoft 2s infinite;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes typing {
    0%,
    60%,
    100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

@keyframes pulseSoft {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

/* Chat message animations */
.message-container {
    animation: messageSlideIn 0.4s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Smooth scrolling for chat messages */
#chat-messages {
    scroll-behavior: smooth;
}

/* Custom scrollbar for chat messages */
#chat-messages::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Mobile responsiveness for AI Chat */
@media (max-width: 640px) {
    .ai-chat-panel {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        height: 100vh !important;
        max-height: 100vh !important;
        border-radius: 0 !important;
        z-index: 9999 !important;
    }

    .ai-chat-toggle {
        bottom: 1rem !important;
        right: 1rem !important;
    }

    .ai-chat-messages {
        height: calc(100vh - 140px) !important;
    }
}

/* Loading states */
.loading-dots {
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.loading-dots div {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dots div:nth-child(1) {
    animation-delay: -0.32s;
}
.loading-dots div:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loading-bounce {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}
