<?php

namespace App\Models;

use App\Services\ReminderService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\Relation;

class ReminderSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'reminder_id',
        'model_type',
        'model_id',
        'scheduled_at',
        'sent_at',
    ];

    protected function casts()
    {
        return [
            'scheduled_at' => 'datetime',
            'sent_at' => 'datetime',
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            if (! $model->model_type) {
                $reminder = Reminder::find($model->reminder_id);
                if ($reminder) {
                    $modelClass = app(ReminderService::class)->getModelClass($reminder->reminder_type);
                    $model->model_type = Relation::getMorphAlias($modelClass);
                }
            }
        });
    }

    public function reminder(): BelongsTo
    {
        return $this->belongsTo(Reminder::class);
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function getMessageContent(): string
    {
        return app(ReminderService::class)->generateContent($this->reminder, $this->model);
    }

    public function getTagsValues(): array
    {
        return app(ReminderService::class)->getTagsValues($this->reminder->reminder_type, $this->model);
    }
}
