<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{
    protected $fillable = ['code', 'name', 'exchange_rate'];

    protected $casts = [
        'exchange_rate' => 'float',
    ];

    public static function getExchangeRate(string $code)
    {
        return self::query()->where('code', $code)->value('exchange_rate');
    }

    public static function getOptions()
    {
        return self::query()->pluck('name', 'code');
    }
}
