<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @deprecated 2025.02.17
 */
class Institution extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
    ];

    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(GroupCash::class);
    }

    public function delete()
    {
        if ($this->groups()->exists()) {
            return null;
        }

        return parent::delete();
    }
}
