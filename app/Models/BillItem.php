<?php

namespace App\Models;

use App\Models\Finance\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class BillItem extends Model implements Sortable
{
    use HasFactory;
    use SortableTrait;

    public $sortable = [
        'order_column_name' => 'order_column',
        'sort_when_creating' => true,
    ];

    protected $fillable = [
        'bill_id',
        'group_id',

        'product_id',

        'name',
        'description',

        'quantity',
        'unit_price',
        'vat',

        'order_column',
    ];

    protected function casts()
    {
        return [
            'unit_price' => 'float',
            'vat' => 'float',
        ];
    }

    public function buildSortQuery(): Builder
    {
        return static::query()->where('bill_id', $this->bill_id);
    }

    public function bill(): BelongsTo
    {
        return $this->belongsTo(Bill::class, 'bill_id');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
