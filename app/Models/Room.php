<?php

namespace App\Models;

use App\Enums\RoomCapacity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Room extends Model
{
    use HasFactory;

    protected $fillable = [
        'group_id',
        'name',
        'number',
        'capacity',
        'meta', /** @example ['room_numbers' => [['group_hotel_id' => 1, 'room_number' => '101'], ['group_hotel_id' => 2, 'room_number' => '201']]] */
        'mutawifs',
    ];

    protected function casts()
    {
        return [
            'capacity' => RoomCapacity::class,
            'meta' => 'array',
            'mutawifs' => 'array',
        ];
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function groupPilgrims(): HasMany
    {
        return $this->hasMany(GroupPilgrim::class);
    }

    public function pilgrims(): BelongsToMany
    {
        return $this->belongsToMany(Pilgrim::class, 'group_pilgrim')->withTimestamps()
            ->withPivot(['group_id', 'is_tour_leader']);
    }

    /**
     * Get the combination of hotels where this room has room numbers assigned
     *
     * @return array Array of hotel IDs where room numbers are assigned
     */
    public function getAssignedHotelIds(): array
    {
        if (empty($this->meta['group_hotel_ids'])) {
            return $this->group->group_hotels()
                ->orderBy('check_in')
                ->pluck('id')
                ->toArray();
        }

        return $this->group->group_hotels()
            ->whereIn('id', $this->meta['group_hotel_ids'])
            ->orderBy('check_in')
            ->pluck('id')
            ->toArray();
    }

    public function getRoomNumberForHotel(string|int $groupHotelId): ?string
    {
        if (empty($this->meta['room_numbers'])) {
            return null;
        }

        $roomInfo = collect($this->meta['room_numbers'])
            ->firstWhere('group_hotel_id', $groupHotelId);

        return $roomInfo['room_number'] ?? null;
    }
}
