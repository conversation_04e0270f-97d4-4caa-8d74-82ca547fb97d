<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Meta extends Model
{
    public $timestamps = false;

    protected $guarded = ['id', 'metable_type', 'metable_id'];

    protected $cachedValue;

    public function metable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getValueAttribute()
    {
        if (!isset($this->cachedValue)) {
            $this->cachedValue = maybe_unserialize($this->attributes['value']);
        }

        return $this->cachedValue;
    }

    public function setValueAttribute($value): void
    {
        $this->attributes['value'] = maybe_serialize($value);

        $this->cachedValue = null;
    }

    public function getRawValue(): string
    {
        return $this->attributes['value'];
    }
}
