<?php

namespace App\Models\Vendors;

use App\Enums\VendorType;
use App\Models\GroupCash;
use App\Models\GroupHotel;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class HotelBroker extends Vendor
{
    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->vendor_type = VendorType::Hotel;
        });
    }

    public function newQuery()
    {
        return parent::newQuery()->where('vendor_type', VendorType::Hotel);
    }

    public function getContactDetailsAttribute()
    {
        return $this->contact_name . ' (' . $this->contact_phone . ')';
    }

    public function getFullNameAttribute()
    {
        return $this->company_name . ' (' . $this->contact_name . ')';
    }

    public function scopeWithFullname(Builder $query): void
    {
        $query->select([
            '*',
            DB::raw("CONCAT(company_name, ' (', IFNULL(contact_name, '-'), ')') AS fullname"),
        ]);
    }

    public function group_hotels(): HasMany
    {
        return $this->hasMany(GroupHotel::class, 'broker_id');
    }

    public function group_cashes(): HasMany
    {
        return $this->hasMany(GroupCash::class, 'hotel_broker_id');
    }

    public function delete()
    {
        if ($this->group_hotels()->exists()) {
            return null;
        }

        return parent::delete();
    }
}
