<?php

namespace App\Models\Vendors;

use App\Enums\VendorType;
use App\Models\Vendor;

class SnackHandler extends Vendor
{
    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->vendor_type = VendorType::Snack;
        });
    }

    public function newQuery()
    {
        return parent::newQuery()->where('vendor_type', VendorType::Snack);
    }
}
