<?php

namespace App\Models\Vendors;

use App\Enums\VendorType;
use App\Models\Airport;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

class AirportHandler extends Vendor
{
    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->vendor_type = VendorType::AirportHandler;
        });
    }

    public function newQuery()
    {
        return parent::newQuery()->where('vendor_type', VendorType::AirportHandler);
    }

    public function airports(): BelongsToMany
    {
        return $this->belongsToMany(Airport::class, 'airport_handler', 'handler_id', 'airport_code', 'id', 'code');
    }

    public function delete()
    {
        DB::table('airport_handler')->where('handler_id', $this->id)->delete();

        return parent::delete();
    }
}
