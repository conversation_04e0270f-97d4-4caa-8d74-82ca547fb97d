<?php

namespace App\Models;

use App\Models\Vendors\AirportHandler;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;

class GroupFlight extends Model
{
    use HasFactory;

    protected $fillable = [
        'airline_id',
        'group_id',
        'type', // arrival | departure

        'flight_number',
        'pax',

        'from',
        'to',

        'date_etd',
        'date_eta',

        'via_number',
        'via',
        'via_eta',
        'via_etd',

        'handler_id',

        'meta',
    ];

    protected $casts = [
        'date_etd' => 'datetime',
        'date_eta' => 'datetime',
        'via_eta' => 'datetime',
        'via_etd' => 'datetime',

        'meta' => 'array',
    ];

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function handler(): BelongsTo
    {
        return $this->belongsTo(AirportHandler::class);
    }

    public function getTotalPaxAttribute()
    {
        return $this->pax ?? $this->group->total_pax;
    }

    public function getEtdAttribute()
    {
        return $this->date_etd->format('H:i');
    }

    public function getEtaAttribute()
    {
        $etd = $this->date_etd->startOfDay();
        $eta = $this->date_eta->startOfDay();
        $diff = $etd->diffInDays($eta);
        $eta = $this->date_eta->format('H:i');

        if ($diff > 0) {
            return $eta . '+' . $diff;
        }

        return $eta;
    }

    public function getEtdViaAttribute()
    {
        if (! $this->via_etd) {
            return '-';
        }

        $etd = $this->date_etd->startOfDay();
        $etd_via = $this->via_etd->startOfDay();
        $diff = $etd->diffInDays($etd_via);
        $etd_via = $this->via_etd->format('H:i');

        if ($diff > 0) {
            return $etd_via . '+' . $diff;
        }

        return $etd_via;
    }

    public function getEtaViaAttribute()
    {
        if (! $this->via_eta) {
            return '-';
        }

        $etd = $this->date_etd->startOfDay();
        $eta_via = $this->via_eta->startOfDay();
        $diff = $etd->diffInDays($eta_via);
        $eta_via = $this->via_eta->format('H:i');

        if ($diff > 0) {
            return $eta_via . '+' . $diff;
        }

        return $eta_via;
    }

    public function mealbox(): Attribute
    {
        return Attribute::get(fn () => $this->meta['mealbox'] ?? null);
    }

    /**
     * @return Collection<User>
     */
    public function getUserHandlers(): ?Collection
    {
        return $this->handler?->users;
    }
}
