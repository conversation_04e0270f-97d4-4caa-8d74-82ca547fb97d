<?php

namespace App\Models;

use App\Enums\NotificationType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Reminder extends Model
{
    use HasFactory;

    protected $fillable = [
        'reminder_type',
        'subject',
        'notification_type',
        'template',
        'interval',
        'time',
        'active',
        'additional_data',
    ];

    protected function casts()
    {
        return [
            'notification_type' => NotificationType::class,
            'time' => 'datetime',
            'active' => 'bool',
            'additional_data' => 'array',
        ];
    }

    public function recipients(): HasMany
    {
        return $this->hasMany(ReminderRecipient::class);
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(ReminderSchedule::class);
    }

    public function scopeIsActive(Builder $query): Builder
    {
        return $query->where('active', true);
    }
}
