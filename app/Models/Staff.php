<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @deprecated
 */
class Staff extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'phone',
    ];

    public function user(): HasOne
    {
        return $this->hasOne(User::class);
    }
}
