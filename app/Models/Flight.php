<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @deprecated
 **/
class Flight extends Model
{
    use HasFactory;

    protected $fillable = [
        'airline_id',
        'number',
        'date',
        'from',
        'to',
        'etd',
        'eta',

        'date_etd',
        'date_eta',
    ];

    protected $casts = [
        'date' => 'datetime:Y-m-d',
        'date_etd' => 'datetime',
        'date_eta' => 'datetime',
    ];

    public function airline(): BelongsTo
    {
        return $this->belongsTo(Airline::class);
    }

    public function arrival_groups(): HasMany
    {
        return $this->hasMany(Group::class, 'flight_arr_id');
    }

    public function departure_groups(): HasMany
    {
        return $this->hasMany(Group::class, 'flight_dep_id');
    }

    public function getEtdAttribute()
    {
        if ($this->attributes['date_etd']) {
            return $this->date_etd->format('H:i');
        }

        return substr($this->attributes['etd'], 0, 5);
    }

    public function getEtaAttribute()
    {
        if ($this->attributes['date_etd'] && $this->attributes['date_eta']) {
            $etd = $this->date_etd->startOfDay();
            $eta = $this->date_eta->startOfDay();
            $diff = $etd->diffInDays($eta);
            $eta = $this->date_eta->format('H:i');

            if ($diff > 0) {
                return $eta . '+' . $diff;
            }

            return $eta;
        }

        return substr($this->attributes['eta'], 0, 5);
    }
}
