<?php

namespace App\Models;

use App\Enums\Enums\Gender;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Storage;

class Pilgrim extends Model
{
    use HasFactory;

    const TITLES = [
        'mr' => 'Mr.',
        'mrs' => 'Mrs.',
        'ms' => 'Miss',
    ];

    protected $fillable = [
        'fullname',
        'title',

        'passport_number',
        'national_id',

        'gender',
        'birthplace',
        'birthdate',

        'phone',

        'photo',
    ];

    protected $casts = [
        'birthdate' => 'date',
        'gender' => Gender::class,
    ];

    protected static function booted()
    {
        static::creating(function ($model) {
            if (blank($model->gender)) {
                $model->gender = match ($model->title) {
                    'mrs', 'ms' => Gender::Female,
                    default => Gender::Male
                };
            }
        });
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class)->withTimestamps()
            ->withPivot(['room_id', 'is_tour_leader']);
    }

    public function fullnameWithTitle(): Attribute
    {
        return Attribute::make(
            get: fn () => static::TITLES[$this->title] . ' ' . $this->fullname,
        );
    }

    public function photoUrl(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->photo ? Storage::disk('s3')->url($this->photo) : null,
        );
    }

    public function age(): Attribute
    {
        return Attribute::make(
            get: fn () => floor($this->birthdate?->diffInYears(now()) ?? 0),
        );
    }
}
