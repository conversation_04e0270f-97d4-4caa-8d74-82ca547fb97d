<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class GroupVehicle extends Pivot
{
    protected $table = 'group_vehicle';

    protected $fillable = [
        'group_id',
        'vehicle_id',

        'pax_count',
        'count',
    ];

    public $timestamps = false;

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }
}
