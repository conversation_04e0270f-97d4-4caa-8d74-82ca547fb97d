<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Airport extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'city',
        'country',
        'code',
        'iata',
        'icao',
        'latitude',
        'longitude',
        'timezone',
    ];

    public function handlers(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class, 'airport_handler', 'airport_code', 'handler_id', 'code');
    }

    public static function getCodeOptions(): array
    {
        return self::query()
            ->orderBy('code')
            ->pluck('code', 'code')
            ->toArray();
    }
}
