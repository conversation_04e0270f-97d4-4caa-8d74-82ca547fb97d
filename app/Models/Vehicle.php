<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'capacity',
    ];

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class)
            ->using(GroupVehicle::class);
    }

    public function group_vehicles(): HasMany
    {
        return $this->hasMany(GroupVehicle::class);
    }

    public function delete()
    {
        if ($this->group_vehicles()->exists()) {
            return null;
        }

        return parent::delete();
    }
}
