<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Airline extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
    ];

    public function flights(): HasMany
    {
        return $this->hasMany(GroupFlight::class);
    }

    public function delete()
    {
        if ($this->flights()->exists()) {
            return null;
        }

        return parent::delete();
    }
}
