<?php

namespace App\Models\Traits;

use App\Models\Meta;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait Metable
{
    public static function bootMetable()
    {
        // delete all attached meta on deletion
        static::deleted(function (self $model) {
            if (method_exists($model, 'isForceDeleting') && ! $model->isForceDeleting()) {
                return;
            }
            $model->purgeMeta();
        });
    }

    public function metas(): MorphMany
    {
        return $this->morphMany(Meta::class, 'metable');
    }

    public function setMeta($key, $value = null)
    {
        $meta = $this->metas()->firstWhere('key', $key);
        if ($meta) {
            $meta->update(['value' => maybe_serialize($value)]);
        } else {
            $this->metas()->create([
                'key' => $key,
                'value' => maybe_serialize($value),
            ]);
        }
    }

    public function getMeta($key = null)
    {
        if (is_null($key)) {
            return $this->metas->mapWithKeys(fn ($m) => [
                $m->key => $m->value,
            ])->toArray();
        }

        if (is_string($key)) {
            $meta = $this->metas()->firstWhere('key', $key);

            return $meta?->value;
        }

        if (is_array($key)) {
            return $this->metas()->whereIn('key', $key)->get()->mapWithKeys(fn ($m) => [
                $m->key => $m->value,
            ])->toArray();
        }
    }

    public function purgeMeta(): void
    {
        $this->metas()->delete();
        // $this->setRelation('meta', $this->makeMeta()->newCollection([]));
    }
}
