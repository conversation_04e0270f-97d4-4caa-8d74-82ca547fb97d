<?php

namespace App\Models\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

trait WithUserstamps
{
    public static function bootWithUserstamps()
    {
        static::creating(function ($model) {
            $model->created_by_id = auth()->id();
            $model->updated_by_id = auth()->id();
        });
        static::updating(function ($model) {
            $model->updated_by_id = auth()->id();
        });

        if (static::usingSoftDeletes()) {
            static::deleting(function ($model) {
                $model->deleted_by_id = auth()->id();

                $dispatcher = $model->getEventDispatcher();
                $model->unsetEventDispatcher();
                $model->save();
                $model->setEventDispatcher($dispatcher);
            });
            static::restoring(function ($model) {
                $model->deleted_by_id = null;
            });
        }
    }

    public static function usingSoftDeletes()
    {
        static $usingSoftDeletes;

        if (is_null($usingSoftDeletes)) {
            return $usingSoftDeletes = in_array(SoftDeletes::class, class_uses_recursive(get_called_class()));
        }

        return $usingSoftDeletes;
    }

    public function created_by(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function updated_by(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by_id');
    }

    public function deleted_by(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by_id');
    }
}
