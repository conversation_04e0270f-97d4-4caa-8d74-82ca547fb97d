<?php

namespace App\Models;

use App\Contracts\IsActivitySubject;
use App\Models\Traits\DefaultLogOptions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Znck\Eloquent\Relations\BelongsToThrough;

class GroupData extends Model implements IsActivitySubject
{
    use DefaultLogOptions;
    use HasFactory;
    use LogsActivity;
    use \Znck\Eloquent\Traits\BelongsToThrough;

    const FILES = [
        'Visa',
        'Ticket',
        'Roomlist',
        'Itinerary',
        '<PERSON><PERSON><PERSON><PERSON> (Ikhwan)',
        '<PERSON><PERSON><PERSON><PERSON> (Akhwat)',
        'Tasrih Umrah',
        'Train Ticket',
        'Amr Tasyghil',
        'Package Info Travel',
    ];

    protected $fillable = [
        'group_id',

        'visa',
        'ticket',
        'roomlist',
        'manifest',
        'files',
    ];

    protected $casts = [
        'files' => 'array',
    ];

    public function getActivitySubjectDescription(Activity $activity): string
    {
        return "Group Data #{$this->group_id}";
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function customer(): BelongsToThrough
    {
        return $this->belongsToThrough(Customer::class, Group::class);
    }

    public function institution(): BelongsToThrough
    {
        return $this->belongsToThrough(Institution::class, Group::class);
    }
}
