<?php

namespace App\Models;

use App\Enums\ReminderRecipientType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReminderRecipient extends Model
{
    use HasFactory;

    protected $fillable = [
        'reminder_id',
        'recipient_type',
        'recipient_id',
    ];

    protected function casts()
    {
        return [
            'recipient_type' => ReminderRecipientType::class,
        ];
    }

    public function reminder(): BelongsTo
    {
        return $this->belongsTo(Reminder::class);
    }
}
