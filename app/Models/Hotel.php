<?php

namespace App\Models;

use App\Settings\GeneralSettings;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Hotel extends Model
{
    use HasFactory;

    const CITIES = [
        'Makkah' => 'مكة',
        'Madinah' => 'المدينة',
        'Jeddah' => 'جدة',
        'Riyadh' => 'الرياض',
        'Thaif' => 'الطائف',
        'Bursa - Turki' => 'Bursa - Turki',
        'Kusadasi - Turki' => 'Kusadasi - Turki',
        'Pamukkale - Turki' => 'Pamukkale - Turki',
        'Cappadocia - Turki' => 'Cappadocia - Turki',
        'Bolu - Turki' => 'Bolu - Turki',
        'Istanbul - Turki' => 'Istanbul - Turki',
    ];

    protected $fillable = [
        'name',
        'city',

        'stars',
        'distance',

        'price_quad',
        'price_triple',
        'price_double',
    ];

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class)
            ->using(GroupHotel::class);
    }

    public function group_hotels(): HasMany
    {
        return $this->hasMany(GroupHotel::class);
    }

    public function delete()
    {
        if ($this->groups()->exists()) {
            return null;
        }

        return parent::delete();
    }

    public static function getCities(): array
    {
        // return array_combine(array_keys(self::CITIES), array_keys(self::CITIES));
        $cities = app(GeneralSettings::class)->cities;

        return array_combine($cities, $cities);
    }
}
