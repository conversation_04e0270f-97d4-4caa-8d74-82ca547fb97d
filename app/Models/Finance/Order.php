<?php

namespace App\Models\Finance;

use App\Enums\OrderStatus;
use App\Models\Customer;
use App\Models\Group;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    protected $fillable = [
        'order_date',
        'order_number',

        'customer_id',
        'group_id',

        'currency_code',
        'exchange_rate',

        'subject',

        'status',
    ];

    protected $casts = [
        'order_date' => 'datetime:Y-m-d',
        'status' => OrderStatus::class,
    ];

    protected static function boot()
    {
        static::creating(function ($model) {
            $model->order_number ??= static::getNextOrderNumber();
        });

        parent::boot();
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'order_id')->orderBy('order_column');
    }

    public static function getNextOrderNumber()
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "ODR-{$periodYear}";

        $lastOrder = static::query()
            ->where('order_number', 'like', $prefix . '%')
            ->orderBy('order_number', 'desc')
            ->first();
        $lastNumber = $lastOrder ?
            (int) str_replace($prefix, '', $lastOrder->order_number)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
