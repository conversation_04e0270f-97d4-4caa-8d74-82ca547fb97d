<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\EloquentSortable\SortableTrait;

class OrderItem extends Model
{
    use SortableTrait;

    public $sortable = [
        'order_column_name' => 'order_column',
        'sort_when_creating' => true,
    ];

    protected $fillable = [
        'order_id',

        'name',
        'description',

        'quantity',

        'unit_price',

        'order_column',
    ];

    public function buildSortQuery(): Builder
    {
        return static::query()->where('order_id', $this->order_id);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }
}
