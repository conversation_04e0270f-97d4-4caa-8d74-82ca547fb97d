<?php

namespace App\Models\Finance;

use App\Enums\OrderStatus;
use App\Models\Bill;
use App\Models\Group;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Facades\DB;

class PurchaseOrder extends Model
{
    protected $fillable = [
        'order_date',
        'order_number',

        'vendor_id',

        'currency_code',
        'exchange_rate',

        'subject',
        'notes',

        'total',
        'status',
    ];

    protected function casts()
    {
        return [
            'order_date' => 'date',
            'exchange_rate' => 'float',
            'total' => 'float',
            'status' => OrderStatus::class,
        ];
    }

    protected static function booted()
    {
        static::creating(function (self $model) {
            $model->order_number ??= self::getNextNumber();
        });
        static::saving(function (self $model) {
            $model->total = $model->getTotal();
        });
    }

    public function bills(): HasMany
    {
        return $this->hasMany(Bill::class, 'order_id');
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class, 'order_id')->orderBy('order_column');
    }

    public function groups(): HasManyThrough
    {
        return $this->hasManyThrough(Group::class, PurchaseOrderItem::class, 'order_id', 'id', secondLocalKey: 'group_id')->distinct();
    }

    public function getTotal(): float
    {
        return $this->items()->sum(DB::raw('quantity * unit_price * (1 + vat/100)'));
    }

    public static function getNextNumber(): string
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "PO-{$periodYear}";

        $lastOrder = static::query()
            ->where('order_number', 'like', $prefix . '%')
            ->orderBy('order_number', 'desc')
            ->first();
        $lastNumber = $lastOrder ?
            (int) str_replace($prefix, '', $lastOrder->order_number)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }

    public function createBill(): Bill
    {
        $bill = Bill::create([
            'order_id' => $this->id,
            'bill_date' => $this->order_date,
            'vendor_id' => $this->vendor_id,
            'due_date' => $this->order_date,
            'currency_code' => $this->currency_code,
            'exchange_rate' => $this->exchange_rate,
            'subject' => $this->subject,
            'notes' => $this->notes,
            'total' => $this->total,
        ]);

        foreach ($this->items as $item) {
            $bill->items()->create([
                'group_id' => $item->group_id,
                'product_id' => $item->product_id,
                'name' => $item->name,
                'description' => $item->description,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'vat' => $item->vat,
            ]);
        }

        $bill->save();

        return $bill;
    }
}
