<?php

namespace App\Models\Finance;

use App\Actions\Invoice\GeneratePaymentPDF;
use App\Enums\PaymentMethod;
use App\Mail\InvoicePaymentReceipt;
use App\Models\Contracts\JournalTransaction;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Traits\WithUserstamps;
use Filament\Forms;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Spatie\Activitylog\Traits\LogsActivity;

class InvoicePayment extends Model implements JournalTransaction
{
    use DefaultLogOptions;
    use HasFactory;
    use LogsActivity;
    use WithUserstamps;

    protected $fillable = [
        'invoice_id',

        'payment_method',
        'cash_account_id',

        'customer_cash_id',

        'paid_at',
        'amount',
        'currency_code',
        'exchange_rate',

        'description',
        'attachment',

        'created_by_id',
        'updated_by_id',
    ];

    protected $casts = [
        'payment_method' => PaymentMethod::class,
        'paid_at' => 'datetime',
        'amount' => 'float',
        'exchange_rate' => 'float',
    ];

    public function getTransactionDetailAttribute(): ?string
    {
        return $this->description;
    }

    public function amountConverted(): Attribute
    {
        return Attribute::get(function () {
            if ($this->currency_code == $this->invoice->currency_code) {
                return $this->amount;
            }

            $baseCurrency = config('finance.base_currency');

            return $this->currency_code == $baseCurrency
                ? $this->amount / $this->invoice->exchange_rate
                : $this->amount * $this->exchange_rate / $this->invoice->exchange_rate;
        });
    }

    protected static function boot()
    {
        static::creating(function (self $model) {
            $model->exchange_rate ??= 1;
        });
        static::saving(function (self $model) {
            if ($model->payment_method === PaymentMethod::Deposit) {
                $model->cash_account_id = CashAccount::query()
                    ->where('code', config('finance.coa.customer_deposit'))
                    ->value('id');
            } else {
                $model->cash_account_id ??= CashAccount::query()
                    ->where('code', config('finance.coa.main_cash'))
                    ->value('id');

                $model->payment_method ??= PaymentMethod::from(CashAccount::query()
                    ->find($model->cash_account_id)?->group ?? 'cash');
            }
        });
        static::saved(function (self $model) {
            if ($model->payment_method === PaymentMethod::Deposit && blank($model->customer_cash_id)) {
                $model->updateQuietly([
                    'customer_cash_id' => CustomerCash::query()
                        ->create([
                            'customer_id' => $model->invoice?->customer_id,

                            'cashed_at' => $model->paid_at,

                            'type' => 'c',
                            'amount' => $model->amount,
                            'currency' => $model->currency_code,
                            'exchange_rate' => $model->exchange_rate,

                            'details' => $model->description,
                            'attachment' => $model->attachment,

                            'related_type' => 'invoice_payment',
                            'related_id' => $model->id,
                        ])
                        ->id,
                ]);
            } elseif ($model->payment_method === PaymentMethod::Deposit && filled($model->customer_cash_id) && $model->wasChanged(['amount', 'currency', 'exchange_rate'])) {
                CustomerCash::find($model->customer_cash_id)?->update([
                    'amount' => $model->amount,
                    'currency' => $model->currency_code,
                    'exchange_rate' => $model->exchange_rate,
                ]);
            } elseif ($model->payment_method !== PaymentMethod::Deposit && filled($model->customer_cash_id)) {
                CustomerCash::find($model->customer_cash_id)?->delete();
                $model->updateQuietly([
                    'customer_cash_id' => null,
                ]);
            }

            $model->invoice?->save();
            $model->syncJournalEntry();
        });
        static::deleted(fn ($model) => $model->invoice?->save());

        parent::boot();
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function cash_account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class)
            ->isCashOrBank()
            ->orWhere('code', config('finance.coa.customer_deposit'));
    }

    public function customer_cash(): BelongsTo
    {
        return $this->belongsTo(CustomerCash::class);
    }

    public function journal_entry(): MorphOne
    {
        return $this->morphOne(JournalEntry::class, 'transaction');
    }

    public function syncJournalEntry(): void
    {
        /** @var JournalEntry $entry */
        $entry = $this->journal_entry()->updateOrCreate([], [
            'entry_date' => $this->paid_at,
            'details' => 'Invoice Payment ' . ($this->invoice?->invoice_number ?? '') . " (#{$this->id})",
        ]);
        $customerCash = $this->customer_cash;
        $entry->items()->updateOrCreate([
            'type' => 'd',
        ], [
            'account_id' => $this->cash_account_id ?? CashAccount::query()
                ->where('code', $this->payment_method === PaymentMethod::Deposit
                    ? config('finance.coa.customer_deposit')
                    : config('finance.coa.main_cash'))
                ->value('id'),
            'amount' => $this->amount * $this->exchange_rate,
            'owner_type' => $this->payment_method === PaymentMethod::Deposit ? 'customer' : null,
            'owner_id' => $this->payment_method === PaymentMethod::Deposit ? $customerCash?->customer_id : null,
        ]);
        $entry->items()->updateOrCreate([
            'type' => 'c',
        ], [
            'account_id' => CashAccount::query()
                ->where('code', config('finance.coa.receivable'))
                ->value('id'),
            'amount' => $this->amount * $this->exchange_rate,
        ]);
    }

    public function delete()
    {
        $this->customer_cash?->delete();
        $this->journal_entry?->delete();

        return parent::delete();
    }

    public static function getReceiptTableAction()
    {
        return Tables\Actions\ViewAction::make()
            ->label('Receipt')
            ->icon('heroicon-m-receipt-percent')
            ->form(function ($record) {
                return [
                    Forms\Components\View::make('payment')
                        ->view('components.finance.receipt')
                        ->viewData(['payment' => $record, 'isInline' => true]),
                ];
            })
            ->extraModalFooterActions([
                Tables\Actions\Action::make('pdf')
                    ->label('PDF')
                    ->action(function ($record) {
                        return response()->download(GeneratePaymentPDF::run($record));
                    })
                    ->color('gray')
                    ->icon('heroicon-o-arrow-down-tray'),
                Tables\Actions\Action::make('send')
                    ->action(function ($record, $data, $action) {
                        Mail::to($data['email'])
                            ->send(
                                new InvoicePaymentReceipt($record)
                            );
                        $action->success();
                    })
                    ->successNotificationTitle('Payment receipt sent.')
                    ->form(function ($record) {
                        return [
                            Forms\Components\TextInput::make('email')
                                ->required()
                                ->email()
                                ->default($record->invoice->customer->email),
                        ];
                    })
                    ->modalWidth('sm')
                    ->modalHeading('Send receipt')
                    ->modalSubmitActionLabel('Send')
                    ->color('success')
                    ->icon('heroicon-o-paper-airplane'),
            ])
            ->modalFooterActionsAlignment(Alignment::End)
            ->modalHeading('');
    }

    public static function getDateTableFilter($default = null)
    {
        return Tables\Filters\Filter::make('paid_at')
            ->form([
                Forms\Components\DatePicker::make('date_from')
                    ->default($default)
                    ->placeholder(fn ($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                Forms\Components\DatePicker::make('date_until')
                    ->placeholder(fn ($state): string => now()->format('M d, Y')),
            ])
            ->query(function (Builder $query, array $data): Builder {
                return $query
                    ->when(
                        $data['date_from'],
                        fn (Builder $query, $date): Builder => $query->whereDate('paid_at', '>=', $date),
                    )
                    ->when(
                        $data['date_until'],
                        fn (Builder $query, $date): Builder => $query->whereDate('paid_at', '<=', $date),
                    );
            })
            ->indicateUsing(function (array $data): array {
                $indicators = [];
                if ($data['date_from'] ?? null) {
                    $indicators['date_from'] = 'Payments from ' . Carbon::parse($data['date_from'])->format('d-M-y');
                }
                if ($data['date_until'] ?? null) {
                    $indicators['date_until'] = 'Payments until ' . Carbon::parse($data['date_until'])->format('d-M-y');
                }

                return $indicators;
            });
    }
}
