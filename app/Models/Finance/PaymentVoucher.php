<?php

namespace App\Models\Finance;

use App\Enums\VoucherPaymentMethod;
use App\Models\User;
use Filament\Forms;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Mail;

class PaymentVoucher extends Model
{
    protected $fillable = [
        'voucher_no',

        'paid_at',
        'paid_to_id',

        'amount',
        'currency_code',
        'exchange_rate',

        'payment_method',
        'check_no',

        'description',

        'created_by_id',
        'checked_by_id',
        'approved_by_id',
    ];

    protected function casts()
    {
        return [
            'amount' => 'float',
            'exchange_rate' => 'float',
            'payment_method' => VoucherPaymentMethod::class,
        ];
    }

    public function paidTo(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function checkedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public static function getVoucherTableAction()
    {
        return Tables\Actions\ViewAction::make()
            ->form(function ($record) {
                return [
                    Forms\Components\View::make('voucher')
                        ->view('components.finance.payment-voucher')
                        ->viewData(['voucher' => $record, 'isInline' => true]),
                ];
            })
            ->extraModalFooterActions([
                Tables\Actions\Action::make('pdf')
                    ->label('PDF')
                    ->action(function ($record) {
                        return response()->download(GeneratePaymentPDF::run($record));
                    })
                    ->color('gray')
                    ->icon('heroicon-o-arrow-down-tray'),
                // Tables\Actions\Action::make('send')
                //     ->action(function ($record, $data, $action) {
                //         Mail::to($data['email'])
                //             ->send(
                //                 new InvoicePaymentReceipt($record)
                //             );
                //         $action->success();
                //     })
                //     ->successNotificationTitle('Payment voucher sent.')
                //     ->form(function ($record) {
                //         return [
                //             Forms\Components\TextInput::make('email')
                //                 ->required()
                //                 ->email()
                //                 ->default($record->invoice->customer->email),
                //         ];
                //     })
                //     ->modalWidth('sm')
                //     ->modalHeading('Send voucher')
                //     ->modalSubmitActionLabel('Send')
                //     ->color('success')
                //     ->icon('heroicon-o-paper-airplane'),
            ])
            ->modalFooterActionsAlignment(Alignment::End)
            ->modalHeading('');
    }

    public static function getNextNumber(): string
    {
        $period = current_period();
        $periodYear = preg_replace('/[^0-9]/', '', $period->name);

        $prefix = "PV-{$periodYear}";

        $lastBill = static::query()
            ->where('voucher_no', 'like', $prefix . '%')
            ->orderBy('voucher_no', 'desc')
            ->first();
        $lastNumber = $lastBill ?
        (int) str_replace($prefix, '', $lastBill->voucher_no)
            : 0;

        return $prefix . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
