<?php

namespace App\Models\Finance;

use App\Models\Group;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\EloquentSortable\Sortable;
use <PERSON>tie\EloquentSortable\SortableTrait;

class PurchaseOrderItem extends Model implements Sortable
{
    use SortableTrait;

    public $sortable = [
        'order_column_name' => 'order_column',
        'sort_when_creating' => true,
    ];

    protected $fillable = [
        'order_id',
        'group_id',

        'product_id',

        'name',
        'description',

        'quantity',
        'unit_price',
        'vat',

        'order_column',
    ];

    protected function casts()
    {
        return [
            'unit_price' => 'float',
            'vat' => 'float',
        ];
    }

    public function buildSortQuery(): Builder
    {
        return static::query()->where('order_id', $this->order_id);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class, 'order_id');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
