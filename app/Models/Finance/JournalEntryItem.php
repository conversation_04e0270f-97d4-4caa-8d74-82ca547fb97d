<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class JournalEntryItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'entry_id',

        'account_id',
        'type', // c / d
        'amount',

        'owner_type',
        'owner_id',
    ];

    protected function casts()
    {
        return [
            'amount' => 'float',
        ];
    }

    public function entry(): BelongsTo
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(CashAccount::class);
    }

    public function owner(): MorphTo
    {
        return $this->morphTo();
    }
}
