<?php

namespace App\Models\Finance;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',

        'name',
        'description',

        'cost',
        'unit_price',
    ];

    protected function casts()
    {
        return [
            'cost' => 'float',
            'unit_price' => 'float',
        ];
    }

    protected static function booted()
    {
        static::creating(function ($self) {
            $self->unit_price ??= $self->cost ?? 0;
        });
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class);
    }
}
