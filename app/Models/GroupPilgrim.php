<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class GroupPilgrim extends Pivot
{
    public $incrementing = true;

    protected $fillable = [
        'group_id',
        'pilgrim_id',
        'room_id',

        'is_tour_leader',
        // 'is_mutawif',
    ];

    protected function casts(): array
    {
        return [
            'is_tour_leader' => 'boolean',
            // 'is_mutawif' => 'boolean',
        ];
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function pilgrim(): BelongsTo
    {
        return $this->belongsTo(Pilgrim::class);
    }

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class);
    }
}
