<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\EloquentSortable\SortableTrait;

class Manasik extends Model
{
    use HasFactory;
    use SortableTrait;

    protected $fillable = [
        'group_id',

        'name',
        'date',
        'meta',

        'sort',
    ];

    protected $casts = [
        'date' => 'datetime',
        'meta' => 'array',
    ];

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function addons(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->meta['addons'] ?? null,
        );
    }
}
