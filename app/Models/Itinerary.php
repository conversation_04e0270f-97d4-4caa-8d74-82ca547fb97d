<?php

namespace App\Models;

use App\Models\Vendors\SnackHandler;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Spatie\EloquentSortable\SortableTrait;
use Znck\Eloquent\Relations\BelongsToThrough;
use Znck\Eloquent\Traits\BelongsToThrough as BelongsToThroughTrait;

class Itinerary extends Model
{
    use BelongsToThroughTrait;
    use HasFactory;
    use SortableTrait;
    use Traits\Metable;

    protected $fillable = [
        'group_id',

        'date',
        'city',
        'location',
        'description',
        'is_arrival',

        'has_snack',
        'snack_details',
        'snack_handler_id',

        'sort',
    ];

    protected $casts = [
        'date' => 'datetime',
        'is_arrival' => 'boolean',
        'has_snack' => 'boolean',
    ];

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class, 'itinerary_contacts');
    }

    public function customer(): BelongsToThrough
    {
        return $this->belongsToThrough(Customer::class, Group::class);
    }

    public function snack_handler(): BelongsTo
    {
        return $this->belongsTo(SnackHandler::class);
    }

    /**
     * @return Collection<User>
     */
    public function getUserHandlers(): ?Collection
    {
        return $this->snack_handler?->users;
    }
}
