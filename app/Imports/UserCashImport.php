<?php

namespace App\Imports;

use App\Models\Finance\UserCash;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class UserCashImport implements SkipsEmptyRows, SkipsOnFailure, ToModel, WithHeadingRow
{
    use SkipsFailures;

    /**
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        if (filled($row['date'] ?? null)) {
            $cashed_at = Date::excelToDateTimeObject($row['date'])->format('Y-m-d');
        }

        $currency = $row['currency'] ?? 'SAR';
        $exchange_rate = 1;
        if ($currency == 'IDR') {
            $exchange_rate = $row['exchange_rate'] ?? 4200;
            $exchange_rate = $exchange_rate > 1
                ? 1 / $exchange_rate
                : $exchange_rate;
        }

        return new UserCash([
            'user_id' => $row['user_id'] ?? auth()->user()->id,

            'group_id' => $row['group_id'] ?? null,
            'category_id' => $row['category_id'] ?? null,

            'cashed_at' => $cashed_at ?? now(),

            'type' => filled($row['in'] ?? null) ? 'd' : 'c',
            'amount' => $row['in'] ?? $row['out'] ?? 0,
            'currency' => $currency,
            'exchange_rate' => $exchange_rate,

            'details' => $row['details'] ?? null,
        ]);
    }
}
