<?php

namespace App\Imports;

use App\Models\GroupPilgrim;
use App\Models\Pilgrim;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Row;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class ManifestImport implements OnEachRow, SkipsEmptyRows, SkipsOnFailure, WithHeadingRow, WithValidation
{
    use SkipsFailures;

    protected $group_id = null;

    public function __construct(
        public string $model,
        public array $attributes = []
    ) {
        $this->group_id = $attributes['group_id'] ?? null;
    }

    public function rules(): array
    {
        return [
            'ppno' => ['nullable'],
            'fullname' => ['required'],
            'national_id' => ['nullable'],
            'first' => ['nullable'],
            'last' => ['nullable'],
            'father' => ['nullable'],
            'grand' => ['nullable'],
            'gender' => ['nullable', 'in:m,f'],
            'title' => ['nullable', 'in:mr,mrs,ms'],
            'birthdate' => ['nullable', 'date_format:Y-m-d'],
        ];
    }

    public function prepareForValidation($data, $index)
    {
        if (isset($data['birthdate'])) {
            $data['birthdate'] = Date::excelToDateTimeObject($data['birthdate'])->format('Y-m-d');
        }
        $data['gender'] = match ($data['gender'] ?? null) {
            'Male', 'male', 'm' => 'm',
            'Female', 'female', 'f' => 'f',
            default => null
        };
        $data['title'] = match ($data['title'] ?? null) {
            'Mr', 'mr' => 'mr',
            'Mrs', 'mrs' => 'mrs',
            'Miss', 'miss', 'ms' => 'ms',
            default => null
        };
        if (isset($data['phone'])) {
            $data['phone'] = format_phone_for_db($data['phone']);
        }

        return $data;
    }

    public function onRow(Row $row)
    {
        $data = $row->toArray();

        if (blank($data['national_id'] ?? null) && blank($data['ppno'] ?? null)) {
            return;
        }

        if (filled($data['national_id'] ?? null)) {
            $pilgrim = Pilgrim::where('national_id', $data['national_id'])
                ->first();
        } else {
            $pilgrim = Pilgrim::where('passport_number', $data['ppno'])
                ->first();
        }
        if (preg_match('/\p{Arabic}/u', $data['fullname'] ?? '')) {
            $parts = [];
            if (! empty($data['first'])) {
                $parts[] = $data['first'];
            }
            if (! empty($data['father'])) {
                $parts[] = $data['father'];
            }
            if (! empty($data['grand'])) {
                $parts[] = $data['grand'];
            }
            if (! empty($data['last'])) {
                $parts[] = $data['last'];
            }
            if ($parts) {
                $data['fullname'] = implode(' ', $parts);
            }
        }

        $modelData = [
            'fullname' => $data['fullname'],
            'title' => $data['title'],

            'passport_number' => $data['ppno'] ?? null,
            'national_id' => $data['national_id'] ?? null,

            'gender' => $data['gender'],
            'birthplace' => $data['birthcity'] ?? null,
            'birthdate' => $data['birthdate'] ?? null,

            'phone' => $data['phone'] ?? null,
        ];
        if ($pilgrim) {
            $pilgrim->update($modelData);
        } else {
            $pilgrim = Pilgrim::create($modelData);
        }

        if ($this->group_id) {
            // $pilgrim->groups()->attach($this->group_id);
            GroupPilgrim::query()->firstOrCreate([
                'group_id' => $this->group_id,
                'pilgrim_id' => $pilgrim->id,
            ]);
        }
    }
}
