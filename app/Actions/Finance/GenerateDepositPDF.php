<?php

namespace App\Actions\Finance;

use App\Models\Finance\CustomerCash;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateDepositPDF
{
    use AsAction;

    public function handle(CustomerCash $deposit)
    {
        $name = 'Deposit #' . str_pad($deposit->id, 6, '0', STR_PAD_LEFT);

        return pdf()
            ->view('finance.pdf.deposit-receipt', compact('deposit'))
            ->name($name)
            ->save(storage_path('app/deposit-receipts'));
    }
}
