<?php

namespace App\Actions\Estimate;

use App\Models\Finance\Estimate;
use Lorisleiva\Actions\Concerns\AsAction;

class GeneratePDF
{
    use AsAction;

    public function handle(Estimate $estimate)
    {
        $storagePath = storage_path('app/estimates');

        if (! file_exists($storagePath)) {
            mkdir($storagePath, 0755, true);
        }

        return pdf()
            ->view('finance.estimates.view', compact('estimate'))
            ->name($estimate->estimate_number)
            ->save($storagePath);
    }
}
