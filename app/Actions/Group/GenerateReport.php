<?php

namespace App\Actions\Group;

use App\Models\Group;
use Lorisleiva\Actions\Concerns\AsAction;

class GenerateReport
{
    use AsAction;

    public function handle(Group $group)
    {
        $data = $group->getClosingReportData();

        return pdf()
            ->view('admin.finance.groups.report', compact('group', 'data'))
            ->name("closing-report-{$group->id}")
            ->save(storage_path('app/groups/closing-reports'));
    }
}
