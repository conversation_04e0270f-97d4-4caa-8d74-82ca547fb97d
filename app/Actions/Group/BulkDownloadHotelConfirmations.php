<?php

namespace App\Actions\Group;

use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Lorisleiva\Actions\Concerns\AsAction;

use function Motekar\LaravelZip\Support\zip;

class BulkDownloadHotelConfirmations
{
    use AsAction;

    public function handle(Collection $items): string
    {
        $tempDir = storage_path('temp/zip');
        $tempZip = storage_path('temp/hotel-confirmations.zip');

        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, recursive: true);
        } else {
            if (File::exists($tempZip)) {
                File::delete($tempZip);
            }
            File::delete(glob("{$tempDir}/*"));
        }

        $items
            ->each(function ($item) {
                if ($item->confirmation_file) {
                    try {
                        File::copy(storage_path("app/public/{$item->confirmation_file}"), storage_path("temp/zip/{$item->confirmation_file}"));
                    } catch (Exception $e) {
                        // do nothing
                    }
                }
            });

        zip()
            ->make($tempZip)
            ->add($tempDir)
            ->close();

        return $tempZip;
    }
}
