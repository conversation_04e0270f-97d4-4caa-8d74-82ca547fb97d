<?php

namespace App\Actions\Group;

use App\Models\Group;
use Lorisleiva\Actions\Concerns\AsAction;

class GeneratePIF
{
    use AsAction;

    public function handle(Group $group)
    {
        $filename = sanitize_filename("PIF #{$group->id} - {$group->customer->name} ({$group->name})");

        return pdf()
            ->view('groups.prints.pif', compact('group'))
            ->name($filename)
            ->save(storage_path('app/groups'));
    }
}
