<?php

namespace App\Actions\PurchaseOrder;

use App\Models\Finance\PurchaseOrder;
use Lorisleiva\Actions\Concerns\AsAction;

class GeneratePDF
{
    use AsAction;

    public function handle(PurchaseOrder $record)
    {
        return pdf()
            ->view('finance.purchase-orders.view', compact('record'))
            ->name($record->order_number)
            ->save(storage_path('app/purchase-orders'));
    }
}
