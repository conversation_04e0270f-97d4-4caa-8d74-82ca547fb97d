<?php

namespace App\Actions\Invoice;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Lorisleiva\Actions\Concerns\AsAction;

use function Motekar\LaravelZip\Support\zip;

class BulkGeneratePaymentPDF
{
    use AsAction;

    public function handle(Collection $payments): string
    {
        $tempDir = storage_path('temp/zip');
        $tempZip = storage_path('temp/invoice-payments.zip');

        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, recursive: true);
        } else {
            if (File::exists($tempZip)) {
                File::delete($tempZip);
            }
            File::delete(glob("{$tempDir}/*"));
        }

        $payments
            ->each(function ($payment) {
                $name = 'Payment #' . str_pad($payment->id, 6, '0', STR_PAD_LEFT);

                pdf()
                    ->view('finance.invoices.view-payment', compact('payment'))
                    ->name($name)
                    ->save(storage_path('temp/zip'));
            });

        zip()
            ->make($tempZip)
            ->add($tempDir)
            ->close();

        return $tempZip;
    }
}
