<?php

namespace App\Actions\Invoice;

use App\Models\Finance\InvoicePayment;
use Lorisleiva\Actions\Concerns\AsAction;

class GeneratePaymentPDF
{
    use AsAction;

    public function handle(InvoicePayment $payment)
    {
        $name = 'Payment #' . str_pad($payment->id, 6, '0', STR_PAD_LEFT);

        return pdf()
            ->view('finance.invoices.view-payment', compact('payment'))
            ->name($name)
            ->save(storage_path('app/invoices/receipts'));
    }
}
