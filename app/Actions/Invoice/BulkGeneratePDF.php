<?php

namespace App\Actions\Invoice;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Lorisleiva\Actions\Concerns\AsAction;

use function Motekar\LaravelZip\Support\zip;

class BulkGeneratePDF
{
    use AsAction;

    public function handle(Collection $invoices): string
    {
        $tempDir = storage_path('temp/zip');
        $tempZip = storage_path('temp/invoices.zip');

        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, recursive: true);
        } else {
            if (File::exists($tempZip)) {
                File::delete($tempZip);
            }
            File::delete(glob("{$tempDir}/*"));
        }

        $invoices
            ->each(function ($invoice) {
                pdf()
                    ->view('finance.invoices.view', compact('invoice'))
                    ->name($invoice->invoice_number)
                    ->save(storage_path('temp/zip'));
            });

        zip()
            ->make($tempZip)
            ->add($tempDir)
            ->close();

        return $tempZip;
    }
}
