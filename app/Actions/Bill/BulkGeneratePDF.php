<?php

namespace App\Actions\Bill;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Lorisleiva\Actions\Concerns\AsAction;

use function Motekar\LaravelZip\Support\zip;

class BulkGeneratePDF
{
    use AsAction;

    public function handle(Collection $records): string
    {
        $tempDir = storage_path('temp/zip');
        $tempZip = storage_path('temp/bills.zip');

        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, recursive: true);
        } else {
            if (File::exists($tempZip)) {
                File::delete($tempZip);
            }
            File::delete(glob("{$tempDir}/*"));
        }

        $records
            ->each(function ($record) {
                pdf()
                    ->view('finance.bills.view', compact('record'))
                    ->name($record->bill_number)
                    ->save(storage_path('temp/zip'));
            });

        zip()
            ->make($tempZip)
            ->add($tempDir)
            ->close();

        return $tempZip;
    }
}
