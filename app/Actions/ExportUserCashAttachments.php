<?php

namespace App\Actions;

use App\Models\Finance\UserCash;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Lorisleiva\Actions\Concerns\AsAction;

use function Motekar\LaravelZip\Support\zip;

class ExportUserCashAttachments
{
    use AsAction;

    public function handle(array $filters)
    {
        $dateRange = $filters['cashed_at']['cashed_at'] ?? null;
        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $dates = [
                    Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                    Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                ];
            }
        }
        $userRole = $filters['role_id']['value'] ?? null;
        $userId = $filters['user_id']['value'] ?? null;
        $groupId = $filters['group']['group_id'] ?? null;

        $items = UserCash::query()
            ->when(
                $dateRange,
                fn ($query) => $query->whereBetween('cashed_at', $dates)
            )
            ->when(
                $userRole,
                fn ($query) => $query->whereHas('user.roles', fn ($query) => $query->where('id', $userRole))
            )
            ->when($userId, fn ($query) => $query->where('user_id', $userId))
            ->when($groupId, fn ($query) => $query->where('group_id', $groupId))
            ->select(['id', 'attachment'])
            ->get();

        $tempDir = storage_path('temp/zip');
        $tempZip = storage_path('temp/attachments.zip');

        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, recursive: true);
        } else {
            if (File::exists($tempZip)) {
                File::delete($tempZip);
            }
            File::delete(glob("{$tempDir}/*"));
        }

        $items
            ->filter(fn ($item) => (bool) $item->attachment)
            ->map(function ($item) {
                $extension = pathinfo($item->attachmentUrl, PATHINFO_EXTENSION);

                return [
                    'filename' => "#{$item->id}.{$extension}",
                    'url' => $item->attachmentUrl,
                ];
            })
            ->each(function ($item) {
                copy($item['url'], storage_path("temp/zip/{$item['filename']}"));
                // Http::get($item['url'], [
                //     'sink' => storage_path("temp/zip/{$item['filename']}"),
                // ]);
            });

        zip()
            ->make($tempZip)
            ->add($tempDir)
            ->close();

        return $tempZip;
    }
}
