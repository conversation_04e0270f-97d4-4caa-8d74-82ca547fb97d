<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GroupHotel;

class GroupHotelController extends Controller
{
    public function downloadConfirmation(GroupHotel $groupHotel)
    {
        abort_unless((bool) $groupHotel->confirmation_file, 404);

        $filename = $groupHotel->confirmation_file;
        if ($filename) {
            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $filepath = storage_path("app/public/$filename");

            abort_unless(file_exists($filepath), 404, 'File does not exis.');

            $customer = $groupHotel->group?->customer?->name ?? '';

            return response()->download(
                $filepath,
                sanitize_filename("Confirmation - {$customer} - {$groupHotel->hotel->city} - {$groupHotel->hotel->name} - {$groupHotel->confirmation_number}.$ext")
            );
        }

        abort(404, 'File not found.');
    }
}
