<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Group;

class GroupController extends Controller
{
    public function downloadFile(Group $group)
    {
        abort_unless(request()->has('name'), 404);

        $name = request('name');

        $data = $group->group_data;

        $filename = $data[$name] ?? null;
        if (! $filename && filled($data->files)) {
            foreach ($data->files as $f) {
                if ($name == $f['name']) {
                    $filename = $f['file'];
                }
            }
        }

        if ($filename) {
            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $filepath = storage_path("app/public/$filename");

            abort_unless(file_exists($filepath), 404, 'File does not exis.');

            return response()->download(
                $filepath,
                sanitize_filename("#{$group->id} - $name - {$group->customer->name} ({$group->name}).$ext")
            );
        }

        abort(404, 'File not found.');
    }

    public function print(Group $group)
    {
        $group->load([
            'mutawif', 'mutawif_2', 'mutawif_3',
            'group_hotels' => fn ($q) => $q->orderBy('sort')->orderBy('check_in'),
            'group_vehicles.vehicle',
            'itineraries' => fn ($q) => $q->orderBy('sort'),
        ]);

        return view('groups.prints.pif', compact('group'));
    }

    public function printLuggageTags(Group $group)
    {
        $group->load(['pilgrims' => fn ($query) => $query->orderBy('fullname')]);

        return view('groups.prints.luggage-tags', compact('group'));
    }

    public function printRoomlist(Group $group)
    {
        $group->load(['rooms' => fn ($query) => $query->orderBy('number'), 'rooms.pilgrims', 'hotels']);

        return view('groups.prints.roomlist', compact('group'));
    }

    public function customerView(Group $group)
    {
        $token = request()->get('token');

        if (! $token) {
            abort(404);
        }

        if ($token != $group->token) {
            abort(404);
        }

        $group->load([
            'mutawif', 'mutawif_2', 'mutawif_3',
            'group_hotels' => fn ($q) => $q->orderBy('sort')->orderBy('check_in'),
            'group_vehicles.vehicle',
            'itineraries' => fn ($q) => $q->orderBy('sort'),
        ]);

        return view('admin.groups.customer-view', compact('group'));
    }
}
