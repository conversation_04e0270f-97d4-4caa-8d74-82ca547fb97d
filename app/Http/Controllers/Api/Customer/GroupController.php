<?php

namespace App\Http\Controllers\Api\Customer;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiErrorResponse;
use App\Http\Responses\ApiSuccessResponse;
use App\Models\Customer;
use App\Models\Group;
use Illuminate\Contracts\Support\Responsable;

class GroupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Responsable
    {
        /** @var Customer */
        $customer = auth('customer')->user();

        return new ApiSuccessResponse($customer->groups()->latest()->get());
    }

    /**
     * Display the specified resource.
     */
    public function show(Group $group): Responsable
    {
        try {
            /** @var Customer */
            $customer = auth('customer')->user();

            $group = $customer->groups()->findOrFail($group->id);

            return new ApiSuccessResponse($group);
        } catch (\Throwable $th) {
            return new ApiErrorResponse('Group not found', $th, 404);
        }
    }
}
