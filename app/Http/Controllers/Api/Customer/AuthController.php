<?php

namespace App\Http\Controllers\Api\Customer;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (!auth('customer')->attempt($credentials)) {
            return response()->json([
                'success' => false,
                'message' => 'Email and password does not match.',
            ], 401);
        }

        /** @var Customer */
        $customer = auth('customer')->user();

        return response()->json([
            'success' => true,
            'data' => [
                'token' => $customer->createToken('API')->plainTextToken,
                'customer' => $customer,
            ],
        ]);
    }
}
