<?php

namespace App\Http\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\Response;

class ApiErrorResponse implements Responsable
{
    private $message;
    private $exception;
    private $code;

    public function __construct($message, $exception = null, $code = Response::HTTP_INTERNAL_SERVER_ERROR)
    {
        $this->message = $message;
        $this->exception = $exception;
        $this->code = $code;
    }

    public function toResponse($request)
    {
        $response = [
            'success' => false,
            'message' => $this->message,
        ];

        if (!is_null($this->exception) && config('app.debug')) {
            $response['debug'] = [
                'message' => $this->exception->getMessage(),
                'file'    => $this->exception->getFile(),
                'line'    => $this->exception->getLine(),
                'trace'   => $this->exception->getTraceAsString()
            ];
        }

        return response()->json($response, $this->code);
    }
}
