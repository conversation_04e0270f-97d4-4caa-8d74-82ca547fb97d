<?php

namespace App\Http\Responses;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\Response;

class ApiSuccessResponse implements Responsable
{
    private $data;
    private $message;
    private $code;

    public function __construct($data, $message = null, $code = Response::HTTP_OK)
    {
        $this->data = $data;
        $this->message = $message;
        $this->code = $code;
    }

    public function toResponse($request)
    {
        $response = [
            'success' => true,
            'data' => $this->data,
        ];

        if ($this->message) {
            $response['message'] = $this->message;
        }

        return response()->json($response, $this->code);
    }
}
