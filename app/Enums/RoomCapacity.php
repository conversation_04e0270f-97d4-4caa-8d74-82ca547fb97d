<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum RoomCapacity: int implements HasLabel
{
    case Single = 1;
    case Double = 2;
    case Triple = 3;
    case Quad = 4;
    case Quint = 5;

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Single => 'Single',
            self::Double => 'Double',
            self::Triple => 'Triple',
            self::Quad => 'Quad',
            self::Quint => 'Quint',
        };
    }
}
