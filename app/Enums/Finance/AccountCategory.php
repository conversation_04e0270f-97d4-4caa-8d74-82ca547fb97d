<?php

namespace App\Enums\Finance;

use Filament\Support\Contracts\HasLabel;

enum AccountCategory: string implements HasLabel
{
    // Assets
    case Cash = 'cash';
    case Bank = 'bank';
    case CurrentAsset = 'current_asset';
    case AccountsReceivable = 'accounts_receivable';
    case NonCurrentAsset = 'non_current_asset';
    // Liabilities
    case CurrentLiability = 'current_liability';
    case AccountsPayable = 'accounts_payable';
    case NonCurrentLiability = 'non_current_liability';
    // Equity
    case Equity = 'equity';
    // Revenue
    case OperatingRevenue = 'operating_revenue';
    case NonOperatingRevenue = 'non_operating_revenue';
    case UncategorizedRevenue = 'uncategorized_revenue';
    // Expenses
    case OperatingExpense = 'operating_expense';
    case NonOperatingExpense = 'non_operating_expense';
    case CostOfGoodsSold = 'cost_of_goods_sold';
    case UncategorizedExpense = 'uncategorized_expense';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Cash => 'Kas',
            self::Bank => 'Bank',
            self::CurrentAsset => 'Aset Lancar',
            self::AccountsReceivable => 'Piutang Usaha',
            self::NonCurrentAsset => 'Aset Tidak Lancar',
            self::CurrentLiability => 'Kewajiban Lancar',
            self::AccountsPayable => 'Utang Usaha',
            self::NonCurrentLiability => 'Kewajiban Tidak Lancar',
            self::Equity => 'Ekuitas',
            self::OperatingRevenue => 'Pendapatan Operasional',
            self::NonOperatingRevenue => 'Pendapatan Non-Operasional',
            self::UncategorizedRevenue => 'Pendapatan Tidak Terkategorikan',
            self::OperatingExpense => 'Beban Operasional',
            self::NonOperatingExpense => 'Beban Non-Operasional',
            self::CostOfGoodsSold => 'Harga Pokok Penjualan',
            self::UncategorizedExpense => 'Beban Tidak Terkategorikan',
        };
    }

    /**
     * Determines if the account typically has a normal debit balance.
     *
     * In accounting, assets and expenses typically have a normal debit balance.
     * A debit increases the balance of these accounts, while a credit decreases it.
     */
    public function isNormalDebitBalance(): bool
    {
        return in_array($this, [
            ...self::groupedCases()['Aset'],
            ...self::groupedCases()['Pengeluaran'],
        ], true);
    }

    /**
     * Determines if the account typically has a normal credit balance.
     *
     * In accounting, liabilities, equity, and revenue typically have a normal credit balance.
     * A credit increases the balance of these accounts, while a debit decreases it.
     */
    public function isNormalCreditBalance(): bool
    {
        return ! $this->isNormalDebitBalance();
    }

    /**
     * Determines if the account is a nominal account.
     *
     * In accounting, nominal accounts are temporary accounts that are closed at the end of each accounting period,
     * with their net balances transferred to Retained Earnings (a real account).
     */
    public function isNominal(): bool
    {
        return in_array($this, [
            ...self::groupedCases()['Penghasilan'],
            ...self::groupedCases()['Pengeluaran'],
        ], true);
    }

    /**
     * Determines if the account is a real account.
     *
     * In accounting, assets, liabilities, and equity are real accounts which are permanent accounts that retain their balances across accounting periods.
     * They are not closed at the end of each accounting period.
     */
    public function isReal(): bool
    {
        return ! $this->isNominal();
    }

    public static function orderedCases(): array
    {
        return [
            self::Cash,
            self::Bank,
            self::CurrentAsset,
            self::AccountsReceivable,
            self::NonCurrentAsset,
            self::CurrentLiability,
            self::AccountsPayable,
            self::NonCurrentLiability,
            self::Equity,
            self::OperatingRevenue,
            self::NonOperatingRevenue,
            self::CostOfGoodsSold,
            self::OperatingExpense,
            self::NonOperatingExpense,
            self::UncategorizedRevenue,
            self::UncategorizedExpense,
        ];
    }

    public static function groupedCases(): array
    {
        return [
            'Aset' => [
                self::Cash,
                self::Bank,
                self::CurrentAsset,
                self::AccountsReceivable,
                self::NonCurrentAsset,
            ],
            'Kewajiban' => [
                self::CurrentLiability,
                self::AccountsPayable,
                self::NonCurrentLiability,
            ],
            'Ekuitas' => [
                self::Equity,
            ],
            'Penghasilan' => [
                self::OperatingRevenue,
                self::NonOperatingRevenue,
                self::UncategorizedRevenue,
            ],
            'Pengeluaran' => [
                self::OperatingExpense,
                self::NonOperatingExpense,
                self::CostOfGoodsSold,
                self::UncategorizedExpense,
            ],
        ];
    }

    public static function getOptions(): array
    {
        $grouped = [];
        foreach (self::groupedCases() as $group => $cases) {
            $grouped[$group] = [];
            foreach ($cases as $case) {
                $grouped[$group][$case->value] = $case->getLabel();
            }
        }

        return $grouped;
    }
}
