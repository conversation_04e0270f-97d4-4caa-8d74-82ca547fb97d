<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum CashTransactionType: string implements HasColor, HasLabel
{
    case In = 'in';
    case Out = 'out';
    case Transfer = 'transfer';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::In => 'Income',
            self::Out => 'Expense',
            self::Transfer => 'Transfer'
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::In => 'success',
            self::Out => 'danger',
            self::Transfer => 'warning'
        };
    }
}
