<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum EstimateStatus: string implements HasColor, HasLabel
{
    case Draft = 'draft';
    case Sent = 'sent';
    case Declined = 'declined';
    case Expired = 'expired';
    case Invoiced = 'invoiced';

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::Draft => 'gray',
            self::Sent => 'info',
            self::Declined => 'danger',
            self::Expired => 'warning',
            self::Invoiced => 'success',
        };
    }

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Draft => 'Draft',
            self::Sent => 'Sent',
            self::Declined => 'Declined',
            self::Expired => 'Expired',
            self::Invoiced => 'Invoiced',
        };
    }
}
