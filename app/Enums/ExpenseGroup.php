<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum ExpenseGroup: string implements HasLabel
{
    case VendorPayment = 'vendor-payment';
    case AirportHandling = 'airport-handling';
    case HotelCheckInOut = 'hotel-check-in-out';
    case Mutawif = 'mutawif';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::VendorPayment => 'Vendor Payment',
            self::AirportHandling => 'Airport Handling',
            self::HotelCheckInOut => 'Hotel Check-In/Out',
            self::Mutawif => 'Mutawif',
        };
    }
}
