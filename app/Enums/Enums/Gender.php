<?php

namespace App\Enums\Enums;

use Filament\Support\Contracts\HasLabel;

enum Gender: string implements HasLabel
{
    case Male = 'm';
    case Female = 'f';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Male => 'Laki-laki',
            self::Female => 'Perempuan',
        };
    }

    public function getShortLabel(): ?string
    {
        return match ($this) {
            self::Male => 'L',
            self::Female => 'P',
        };
    }
}
