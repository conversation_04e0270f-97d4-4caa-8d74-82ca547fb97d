<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum UserRole: string implements HasLabel
{
    case Admin = 'Admin';
    case AdminOperator = 'Admin Operator';
    case Operator = 'Operator';
    case AirportHandler = 'Airport Handler';
    case Mutawif = 'Mutawif';
    case Finance = 'Finance';
    case CheckInTeam = 'Check-In Team';
    case SnackHandler = 'Snack Handler';
    case Customer = 'Customer';
    case Accountant = 'Accountant';

    public function getLabel(): ?string
    {
        return $this->value;
    }
}
