<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum ContactType: string implements HasLabel
{
    case TourLeader = 'tour-leader';
    case Driver = 'driver';
    case General = 'general';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::TourLeader => 'Tour Leader',
            self::Driver => 'Driver',
            self::General => 'General'
        };
    }
}
