<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum NotificationType: string implements HasColor, HasIcon, HasLabel
{
    case WhatsApp = 'whatsapp';
    case Email = 'email';

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::WhatsApp => 'whatsapp',
            self::Email => 'primary',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::WhatsApp => 'tabler-brand-whatsapp',
            self::Email => 'heroicon-o-envelope',
        };
    }

    public function getLabel(): ?string
    {
        return match ($this) {
            self::WhatsApp => 'WhatsApp',
            self::Email => 'Email',
        };
    }
}
