<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum VoucherPaymentMethod: string implements HasLabel
{
    // case Cash = 'cash';
    case Cheque = 'check';
    case BankTransfer = 'bank';
    case Giro = 'giro';

    public function getLabel(): ?string
    {
        return match ($this) {
            // self::Cash => 'Cash',
            self::Cheque => 'Cheque',
            self::BankTransfer => 'Bank Transfer',
            self::Giro => 'Giro',
        };
    }
}
