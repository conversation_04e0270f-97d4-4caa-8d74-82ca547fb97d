<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum HotelRoomType: string implements HasLabel
{
    case CityView = 'city_view';
    case HaromView = 'harom_view';
    case KabahView = 'kabah_view';
    case Connecting = 'connecting';
    case Suite = 'suite';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::CityView => 'City View',
            self::HaromView => 'Harom View',
            self::KabahView => 'Kabah View',
            self::Connecting => 'Connecting',
            self::Suite => 'Suite',
        };
    }

    public function getShortLabel(): ?string
    {
        return match ($this) {
            self::CityView => 'CV',
            self::HaromView => 'HV',
            self::KabahView => 'KV',
            self::Connecting => 'Connecting',
            self::Suite => 'Suite',
        };
    }
}
