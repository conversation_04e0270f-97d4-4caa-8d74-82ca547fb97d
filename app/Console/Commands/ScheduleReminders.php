<?php

namespace App\Console\Commands;

use App\Models\Reminder;
use App\Models\ReminderSchedule;
use App\Services\ReminderService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Relations\Relation;

class ScheduleReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminders:schedule';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Schedule active reminders';

    public function __construct(protected ReminderService $reminderService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $reminders = Reminder::query()->isActive()->get();
        foreach ($reminders as $reminder) {
            $this->scheduleReminder($reminder);
        }
    }

    private function scheduleReminder(Reminder $reminder)
    {
        $modelClass = $this->reminderService->getModelClass($reminder->reminder_type);
        $modelMorphAlias = Relation::getMorphAlias($modelClass);
        $models = $this->reminderService->findRelevantModels($reminder->reminder_type, $reminder->interval, $reminder->additional_data ?? []);

        foreach ($models as $model) {
            $eventDate = $this->reminderService->getEventDate($reminder->reminder_type, $model);

            $scheduledAt = $this->calculateScheduledAt($eventDate, $reminder);

            // Check for existing schedule
            $existingSchedule = ReminderSchedule::query()
                ->where('reminder_id', $reminder->id)
                ->where('model_type', $modelMorphAlias)
                ->where('model_id', $model->id)
                ->first();

            if (! $existingSchedule) {
                ReminderSchedule::create([
                    'reminder_id' => $reminder->id,
                    'model_type' => $modelMorphAlias,
                    'model_id' => $model->id,
                    'scheduled_at' => $scheduledAt,
                ]);
            }
        }
    }

    private function calculateScheduledAt(Carbon $eventDate, Reminder $reminder): Carbon
    {
        $scheduledDate = $eventDate->copy()->addDays($reminder->interval);
        $scheduledTime = Carbon::parse($reminder->time);

        return $scheduledDate->setTime(
            $scheduledTime->hour,
            $scheduledTime->minute,
            $scheduledTime->second
        );
    }
}
