<?php

namespace App\Console\Commands;

use App\Models\Finance\Invoice;
use App\Models\Group;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateInvoices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Group::query()
            ->whereNotNull('invoice_number')
            ->each(function ($group) {
                DB::transaction(function () use ($group) {
                    $invoice = Invoice::query()
                        ->where('invoice_number', $group->invoice_number)
                        ->first();

                    if (! $invoice) {
                        /** @var Invoice */
                        $invoice = Invoice::query()->create([
                            'invoice_date' => $group->created_at,
                            'invoice_number' => $group->invoice_number,
                            'customer_id' => $group->customer_id,
                            'due_date' => $group->created_at,
                            'subject' => $group->name ? $group->name : 'Group #'.$group->id,
                            'terms' => Invoice::DEFAULT_TERMS,
                        ]);
                        if ($group->invoice_amount) {
                            $invoice->items()->create([
                                'name' => 'Placeholder',
                                'quantity' => 1,
                                'unit_price' => $group->invoice_amount,
                            ]);
                        }
                    }

                    $group->update([
                        'invoice_id' => $invoice->id,
                        'invoice_number' => null,
                        'invoice_amount' => null,
                    ]);
                });
            });

        Invoice::query()
            ->whereHas('items')
            ->each(fn ($invoice) => $invoice->syncJournalEntry());
    }
}
