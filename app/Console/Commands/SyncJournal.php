<?php

namespace App\Console\Commands;

use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use Illuminate\Console\Command;

class SyncJournal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-journal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Invoice::all()->each(fn ($i) => $i->syncJournalEntry());
        InvoicePayment::all()->each(fn ($i) => $i->syncJournalEntry());
    }
}
