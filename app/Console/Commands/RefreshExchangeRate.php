<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Contracts\CurrencyHandler;
use App\Models\Currency;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class RefreshExchangeRate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exchange-rates:refresh
                            {--base=SAR : Base currency code}
                            {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh exchange rates for all currencies from external API';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $this->info('Starting exchange rate refresh...');

            $baseCurrency = $this->option('base');
            $isDryRun = $this->option('dry-run');

            // Get all currencies at once
            $currencies = $this->getCurrencies();

            if ($currencies->isEmpty()) {
                $this->warn('No currencies found in database.');

                return self::SUCCESS;
            }

            $this->info("Found {$currencies->count()} currencies to update.");

            // Get exchange rates from API
            $rates = $this->getExchangeRates($baseCurrency, $currencies);

            if (empty($rates)) {
                $this->error('Failed to retrieve exchange rates from API.');

                return self::FAILURE;
            }

            // Update currencies
            $updatedCount = $this->updateCurrencyRates($currencies, $rates, $isDryRun);

            $action = $isDryRun ? 'Would update' : 'Updated';
            $this->info("{$action} {$updatedCount} currency exchange rates.");

            Log::info('Exchange rates refreshed successfully', [
                'base_currency' => $baseCurrency,
                'updated_count' => $updatedCount,
                'dry_run' => $isDryRun,
            ]);

            return self::SUCCESS;

        } catch (Throwable $e) {
            $this->error("Failed to refresh exchange rates: {$e->getMessage()}");

            Log::error('Exchange rate refresh failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return self::FAILURE;
        }
    }

    /**
     * Get all currencies from database.
     */
    private function getCurrencies(): Collection
    {
        return Currency::query()
            ->select(['id', 'code', 'exchange_rate'])
            ->get();
    }

    /**
     * Get exchange rates from currency handler.
     */
    private function getExchangeRates(string $baseCurrency, Collection $currencies): ?array
    {
        $currencyHandler = app(CurrencyHandler::class);

        if (! $currencyHandler->isEnabled()) {
            $this->warn('Currency handler is disabled.');

            return null;
        }

        $currencyCodes = $currencies->pluck('code')->toArray();

        $this->info("Fetching rates for base currency: {$baseCurrency}");

        return $currencyHandler->getCachedExchangeRates($baseCurrency, $currencyCodes);
    }

    /**
     * Update currency exchange rates in database.
     */
    private function updateCurrencyRates(Collection $currencies, array $rates, bool $isDryRun): int
    {
        $updatedCount = 0;
        $updateData = [];

        // Prepare update data
        foreach ($currencies as $currency) {
            $newRate = $rates[$currency->code] ?? 1.0;

            // Skip if rate hasn't changed significantly (avoid unnecessary updates)
            if (abs($currency->exchange_rate - $newRate) < 0.0001) {
                continue;
            }

            if ($isDryRun) {
                $this->line("Would update {$currency->code}: {$currency->exchange_rate} → {$newRate}");
                $updatedCount++;

                continue;
            }

            $updateData[] = [
                'id' => $currency->id,
                'exchange_rate' => $newRate,
                'updated_at' => now(),
            ];

            $this->line("Updating {$currency->code}: {$currency->exchange_rate} → {$newRate}");
        }

        // Perform batch update if not dry run
        if (! $isDryRun && ! empty($updateData)) {
            DB::transaction(function () use ($updateData, &$updatedCount) {
                foreach ($updateData as $data) {
                    Currency::where('id', $data['id'])
                        ->update([
                            'exchange_rate' => $data['exchange_rate'],
                            'updated_at' => $data['updated_at'],
                        ]);
                    $updatedCount++;
                }
            });
        } elseif ($isDryRun) {
            // For dry run, count was already incremented in the loop
        }

        return $updatedCount;
    }
}
