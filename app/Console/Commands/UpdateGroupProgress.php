<?php

namespace App\Console\Commands;

use App\Models\Group;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Umrahservice\Groups\Enums\GroupProgress;

class UpdateGroupProgress extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-group-progress';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $count = Group::query()
            ->currentPeriod()
            ->confirmed()
            ->where('progress', GroupProgress::Waiting)
            ->whereDate('arrival_date', '<=', Carbon::now())
            ->update(['progress' => GroupProgress::Ongoing]);

        $count += Group::query()
            ->currentPeriod()
            ->confirmed()
            ->where('progress', GroupProgress::Ongoing)
            ->whereDate('departure_date', '<=', Carbon::now())
            ->update(['progress' => GroupProgress::Finishing]);

        $this->info("{$count} groups updated.");

        return Command::SUCCESS;
    }
}
