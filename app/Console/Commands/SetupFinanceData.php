<?php

namespace App\Console\Commands;

use App\Contracts\CurrencyHandler;
use App\Enums\Finance\AccountCategory;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\UserCash;
use App\Models\GroupCash;
use Illuminate\Console\Command;

class SetupFinanceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-finance-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (Currency::count() == 0) {
            $currencies = [
                'SAR' => 'Saudi Riyal',
                'IDR' => 'Indonesian Rupiah',
                'USD' => 'US Dollar',
            ];
            $rates = app(CurrencyHandler::class)->getCachedExchangeRates('SAR', array_keys($currencies));

            foreach ($currencies as $code => $name) {
                Currency::create([
                    'code' => $code,
                    'name' => $name,
                    'exchange_rate' => $rates[$code] ?? 1,
                ]);
            }
        }

        $accounts = [
            ['old_code' => '11101', 'code' => '1.101.001', 'category' => AccountCategory::Cash, 'name' => 'Kas Besar'],
            ['old_code' => '11102', 'code' => '1.101.002', 'category' => AccountCategory::Cash, 'name' => 'Kas Kecil'],
            ['old_code' => '11103', 'code' => '1.101.003', 'category' => AccountCategory::Cash, 'name' => 'Kas Kasir'],
            ['old_code' => '11104', 'code' => '1.101.004', 'category' => AccountCategory::Cash, 'name' => 'Kas Advance'],
            ['old_code' => '11301', 'code' => '1.104.001', 'category' => AccountCategory::AccountsReceivable, 'name' => 'Piutang Usaha'],
            ['old_code' => '21101', 'code' => '2.101.001', 'category' => AccountCategory::AccountsPayable, 'name' => 'Utang Usaha'],
            ['old_code' => '21201', 'code' => '2.102.001', 'category' => AccountCategory::CurrentLiability, 'name' => 'Deposit Customer'],
            ['old_code' => '31101', 'code' => '3.100.000', 'category' => AccountCategory::Equity, 'name' => 'Modal Usaha'],
            ['old_code' => '32101', 'code' => '3.101.001', 'category' => AccountCategory::Equity, 'name' => 'Laba Ditahan'],
            ['old_code' => '33101', 'code' => '3.102.001', 'category' => AccountCategory::Equity, 'name' => 'Deviden'],
            ['old_code' => '41100', 'code' => '4.100.000', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Penjualan'],
            ['old_code' => '41200', 'code' => '4.101.000', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Diskon Penjualan'],
            ['old_code' => '41300', 'code' => '4.102.000', 'category' => AccountCategory::OperatingRevenue, 'name' => 'Retur Penjualan'],
            ['old_code' => '51101', 'code' => '5.100.000', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'Harga Pokok Penjualan'],
            ['old_code' => '51102', 'code' => '5.100.001', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Tiket Pesawat'],
            ['old_code' => '51103', 'code' => '5.100.002', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Visa'],
            ['old_code' => '51104', 'code' => '5.100.003', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Hotel'],
            ['old_code' => '51105', 'code' => '5.100.004', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Airport Handling'],
            ['old_code' => '51106', 'code' => '5.100.005', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Katering'],
            ['old_code' => '51107', 'code' => '5.100.006', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Transportasi'],
            ['old_code' => '51108', 'code' => '5.100.007', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Perlengkapan'],
            ['old_code' => '51109', 'code' => '5.100.008', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'HPP - Snack'],
            ['code' => '5.101.000', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'Diskon Pembelian'],
            ['old_code' => '51201', 'code' => '5.102.000', 'category' => AccountCategory::CostOfGoodsSold, 'name' => 'Retur Pembelian'],
            ['old_code' => '52101', 'code' => '6.100.001', 'category' => AccountCategory::OperatingExpense, 'name' => 'Gaji'],
            ['code' => '6.100.002', 'category' => AccountCategory::OperatingExpense, 'name' => 'Tunjangan Hari Raya (THR)'],
            ['code' => '6.100.003', 'category' => AccountCategory::OperatingExpense, 'name' => 'Bonus & Insentif'],
            ['old_code' => '52103', 'code' => '6.200.000', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Operasional'],
            ['old_code' => '52109', 'code' => '6.200.001', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Asuransi'],
            ['old_code' => '52106', 'code' => '6.200.002', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Internet & Telepon'],
            ['old_code' => '52104', 'code' => '6.200.003', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Listrik'],
            ['old_code' => '52105', 'code' => '6.200.004', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Air'],
            ['old_code' => '52110', 'code' => '6.200.005', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Kebersihan & Keamanan'],
            ['old_code' => '52111', 'code' => '6.200.006', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Konsumsi'],
            ['old_code' => '52107', 'code' => '6.200.007', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya ATK & Perlengkapan Kantor'],
            ['old_code' => '52108', 'code' => '6.200.008', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Pemeliharaan'],
            ['old_code' => '52119', 'code' => '6.200.009', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Pajak'],
            ['old_code' => '52113', 'code' => '6.200.010', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Perjalanan Dinas'],
            ['old_code' => '52112', 'code' => '6.200.011', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Transportasi'],
            ['old_code' => '52114', 'code' => '6.200.012', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Pelatihan & Pengembangan'],
            ['old_code' => '52102', 'code' => '6.300.000', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Sewa'],
            ['old_code' => '52115', 'code' => '6.400.000', 'category' => AccountCategory::OperatingExpense, 'name' => 'Biaya Pemasaran'],
            ['code' => '7.100.000', 'category' => AccountCategory::UncategorizedRevenue, 'name' => 'Pendapatan Lain-lain'],
            ['old_code' => '52120', 'code' => '8.100.000', 'category' => AccountCategory::UncategorizedExpense, 'name' => 'Biaya Lain-lain'],
        ];
        $deletes = ['52116', '52117', '52118'];

        CashAccount::query()
            ->whereIn('code', $deletes)
            ->delete();

        foreach ($accounts as $account) {
            if (isset($account['old_code']) && CashAccount::query()->where('code', $account['old_code'])->exists()) {
                CashAccount::query()->updateOrCreate([
                    'code' => $account['old_code'],
                ], [
                    'code' => $account['code'],
                    'category' => $account['category'],
                    'name' => $account['name'],
                    'is_fixed' => true,
                ]);
            } else {
                CashAccount::query()->updateOrCreate([
                    'code' => $account['code'],
                ], [
                    'category' => $account['category'],
                    'name' => $account['name'],
                    'is_fixed' => true,
                ]);
            }
        }

        GroupCash::query()
            ->whereIn('division', ['handling_arr', 'handling_dep'])
            ->get()
            ->each(function ($gc) {
                UserCash::query()->create([
                    'user_id' => $gc->user_id,

                    'group_id' => $gc->group_id,
                    'category_id' => $gc->category_id,

                    'cashed_at' => $gc->cashed_at,

                    'type' => $gc->cash_in ? 'd' : 'c',
                    'amount' => $gc->cash_in ?? $gc->cash_out,
                    'currency' => $gc->currency,
                    'exchange_rate' => 1 / $gc->exchange_rate,

                    'details' => $gc->description,
                    'attachment' => $gc->attachment,
                ]);

                $gc->delete();
            });
    }
}
