<?php

namespace App\Console\Commands;

use App\Models\Finance\Invoice;
use App\Models\Finance\UserCash;
use App\Models\Group;
use App\Models\Bill;
use Illuminate\Console\Command;

class FixData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing data...');
        Group::query()
            ->with([
                'flights' => fn ($query) => $query->where('type', 'arrival')->orderBy('date_eta'),
                'group_hotels' => fn ($query) => $query->orderBy('check_in'),
            ])
            ->whereNull('arrival_date')
            ->each(function ($group) {
                if (filled($group->flights)) {
                    $group->update(['arrival_date' => $group->flights[0]->date_eta]);
                } elseif (filled($group->group_hotels)) {
                    $group->update(['arrival_date' => $group->group_hotels[0]->check_in]);
                }
            });

        Group::query()
            ->with([
                'flights' => fn ($query) => $query->where('type', 'departure')->orderByDesc('date_etd'),
                'group_hotels' => fn ($query) => $query->orderByDesc('check_out'),
            ])
            ->whereNull('departure_date')
            ->each(function ($group) {
                if (filled($group->flights)) {
                    $group->update(['departure_date' => $group->flights[0]->date_etd]);
                } elseif (filled($group->group_hotels)) {
                    $group->update(['departure_date' => $group->group_hotels[0]->check_out]);
                }
            });

        UserCash::query()
            ->whereDoesntHave('journal_entry')
            ->each(function (UserCash $cash) {
                $cash->syncJournalEntry();
            });
        UserCash::query()
            ->whereNotNull('related_type')
            ->whereNotNull('related_id')
            ->each(fn ($cash) => $cash->save());

        Invoice::query()
            ->where('total', 0)
            ->get()
            ->each(fn ($item) => $item->save());
        Bill::query()
            ->where('total', 0)
            ->get()
            ->each(fn ($item) => $item->save());

        $this->info('Done fixing data.');

        return Command::SUCCESS;
    }
}
