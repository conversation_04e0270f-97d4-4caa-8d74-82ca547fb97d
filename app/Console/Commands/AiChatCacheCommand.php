<?php

namespace App\Console\Commands;

use App\Services\AiChatService;
use Illuminate\Console\Command;

class AiChatCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai-chat:cache {action=refresh : The action to perform (refresh, clear, show)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage AI chat schema cache';

    /**
     * Execute the console command.
     */
    public function handle(AiChatService $aiChatService): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'clear':
                $aiChatService->clearSchemaCache();
                $this->info('AI chat schema cache cleared successfully.');
                break;

            case 'refresh':
                $aiChatService->clearSchemaCache();
                $this->info('Cache cleared. Warming up cache...');
                
                // Use reflection to access the private method for warming up
                $reflection = new \ReflectionClass($aiChatService);
                $method = $reflection->getMethod('getDatabaseSchemaInfo');
                $method->setAccessible(true);
                $method->invoke($aiChatService);
                
                $this->info('AI chat schema cache refreshed successfully.');
                break;

            case 'show':
                // Use reflection to access the private method
                $reflection = new \ReflectionClass($aiChatService);
                $method = $reflection->getMethod('getDatabaseSchemaInfo');
                $method->setAccessible(true);
                $schemaInfo = $method->invoke($aiChatService);
                
                $this->line($schemaInfo);
                break;

            default:
                $this->error("Unknown action: {$action}");
                $this->line('Available actions: refresh, clear, show');
                return 1;
        }

        return 0;
    }
}
