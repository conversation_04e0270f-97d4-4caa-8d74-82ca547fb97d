<?php

namespace App\Console\Commands;

use App\Enums\EstimateStatus;
use App\Models\Finance\Estimate;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class CheckExpiredEstimates extends Command
{
    protected $signature = 'estimates:check-expired';

    protected $description = 'Check for expired estimates and update their status';

    public function handle()
    {
        $expiredEstimates = Estimate::query()
            ->whereDate('valid_until', '<', Carbon::today())
            ->whereNotIn('status', [
                EstimateStatus::Invoiced->value,
                EstimateStatus::Expired->value,
                EstimateStatus::Declined->value,
            ])
            ->get();

        $count = 0;
        foreach ($expiredEstimates as $estimate) {
            $estimate->update(['status' => EstimateStatus::Expired]);
            $count++;
        }

        $this->info("Updated {$count} expired estimates");
    }
}
