<?php

namespace App\Console\Commands;

use App\Enums\ExpenseGroup;
use App\Models\Finance\CashCategory;
use App\Models\Finance\UserCash;
use App\Models\GroupCash;
use Illuminate\Console\Command;

class MigrateCashes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-cashes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $mutawifCashes = GroupCash::query()
            ->where('division', 'like', 'mutawif%')
            ->get();

        $mutawifCashes->each(function (GroupCash $c) {
            if ($c->user_id) {
                UserCash::create([
                    'user_id' => $c->user_id,

                    'group_id' => $c->group_id,
                    'category_id' => $c->category_id,

                    'cashed_at' => $c->cashed_at,

                    'type' => $c->cash_in > 0 ? 'd' : 'c',
                    'amount' => $c->cash_in ?? $c->cash_out,
                    'currency' => $c->currency,
                    'exchange_rate' => 1 / $c->exchange_rate,

                    'details' => $c->description,
                    'attachment' => $c->attachment,
                ]);
            }
            $c->forceDelete();
        });

        $checkinCashes = GroupCash::query()
            ->where('division', '=', 'check_in')
            ->get();

        $defaultCategoryId = CashCategory::where('group', ExpenseGroup::HotelCheckInOut)->where('name', 'like', '%op%')->value('id');
        $checkinCashes->each(function (GroupCash $c) use ($defaultCategoryId) {
            if ($c->user_id) {
                UserCash::create([
                    'user_id' => $c->user_id,

                    'group_id' => $c->group_id,
                    'category_id' => $c->group_id
                        ? $c->category_id ?? $defaultCategoryId
                        : $c->category_id,

                    'cashed_at' => $c->cashed_at,

                    'type' => $c->cash_in > 0 ? 'd' : 'c',
                    'amount' => $c->cash_in ?? $c->cash_out,
                    'currency' => $c->currency,
                    'exchange_rate' => 1 / $c->exchange_rate,

                    'details' => $c->description,
                    'attachment' => $c->attachment,
                ]);
            }
            $c->forceDelete();
        });

        $total = $mutawifCashes->count() + $checkinCashes->count();

        $this->info("$total items migrated.");
    }
}
