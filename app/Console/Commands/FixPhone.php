<?php

namespace App\Console\Commands;

use App\Models\Customer;
use Illuminate\Console\Command;

class FixPhone extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-phone';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Customer::all()
            ->each(function ($item) {
                if ($item->phone) {
                    $item->update([
                        'phone' => format_phone_for_db($item->phone),
                    ]);
                }
            });
    }
}
