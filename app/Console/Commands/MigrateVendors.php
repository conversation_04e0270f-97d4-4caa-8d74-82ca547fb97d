<?php

namespace App\Console\Commands;

use App\Models\Institution;
use App\Models\Transport;
use App\Models\Vendor;
use App\Models\Vendors\HotelBroker;
use Illuminate\Console\Command;

class MigrateVendors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-vendors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Institution::all()->each(function ($institution) {
            Vendor::query()->updateOrCreate([
                'original_id' => $institution->id,
                'vendor_type' => 'muassasah',
            ], [
                'company_name' => $institution->name,
                'contact_email' => $institution->email,
                'contact_phone' => format_phone_for_db($institution->phone),
            ]);
        });

        HotelBroker::all()->each(function ($hotelBroker) {
            Vendor::query()->updateOrCreate([
                'original_id' => $hotelBroker->id,
                'vendor_type' => 'hotel',
            ], [
                'company_name' => $hotelBroker->company_name,
                'contact_name' => $hotelBroker->contact_name,
                'contact_phone' => format_phone_for_db($hotelBroker->contact_phone),
            ]);
        });

        Transport::all()->each(function ($transport) {
            Vendor::query()->updateOrCreate([
                'original_id' => $transport->id,
                'vendor_type' => 'transportation',
            ], [
                'company_name' => $transport->company_name,
            ]);
        });

        return Command::SUCCESS;
    }
}
