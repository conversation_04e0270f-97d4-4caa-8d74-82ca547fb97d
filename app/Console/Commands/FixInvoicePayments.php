<?php

namespace App\Console\Commands;

use App\Models\Finance\Invoice;
use Illuminate\Console\Command;

class FixInvoicePayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-invoice-payments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Invoice::query()
            ->where('currency_code', 'SAR')
            ->with('payments')
            ->each(function ($invoice) {
                $invoice->payments->each(function ($payment) {
                    $payment->update([
                        'currency_code' => 'SAR',
                        'exchange_rate' => 1,
                        'amount' => $payment->amount * $payment->exchange_rate,
                    ]);
                });
                $invoice->save();
            });
        Invoice::query()
            ->where('currency_code', '!=', 'SAR')
            ->with('payments')
            ->each(function ($invoice) {
                $invoice->payments->each(function ($payment) use ($invoice) {
                    if ($payment->currency_code == $invoice->currency_code) {
                        return;
                    }
                    $payment->update([
                        'currency_code' => $invoice->currency_code,
                        'exchange_rate' => $invoice->exchange_rate,
                    ]);
                });
                $invoice->save();
            });
    }
}
