<?php

namespace App\Console\Commands;

use App\Enums\UserRole;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SetupRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $permissions = array_merge(
            $this->modelPermissions('users'),
            $this->modelPermissions('user-cashes'),
            $this->modelPermissions('groups'),
            $this->modelPermissions('customers'),
            $this->modelPermissions('vendors'),
            $this->modelPermissions('airlines'),
            $this->modelPermissions('airports'),
            $this->modelPermissions('institutions'),
            $this->modelPermissions('transports'),
            $this->modelPermissions('vehicles'),
            $this->modelPermissions('hotels'),
            $this->modelPermissions('hotel-brokers'),
            $this->modelPermissions('mutawifs'),
            [
                'groups.viewData', 'groups.updateData', 'groups.deleteData', 'groups.roomlist',
                'hotels.confirm',
                'widget.arrivals', 'widget.departures', 'widget.check-ins', 'widget.check-outs', 'widget.schedule-calendar',
                'unlock-resource',
                'view-userstamps',
            ],

            $this->modelPermissions('invoices'),
            $this->modelPermissions('invoice-payments'),
            [
                'finance.has-user-cash',

                'accounting.view-data',
                'accounting.update-data',
            ],

            [
                'panel.admin',
                'panel.finance',
            ]
        );
        $roles = collect(UserRole::cases())->map(fn ($c) => $c->value)->toArray();

        collect($permissions)->each(fn ($perm) => Permission::findOrCreate($perm));
        collect($roles)->each(fn ($role) => Role::findOrCreate($role));

        Role::findByName('Admin')->syncPermissions([
            'panel.admin', 'panel.finance',

            ...$this->modelPermissions('users'),
            'groups.roomlist',
            ...$this->modelPermissions('user-cashes'),
            ...$this->modelPermissions('hotels'),
            ...$this->modelPermissions('vehicles'),
            ...$this->modelPermissions('invoices'),
            ...$this->modelPermissions('invoice-payments'),
            'widget.schedule-calendar',
            'hotels.confirm',
            'unlock-resource',
            'view-userstamps',
            'accounting.view-data',
            'accounting.update-data',
            ...$this->modelPermissions('vendors'),
        ]);
        Role::findByName('Finance')->syncPermissions([
            'panel.admin', 'panel.finance',

            // ...$this->modelPermissions('users', ['viewAny']),
            ...$this->modelPermissions('user-cashes'),
            ...$this->modelPermissions('groups', ['viewAny', 'view', 'create', 'update', 'delete', 'viewData', 'updateData']),
            'hotels.confirm',
            ...$this->modelPermissions('customers', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('invoices'),
            ...$this->modelPermissions('invoice-payments'),
            'widget.arrivals', 'widget.departures', 'widget.check-ins', 'widget.check-outs', 'widget.schedule-calendar',

            'accounting.view-data',
            'accounting.update-data',
            'view-userstamps',
            ...$this->modelPermissions('vendors'),
        ]);
        Role::findByName('Operator')->syncPermissions([
            'groups.roomlist',
            'hotels.confirm',
            ...$this->modelPermissions('hotel-brokers', except: ['delete', 'restore', 'forceDelete']),

            'finance.has-user-cash',
        ]);
        Role::findByName('Admin Operator')->syncPermissions([
            'panel.admin',

            ...$this->modelPermissions('groups', ['viewAny', 'view', 'create', 'update', 'delete', 'viewData', 'updateData']),
            'groups.roomlist',
            'hotels.confirm',
            ...$this->modelPermissions('customers', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('airlines', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('airports', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('institutions', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('transports', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('vehicles', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('hotels', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('hotel-brokers', ['viewAny', 'view', 'create', 'update']),
            ...$this->modelPermissions('mutawifs', ['viewAny', 'view', 'create', 'update']),
            'widget.arrivals', 'widget.departures', 'widget.check-ins', 'widget.check-outs', 'widget.schedule-calendar',

            'finance.has-user-cash',

            'unlock-resource',
        ]);
        Role::findByName('Check-In Team')->syncPermissions([
            'finance.has-user-cash',
            'groups.viewAny', 'groups.view', 'groups.viewData', 'groups.roomlist',
            'widget.check-ins', 'widget.check-outs', 'widget.schedule-calendar',
        ]);
        Role::findByName('Airport Handler')->syncPermissions([
            'finance.has-user-cash',
        ]);
        Role::findByName('Snack Handler')->syncPermissions([
            'finance.has-user-cash',
        ]);
        Role::findByName('Mutawif')->syncPermissions([
            'finance.has-user-cash',
        ]);
        Role::findByName('Customer')->syncPermissions([
            'groups.viewAny',
        ]);
        Role::findByName('Accountant')->syncPermissions([
            'panel.finance',

            'accounting.view-data',
        ]);

        return Command::SUCCESS;
    }

    private function modelPermissions(string $model, array $only = [], array $except = [])
    {
        return collect(filled($only) ? $only : ['viewAny', 'view', 'create', 'update', 'delete', 'restore', 'forceDelete'])
            ->diff($except)
            ->map(fn ($p) => "{$model}.{$p}")
            ->toArray();
    }
}
