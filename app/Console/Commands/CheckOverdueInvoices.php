<?php

namespace App\Console\Commands;

use App\Enums\InvoiceStatus;
use App\Models\Finance\Invoice;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class CheckOverdueInvoices extends Command
{
    protected $signature = 'invoices:check-overdue';

    protected $description = 'Check for overdue invoices and update their status';

    public function handle()
    {
        $overdueInvoices = Invoice::query()
            ->whereDate('due_date', '<', Carbon::today())
            ->whereIn('status', [
                InvoiceStatus::Unpaid->value,
                InvoiceStatus::PaidPartial->value,
            ])
            ->get();

        $count = 0;
        foreach ($overdueInvoices as $invoice) {
            $invoice->update(['status' => InvoiceStatus::Overdue]);
            $count++;
        }

        $this->info("Updated {$count} overdue invoices");
    }
}
