<?php

namespace App\Console\Commands;

use App\Enums\NotificationType;
use App\Enums\ReminderRecipientType;
use App\Jobs\SendNotification;
use App\Models\ReminderRecipient;
use App\Models\ReminderSchedule;
use App\Models\User;
use App\Services\ReminderService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class SendReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminders:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send scheduled reminders';

    public function __construct(protected ReminderService $reminderService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $schedules = ReminderSchedule::query()
            ->with(['reminder', 'model'])
            ->whereNull('sent_at')
            ->where('scheduled_at', '<=', now())
            ->get();

        $this->info("Found {$schedules->count()} reminders to send.");

        foreach ($schedules as $schedule) {
            $this->sendReminder($schedule);
        }

        $this->info('Finished sending reminders.');
    }

    protected function sendReminder(ReminderSchedule $schedule)
    {
        $reminder = $schedule->reminder;
        $model = $schedule->model;

        if (! $reminder || ! $model) {
            $schedule->update(['sent_at' => now()]);
            $this->error("Reminder or model not found for schedule {$schedule->id}");

            return;
        }

        $this->info("Sending reminder {$reminder->id} for {$schedule->model_type} {$model->id}");

        try {
            $content = $this->reminderService->generateContent($reminder, $model);
            $contacts = collect();

            foreach ($reminder->recipients as $recipient) {
                $contacts->push(...$this->getRecipientContacts(
                    $recipient, $reminder->notification_type, $model));
            }

            $contacts = $contacts->unique(fn ($contact) => $contact['contact']);

            foreach ($contacts as $contact) {
                $delay = $reminder->notification_type === NotificationType::WhatsApp
                    ? mt_rand(20, 120)
                    : null;

                SendNotification::dispatch(
                    $reminder->notification_type,
                    $contact['contact'],
                    str_replace('{recipient_name}', $contact['name'], $content),
                    $reminder->subject ?? 'Reminder',
                )->delay(now()->addSeconds($delay));
            }

            $schedule->update(['sent_at' => now()]);
            $this->info('Reminder sent successfully.');
        } catch (\Exception $e) {
            // dd($e);
            Log::error('Failed to send reminder: ' . $e->getMessage(), [
                'reminder_id' => $reminder->id,
                'model_type' => $schedule->model_type,
                'model_id' => $model->id,
            ]);
            $this->error('Failed to send reminder: ' . $e->getMessage());
        }
    }

    protected function getRecipientContacts(ReminderRecipient $recipient, NotificationType $type, Model $model): array
    {
        $field = $type === NotificationType::Email ? 'email' : 'phone';

        switch ($recipient->recipient_type) {
            case ReminderRecipientType::User:
                $user = User::verified()->find($recipient->recipient_id);

                return $user?->$field ? [
                    [
                        'name' => $user->name,
                        'contact' => $user->$field,
                    ],
                ] : [];
            case ReminderRecipientType::Role:
                return User::verified()->role($recipient->recipient_id)->get()
                    ->map(fn ($u) => ['name' => $u->name, 'contact' => $u->$field])
                    ->filter(fn ($u) => (bool) $u['contact'])
                    ->values()
                    ->toArray();
            case ReminderRecipientType::Customer:
                if (method_exists($model, 'customer')) {
                    $customer = $model->customer;
                    $name = $customer->owner_name ?? $customer->name;
                    $contact = $customer->$field;

                    return $contact ? [['name' => $name, 'contact' => $contact]] : [];
                }

                return [];
            case ReminderRecipientType::Handler:
                if (method_exists($model, 'getUserHandlers')) {
                    $userHandlers = $model->getUserHandlers();

                    return $userHandlers
                        ? $userHandlers->map(fn ($user) => ['name' => $user->name, 'contact' => $user->$field])
                            ->toArray()
                        : [];
                }

                return [];
            default:
                return [];
        }
    }
}
