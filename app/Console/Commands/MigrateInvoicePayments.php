<?php

namespace App\Console\Commands;

use App\Enums\PaymentMethod;
use App\Models\Finance\CashAccount;
use App\Models\Finance\InvoicePayment;
use App\Models\GroupCash;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateInvoicePayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-invoice-payments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $items = GroupCash::query()
            ->with(['group.invoice'])
            ->where('cash_in', '>', 0)
            ->where('division', 'payment')
            ->whereHas('group', function ($query) {
                $query->whereHas('invoice');
            })
            ->get();

        $account_id = CashAccount::query()
            ->where('code', config('finance.coa.main_cash'))
            ->value('id');

        $items->each(function ($i) use ($account_id) {
            DB::beginTransaction();
            InvoicePayment::create([
                'invoice_id' => $i->group->invoice_id,

                'payment_method' => PaymentMethod::Cash,
                'cash_account_id' => $account_id,

                'paid_at' => $i->cashed_at,
                'amount' => $i->cash_in,
                'currency_code' => $i->currency,
                'exchange_rate' => 1 / $i->exchange_rate,

                'description' => $i->description,
                'attachment' => $i->attachment,

                'created_by_id' => $i->user_id,
                'updated_by_id' => $i->user_id,
            ]);
            $i->delete();
            DB::commit();
        });

        $this->info($items->count() . ' items migrated');
    }
}
