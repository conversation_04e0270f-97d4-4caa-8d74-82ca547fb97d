<?php

namespace App\Console\Commands;

use App\Models\Airport;
use Illuminate\Console\Command;

class SetupData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Airport::query()
            ->updateOrCreate([
                'code' => 'CGK T1',
            ], [
                'name' => 'Soekarno-Hatta International Airport (Terminal 1)',
                'city' => 'Jakarta',
                'country' => 'Indonesia',
                'iata' => 'CGK',
                'icao' => 'WIII',
                'latitude' => -6.1256,
                'longitude' => 106.6558,
                'timezone' => 7,
            ]);

        Airport::query()
            ->updateOrCreate([
                'code' => 'CGK T2',
            ], [
                'name' => 'Soekarno-Hatta International Airport (Terminal 2)',
                'city' => 'Jakarta',
                'country' => 'Indonesia',
                'iata' => 'CGK',
                'icao' => 'WIII',
                'latitude' => -6.1256,
                'longitude' => 106.6558,
                'timezone' => 7,
            ]);

        Airport::query()
            ->updateOrCreate([
                'code' => 'CGK T3',
            ], [
                'name' => 'Soekarno-Hatta International Airport (Terminal 3)',
                'city' => 'Jakarta',
                'country' => 'Indonesia',
                'iata' => 'CGK',
                'icao' => 'WIII',
                'latitude' => -6.1256,
                'longitude' => 106.6558,
                'timezone' => 7,
            ]);

        Airport::query()
            ->updateOrCreate([
                'code' => 'JED N',
            ], [
                'name' => 'King Abdulaziz International Airport (North Terminal)',
                'city' => 'Jeddah',
                'country' => 'Saudi Arabia',
                'iata' => 'JED',
                'icao' => 'OEJN',
                'latitude' => 21.6796,
                'longitude' => 39.1565,
                'timezone' => 3,
            ]);

        Airport::query()
            ->updateOrCreate([
                'code' => 'JED T1',
            ], [
                'name' => 'King Abdulaziz International Airport (Terminal 1)',
                'city' => 'Jeddah',
                'country' => 'Saudi Arabia',
                'iata' => 'JED',
                'icao' => 'OEJN',
                'latitude' => 21.6796,
                'longitude' => 39.1565,
                'timezone' => 3,
            ]);

        Airport::query()
            ->updateOrCreate([
                'code' => 'JED H',
            ], [
                'name' => 'King Abdulaziz International Airport (Hajj Terminal)',
                'city' => 'Jeddah',
                'country' => 'Saudi Arabia',
                'iata' => 'JED',
                'icao' => 'OEJN',
                'latitude' => 21.6796,
                'longitude' => 39.1565,
                'timezone' => 3,
            ]);

        Airport::query()
            ->updateOrCreate([
                'code' => 'MED',
            ], [
                'name' => 'Prince Mohammad bin Abdulaziz International Airport',
                'city' => 'Madinah',
                'country' => 'Saudi Arabia',
                'iata' => 'MED',
                'icao' => 'OEMA',
                'latitude' => 24.5534,
                'longitude' => 39.7056,
                'timezone' => 3,
            ]);
    }
}
