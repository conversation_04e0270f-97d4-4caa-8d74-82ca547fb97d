<?php

namespace App\Livewire;

use App\Models\Period;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class PeriodPicker extends Component
{
    protected string $view = 'livewire.period-picker';

    public function render()
    {
        return view($this->view, [
            'current_period' => current_period(),
            'periods' => Period::all(),
        ]);
    }

    public function setPeriod($period_id)
    {
        Gate::allowIf(auth()->user()->hasRole(['Admin', 'Finance']));

        Session::put('period_id', $period_id);

        return redirect(request()->header('Referer'));
    }
}
