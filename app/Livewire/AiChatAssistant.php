<?php

namespace App\Livewire;

use App\Services\AiChatService;
use Livewire\Attributes\On;
use Livewire\Component;

class AiChatAssistant extends Component
{
    public bool $isOpen = false;

    public string $query = '';

    public array $conversation = [];

    public bool $isLoading = false;

    public bool $isTyping = false;

    public string $currentResponse = '';

    public int $currentMessageIndex = -1;

    public function mount()
    {
        $this->conversation = session('ai_chat_conversation', []);
    }

    public function toggleChat()
    {
        $this->isOpen = ! $this->isOpen;
    }

    #[On('open-ai-chat')]
    public function openChat()
    {
        $this->isOpen = true;
    }

    public function sendQuery()
    {
        if (empty(trim($this->query))) {
            return;
        }

        $this->isLoading = true;
        $this->isTyping = false;
        $this->currentResponse = '';

        // Add user message to conversation
        $this->conversation[] = [
            'role' => 'user',
            'content' => $this->query,
            'timestamp' => now()->toDateTimeString(),
        ];

        // Store the query to process
        $queryText = $this->query;

        // Clear input
        $this->query = '';

        // Add placeholder for AI response
        $this->currentMessageIndex = count($this->conversation);
        $this->conversation[] = [
            'role' => 'assistant',
            'content' => '',
            'data' => null,
            'timestamp' => now()->toDateTimeString(),
            'streaming' => true,
        ];

        // Start typing indicator
        $this->isTyping = true;
        $this->isLoading = false;

        // Dispatch to start streaming
        $this->dispatch('start-ai-response', queryText: $queryText);
    }

    #[On('process-ai-query')]
    public function processAiQuery($queryText)
    {
        try {
            // Process the query
            $aiChatService = app(AiChatService::class);
            $result = $aiChatService->processQuery($queryText);

            if ($result['success']) {
                // Simulate streaming by breaking response into chunks
                $this->streamResponse($result['response'], $result['data']);
            } else {
                $this->finishResponse('Sorry, I couldn\'t process your query.', null);
            }
        } catch (\Exception $e) {
            $this->finishResponse('An error occurred: ' . $e->getMessage(), null);
        }
    }

    private function streamResponse($response, $data)
    {
        // For a more reliable approach, let's just show typing indicator and then the full response
        // The typing effect will be handled by the frontend
        $this->finishResponse($response, $data);
    }

    private function finishResponse($response, $data)
    {
        $this->isTyping = false;
        $this->conversation[$this->currentMessageIndex] = [
            'role' => 'assistant',
            'content' => $response,
            'data' => $data,
            'timestamp' => now()->toDateTimeString(),
            'streaming' => false,
        ];

        // Save conversation to session
        session(['ai_chat_conversation' => $this->conversation]);
    }

    public function clearConversation()
    {
        $this->conversation = [];
        session(['ai_chat_conversation' => []]);
    }

    public function render()
    {
        return view('livewire.ai-chat-assistant');
    }
}
