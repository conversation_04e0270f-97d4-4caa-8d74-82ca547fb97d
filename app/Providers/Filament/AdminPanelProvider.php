<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Backups;
use App\Filament\Widgets\ArrivalScheduleWidget;
use App\Filament\Widgets\CheckInScheduleWidget;
use App\Filament\Widgets\CheckOutScheduleWidget;
use App\Filament\Widgets\DepartureScheduleWidget;
use App\Filament\Widgets\ItinerariesScheduleWidget;
use App\Http\Middleware\SetPeriod;
use App\Services\Alerts;
use Awcodes\FilamentQuickCreate\QuickCreatePlugin;
use Filament\Forms\Components\DateTimePicker;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationGroup;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Tables\Table;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Kenepa\ResourceLock\ResourceLockPlugin;
use pxlrbt\FilamentEnvironmentIndicator\EnvironmentIndicatorPlugin;
use Rmsramos\Activitylog\ActivitylogPlugin;
use Saade\FilamentFullCalendar\FilamentFullCalendarPlugin;
use Saade\FilamentLaravelLog\FilamentLaravelLogPlugin;
use ShuvroRoy\FilamentSpatieLaravelBackup\FilamentSpatieLaravelBackupPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            // ->spa()
            ->id('admin')
            ->path('admin')
            ->login()
            ->registration()
            ->navigationGroups([
                NavigationGroup::make()
                    ->label('Schedules')
                    ->icon('heroicon-o-calendar-days')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Statistics')
                    ->icon('heroicon-o-presentation-chart-bar')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Groups')
                    ->icon('heroicon-o-user-group'),
                NavigationGroup::make()
                    ->label('Hotels')
                    ->icon('heroicon-o-building-office'),
                NavigationGroup::make()
                    ->label('Finance')
                    ->icon('heroicon-o-banknotes')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Master Data')
                    ->icon('heroicon-o-squares-2x2')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Vendors')
                    ->icon('heroicon-o-building-storefront')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Users')
                    ->icon('heroicon-o-users')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Settings'),
                NavigationGroup::make()
                    ->label('System'),
            ])
            ->colors([
                'primary' => Color::hex('#1a2b48'),
                'whatsapp' => Color::hex('#128c7e'),
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets/Dashboard'), for: 'App\\Filament\\Widgets\\Dashboard')
            ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->widgets([
                ArrivalScheduleWidget::class,
                DepartureScheduleWidget::class,
                CheckInScheduleWidget::class,
                CheckOutScheduleWidget::class,
                ItinerariesScheduleWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetPeriod::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                BreezyCore::make()
                    ->myProfile(),
                EnvironmentIndicatorPlugin::make()
                    ->visible(fn () => auth()->user()?->hasRole('Admin')),
                FilamentSpatieLaravelBackupPlugin::make()
                    ->usingPage(Backups::class),
                ResourceLockPlugin::make(),
                FilamentFullCalendarPlugin::make()
                    ->config([
                        'buttonText' => [
                            'today' => 'Today',
                            'month' => 'Month',
                            'week' => 'Week',
                            'day' => 'Day',
                        ],
                        'eventTimeFormat' => [
                            'hour' => '2-digit',
                            'minute' => '2-digit',
                            'meridiem' => false,
                            'hour12' => false,
                        ],
                    ]),
                QuickCreatePlugin::make()
                    ->includes([
                        \App\Filament\Resources\CustomerResource::class,
                        \App\Filament\Resources\GroupResource::class,
                    ])
                    ->sort(false)
                    ->rounded(false)
                    ->label('New')
                    ->renderUsingHook(PanelsRenderHook::TOPBAR_START),
                ActivitylogPlugin::make()
                    ->resource(\App\Filament\Resources\ActivitylogResource::class)
                    ->authorize(fn () => auth('web')->user()->hasRole('Admin')),
                FilamentLaravelLogPlugin::make()
                    ->navigationGroup('System')
                    ->navigationLabel('Logs')
                    ->navigationIcon('heroicon-o-bug-ant')
                    ->slug('logs')
                    ->authorize(fn () => auth()->user()->isSuperAdmin()),
            ])
            ->bootUsing(function () {
                Table::$defaultDateDisplayFormat = 'd-M-y';
                Table::$defaultDateTimeDisplayFormat = 'd-M-y H:i';

                DateTimePicker::$defaultDateDisplayFormat = 'd-M-y';
                DateTimePicker::$defaultDateTimeDisplayFormat = 'd-M-y H:i';
                DateTimePicker::$defaultDateTimeWithSecondsDisplayFormat = 'd-M-y H:i:s';
            })
            ->renderHook(
                PanelsRenderHook::BODY_END,
                fn () => Blade::render('@livewire(\'ai-chat-assistant\')')
            )
            ->renderHook(PanelsRenderHook::CONTENT_START, fn () => Alerts::render())
            ->renderHook(
                PanelsRenderHook::AUTH_LOGIN_FORM_AFTER,
                fn (): string => Blade::render("@env('local')<x-login-link />@endenv")
            );
    }
}
