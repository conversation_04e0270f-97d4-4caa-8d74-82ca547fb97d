<?php

namespace App\Jobs;

use App\Enums\NotificationType;
use App\Mail\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public NotificationType $type,
        public string $recipient,
        public string $content,
        public ?string $subject
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting SendNotification job', [
            'type' => $this->type,
            'recipient' => $this->recipient,
            'subject' => $this->subject,
        ]);

        if (! app()->environment('production')) {
            Log::info('Notification not sent in non-production environment', [
                'type' => $this->type,
                'recipient' => $this->recipient,
                'subject' => $this->subject,
                'content' => $this->content,
            ]);
        } else {
            switch ($this->type) {
                case NotificationType::Email:
                    try {
                        Mail::to($this->recipient)
                            ->send(
                                new Notification($this->subject, $this->content)
                            );
                        Log::info('Email sent successfully', [
                            'recipient' => $this->recipient,
                            'subject' => $this->subject,
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Failed to send email', [
                            'recipient' => $this->recipient,
                            'subject' => $this->subject,
                            'error' => $e->getMessage(),
                        ]);
                    }
                    break;
                case NotificationType::WhatsApp:
                    if (whatsapp()->sendText($this->recipient, $this->content)) {
                        Log::info('WhatsApp message sent successfully', [
                            'recipient' => $this->recipient,
                        ]);
                    } else {
                        Log::error('Failed to send WhatsApp message, retrying...', [
                            'recipient' => $this->recipient,
                        ]);
                        $this->release(mt_rand(20, 120));
                    }
                    break;
            }
        }
    }
}
