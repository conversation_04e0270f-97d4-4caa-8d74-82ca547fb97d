<?php

namespace App\Exports;

use App\Models\Vendors\SnackHandler;
use App\Settings\SnackSettings;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class SnackExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Snacks';

    public function __construct(protected Builder $query, private array $filters = [], private string $type = 'xlsx')
    {
        $fileName = [$this->fileName];
        $month = $filters['month']['month'] ?? null;
        $monthCarbon = $month
            ? Carbon::parse("{$month}-01")
            : today()->startOfMonth();

        $fileName[] = $monthCarbon->format('F Y');

        $city = $filters['city']['value'] ?? null;
        if ($city) {
            $fileName[] = $city;
        }

        $snackHandlerId = $filters['snack_handler_id']['value'] ?? null;
        if ($snackHandlerId) {
            $fileName[] = SnackHandler::find($snackHandlerId)?->company_name;
        }

        $this->fileName = collect($fileName)
            ->filter()
            ->join(' - ')
            . ".{$type}";
    }

    public function query()
    {
        return $this->query;
    }

    public function columnFormats(): array
    {
        return $this->type === 'csv' ? [
            'A' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ] : [
            'A' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    public function headings(): array
    {
        return [
            'Date',
            'Customer',
            'Group ID',
            'Group',
            'Total Pax',
            'Trip',
            'Description',
            'City',
            'Snack Handler',
            'Snack Type',
            'Snack Price',
        ];
    }

    public function map($row): array
    {
        $settings = app(SnackSettings::class);

        return [
            Date::dateTimeToExcel($row->date),
            $row->group->customer->name,
            $row->group_id,
            $row->group->name,
            $row->group->total_pax,
            $row->location,
            $row->description,
            $row->city,
            $row->snack_handler?->company_name ?? '',
            $row->snack_details,
            (float) ($settings->prices[$row->snack_details] ?? 0),
        ];
    }
}
