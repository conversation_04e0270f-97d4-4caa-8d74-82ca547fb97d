<?php

namespace App\Exports;

use App\Models\Customer;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CustomerExport implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Customers.xlsx';

    public function collection()
    {
        return Customer::query()
            ->with(['latest_group'])
            ->orderBy('name')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Customer ID',
            'Company Name',
            'Owner Name',
            'Email',
            'Phone',
            'Address',
            'Latest Arrival',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'G' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }

    public function map($row): array
    {
        $latest_arrival = $row->latest_group?->arrival_date ?? null;
        try {
            $phone = $row->phone ? format_phone($row->phone) : '';
        } catch (\Throwable $th) {
            $phone = $row->phone;
        }

        return [
            $row->id,
            $row->name,
            $row->owner_name,
            $row->email,
            $phone,
            $row->address,
            $latest_arrival ? Date::dateTimeToExcel($latest_arrival) : '',
        ];
    }
}
