<?php

namespace App\Exports;

use App\Enums\Finance\AccountCategory;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class GeneralLedgerExport implements WithMultipleSheets
{
    use Exportable;

    private $data;

    private $period;

    private $fileName;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
        $this->fileName = 'general-ledger-report-' . $period['start'] . '-to-' . $period['end'] . '.xlsx';
    }

    public function sheets(): array
    {
        $sheets = [];

        // Summary sheet
        $sheets[] = new GeneralLedgerSummarySheet($this->data, $this->period);

        // Individual account sheets (limit to prevent too many sheets)
        $accountCount = 0;
        foreach ($this->data['accounts'] as $accountData) {
            if ($accountCount >= 10) { // Limit to 10 account sheets
                break;
            }
            if (count($accountData['transactions']) > 0) {
                $sheets[] = new GeneralLedgerAccountSheet($accountData, $this->period, $this->data['exchange_rate'], $this->data['currency_code']);
                $accountCount++;
            }
        }

        return $sheets;
    }
}

class GeneralLedgerSummarySheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $data;

    private $period;

    public function __construct($data, $period)
    {
        $this->data = $data;
        $this->period = $period;
    }

    public function collection()
    {
        $collection = collect();

        foreach ($this->data['accounts'] as $accountData) {
            $totalDebits = collect($accountData['transactions'])->sum('debit');
            $totalCredits = collect($accountData['transactions'])->sum('credit');

            $collection->push((object) [
                'account_code' => $accountData['account']['code'],
                'account_name' => $accountData['account']['name'],
                'category' => AccountCategory::from($accountData['account']['category'])->getLabel(),
                'opening_balance' => $accountData['opening_balance'],
                'total_debits' => $totalDebits,
                'total_credits' => $totalCredits,
                'closing_balance' => $accountData['closing_balance'],
                'transaction_count' => count($accountData['transactions']),
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Account Code',
            'Account Name',
            'Category',
            'Opening Balance',
            'Total Debits',
            'Total Credits',
            'Closing Balance',
            'Transaction Count',
        ];
    }

    public function map($row): array
    {
        return [
            $row->account_code,
            $row->account_name,
            $row->category,
            $row->opening_balance * $this->data['exchange_rate'],
            $row->total_debits * $this->data['exchange_rate'],
            $row->total_credits * $this->data['exchange_rate'],
            $row->closing_balance * $this->data['exchange_rate'],
            $row->transaction_count,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return 'Summary';
    }
}

class GeneralLedgerAccountSheet implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithTitle
{
    private $accountData;

    private $period;

    private $exchangeRate;

    private $currencyCode;

    public function __construct($accountData, $period, $exchangeRate, $currencyCode)
    {
        $this->accountData = $accountData;
        $this->period = $period;
        $this->exchangeRate = $exchangeRate;
        $this->currencyCode = $currencyCode;
    }

    public function collection()
    {
        $collection = collect();

        // Add opening balance row if not zero
        if ($this->accountData['opening_balance'] != 0) {
            $collection->push((object) [
                'date' => Carbon::parse($this->period['start'])->subDay(),
                'details' => 'Opening Balance',
                'debit' => null,
                'credit' => null,
                'balance' => $this->accountData['opening_balance'],
                'is_opening' => true,
            ]);
        }

        // Add transactions
        foreach ($this->accountData['transactions'] as $transaction) {
            $collection->push((object) [
                'date' => Carbon::parse($transaction['date']),
                'details' => $transaction['details'],
                'debit' => $transaction['debit'] > 0 ? $transaction['debit'] : null,
                'credit' => $transaction['credit'] > 0 ? $transaction['credit'] : null,
                'balance' => $transaction['balance'],
                'is_opening' => false,
            ]);
        }

        return $collection;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Description',
            'Debit',
            'Credit',
            'Balance',
        ];
    }

    public function map($row): array
    {
        return [
            Date::dateTimeToExcel($row->date),
            $row->details,
            $row->debit ? $row->debit * $this->exchangeRate : null,
            $row->credit ? $row->credit * $this->exchangeRate : null,
            $row->balance * $this->exchangeRate,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function title(): string
    {
        return substr($this->accountData['account']['code'] . ' - ' . $this->accountData['account']['name'], 0, 31);
    }
}
