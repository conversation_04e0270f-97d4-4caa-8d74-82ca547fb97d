<?php

namespace App\Exports;

use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class GeneralLedgerExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    private $filters;

    public function __construct($filters)
    {
        $this->filters = $filters;
    }

    public function query()
    {
        return JournalEntryItem::query()
            ->leftJoin('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->when($this->filters['entry_date']['entry_date'] ?? null, function ($query, $range) {
                $dates = explode(' - ', $range);
                $query->whereBetween('entry_date', [
                    Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                    Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                ]);
            })
            ->when($this->filters['account_id']['value'] ?? null, function ($query, $accountId) {
                $query->where('account_id', $accountId);
            })
            ->when($this->filters['type']['value'] ?? null, function ($query, $type) {
                $query->where('type', $type);
            })
            ->select([
                'journal_entries.entry_date',
                'journal_entries.details',
                'journal_entry_items.*',
                DB::raw("IF(type = 'd', amount, null) AS debit"),
                DB::raw("IF(type = 'c', amount, null) AS credit"),
            ])
            ->withCasts([
                'entry_date' => 'datetime',
            ])
            ->orderBy('entry_date');
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function headings(): array
    {
        return [
            'Date',
            'Details',
            'Debit',
            'Credit',
        ];
    }

    public function map($row): array
    {
        return [
            Date::dateTimeToExcel($row->entry_date),
            $row->details,
            $row->debit,
            $row->credit,
        ];
    }
}
