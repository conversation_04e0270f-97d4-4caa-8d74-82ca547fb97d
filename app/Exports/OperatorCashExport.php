<?php

namespace App\Exports;

use App\Models\Finance\CashCategory;
use App\Models\Group;
use App\Models\GroupCash;
use App\Models\User;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Spatie\Activitylog\Models\Activity;

class OperatorCashExport implements FromQuery, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Kas Operator';

    public function __construct(private array $filters)
    {
        $fileName = [$this->fileName];
        $dateRange = $filters['cashed_at']['cashed_at'] ?? null;
        if ($dateRange) {
            $dateRange = str_replace('/', '-', $dateRange);
            $fileName[] = "[{$dateRange}]";
        }
        $userId = $filters['user_id']['value'] ?? null;
        if ($userId) {
            $fileName[] = User::find($userId)?->name ?? null;
        }
        $categoryId = $filters['category_id']['value'] ?? null;
        if ($categoryId) {
            $fileName[] = CashCategory::find($categoryId)?->name ?? null;
        }
        $groupId = $filters['group']['group_id'] ?? null;
        if ($groupId) {
            $fileName[] = Group::find($groupId)?->fullname ?? null;
        }

        $this->fileName = collect($fileName)->filter()->join(' - ') . '.xlsx';
    }

    public function query()
    {
        $dateRange = $this->filters['cashed_at']['cashed_at'] ?? null;
        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $dates = [
                    Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                    Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                ];
            }
        }
        $userId = $this->filters['user_id']['value'] ?? null;
        $categoryId = $this->filters['category_id']['value'] ?? null;
        $groupId = $this->filters['group']['group_id'] ?? null;

        return GroupCash::query()
            ->with(['group.customer', 'category'])
            ->where('division', 'operator')
            ->when(
                $dateRange,
                fn ($query) => $query->whereBetween('cashed_at', $dates)
            )
            ->when($userId, fn ($query) => $query->where('user_id', $userId))
            ->when($categoryId, fn ($query) => $query->where('category_id', $categoryId))
            ->when($groupId, fn ($query) => $query->where('group_id', $groupId))
            ->orderBy('cashed_at');
    }

    public function headings(): array
    {
        return [
            'Transaction ID',
            'Operator ID',
            'Operator Name',
            'Date',
            'Customer ID',
            'Customer Name',
            'Group ID',
            'Group Name',
            'Category ID',
            'Category Name',
            'Description',
            'Currency',
            'Exchange Rate',
            'In',
            'In (SAR)',
            'Out',
            'Out (SAR)',
            'Updated By',
            'Updated At',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'N:Q' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'S' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    public function map($cash): array
    {
        $activity = Activity::query()
            ->whereMorphedTo('subject', $cash)
            ->latest()
            ->first();

        return [
            $cash->id,
            $cash->user_id,
            $cash->user?->name ?? '',
            Date::dateTimeToExcel($cash->cashed_at),
            $cash->group?->customer_id ?? '',
            $cash->group?->customer?->name ?? '',
            $cash->group_id,
            $cash->group?->name ?? '',
            $cash->category_id,
            $cash->category?->name ?? '',
            $cash->description,
            $cash->currency,
            $cash->exchange_rate,
            $cash->cash_in,
            $cash->cash_in_c,
            $cash->cash_out,
            $cash->cash_out_c,
            $activity ? $activity->causer->name : '',
            Date::dateTimeToExcel($cash->updated_at),
        ];
    }
}
