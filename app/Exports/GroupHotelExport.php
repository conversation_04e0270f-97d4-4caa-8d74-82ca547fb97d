<?php

namespace App\Exports;

use App\Enums\HotelRoomType;
use App\Models\GroupHotel;
use App\Models\Hotel;
use App\Models\Vendors\HotelBroker;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class GroupHotelExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Hotels';

    public function __construct(protected Builder $query, private array $filters = [], private string $type = 'xlsx')
    {
        $fileName = [$this->fileName];
        $month = $filters['month']['month'] ?? null;
        if ($month) {
            $monthCarbon = $month
                ? Carbon::parse("{$month}-01")
                : today()->startOfMonth();

            $fileName[] = $monthCarbon->format('F Y');
        }

        $city = $filters['city']['value'] ?? null;
        if ($city) {
            $fileName[] = $city;
        }

        $hotelId = $filters['hotel_id']['value'] ?? null;
        if ($hotelId) {
            $fileName[] = Hotel::find($hotelId)?->fullname;
        }

        $brokerId = $filters['broker_id']['value'] ?? null;
        if ($brokerId) {
            $fileName[] = HotelBroker::find($brokerId)?->company_name;
        }

        $this->fileName = collect($fileName)
            ->filter()
            ->join(' - ')
            . ".{$type}";
    }

    public function query()
    {
        return $this->query->orderBy('check_in');
    }

    public function columnFormats(): array
    {
        return $this->type === 'csv' ? [
            'F' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'G' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ] : [
            'F' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'G' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    public function headings(): array
    {
        return [
            'Customer',
            'Group ID',
            'Group',
            'Hotel',
            'City',
            'Check-In',
            'Check-Out',
            'Broker',
            'S',
            'D',
            'T',
            'Q',
            'Qt',
            'Room Type',
            'Meal Plan',
        ];
    }

    /**
     * @param  GroupHotel  $row
     */
    public function map($row): array
    {
        return [
            $row->group->customer->name,
            $row->group_id,
            $row->group->name,
            $row->hotel->name,
            $row->hotel->city,
            Date::dateTimeToExcel($row->check_in),
            Date::dateTimeToExcel($row->check_out),
            $row->broker?->company_name,
            $row->room_single_count ?? '',
            $row->room_double_count ?? '',
            $row->room_triple_count ?? '',
            $row->room_quad_count ?? '',
            $row->room_quint_count ?? '',
            HotelRoomType::tryFrom($row->meta['room_type'] ?? '')?->getLabel() ?? '',
            $row->meta['meal'] ?? '',
        ];
    }
}
