<?php

namespace App\Exports;

use App\Models\Finance\UserCash;
use App\Models\Group;
use App\Models\User;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Spatie\Permission\Models\Role;

class UserCashExport implements FromQuery, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Kas Advance';

    public function __construct(private array $filters)
    {
        $fileName = [$this->fileName];
        $dateRange = $filters['cashed_at']['cashed_at'] ?? null;
        if ($dateRange) {
            $dateRange = str_replace('/', '-', $dateRange);
            $fileName[] = "[{$dateRange}]";
        }
        $userRole = $filters['role_id']['value'] ?? null;
        if ($userRole) {
            $fileName[] = Role::findById($userRole)?->name ?? null;
        }
        $userId = $filters['user_id']['value'] ?? null;
        if ($userId) {
            $fileName[] = User::find($userId)?->name ?? null;
        }
        $groupId = $filters['group']['group_id'] ?? null;
        if ($groupId) {
            $fileName[] = Group::find($groupId)?->fullname ?? null;
        }

        $this->fileName = collect($fileName)->filter()->join(' - ') . '.xlsx';
    }

    public function query()
    {
        $dateRange = $this->filters['cashed_at']['cashed_at'] ?? null;
        if ($dateRange) {
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $dates = [
                    Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                    Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                ];
            }
        }
        $userRole = $this->filters['role_id']['value'] ?? null;
        $userId = $this->filters['user_id']['value'] ?? null;
        $groupId = $this->filters['group']['group_id'] ?? null;

        return UserCash::query()
            ->with(['user', 'group', 'category', 'updated_by'])
            ->when(
                $dateRange,
                fn ($query) => $query->whereBetween('cashed_at', $dates)
            )
            ->when(
                $userRole,
                fn ($query) => $query->whereHas('user.roles', fn ($query) => $query->where('id', $userRole))
            )
            ->when($userId, fn ($query) => $query->where('user_id', $userId))
            ->when($groupId, fn ($query) => $query->where('group_id', $groupId))
            ->orderBy('cashed_at');
    }

    public function headings(): array
    {
        return [
            'Transaction ID',
            'User ID',
            'User Name',
            'Date',
            'Customer ID',
            'Customer Name',
            'Group ID',
            'Group Name',
            'Category ID',
            'Category Name',
            'Details',
            'Currency',
            'Exchange Rate',
            'In',
            'In (SAR)',
            'Out',
            'Out (SAR)',
            'Updated By',
            'Updated At',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'N:Q' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'S' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    /**
     * @param  UserCash  $cash
     */
    public function map($cash): array
    {
        return [
            $cash->id,
            $cash->user_id,
            $cash->user?->name ?? '',
            Date::dateTimeToExcel($cash->cashed_at),
            $cash->group?->customer_id ?? '',
            $cash->group?->customer?->name ?? '',
            $cash->group_id,
            $cash->group?->name ?? '',
            $cash->category_id,
            $cash->category?->name ?? '',
            $cash->details,
            $cash->currency,
            $cash->exchange_rate,
            $cash->type == 'd' ? $cash->amount : '',
            $cash->type == 'd' ? $cash->amount_c : '',
            $cash->type == 'c' ? $cash->amount : '',
            $cash->type == 'c' ? $cash->amount_c : '',
            $cash->updated_by?->name ?? '',
            Date::dateTimeToExcel($cash->updated_at),
        ];
    }
}
