<?php

namespace App\Exports;

use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CustomerDepositExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    public function __construct(protected Builder $query) {}

    public function query()
    {
        return $this->query;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Customer',
            'Details',
            'Invoice',
            'Debit (SAR)',
            'Credit (SAR)',
            'Original Amount',
            'Currency',
            'Exchange Rate',
        ];
    }

    public function map($row): array
    {
        return [
            Date::dateTimeToExcel($row->cashed_at),
            $row->customer->name,
            $row->details . ($row->related_details ? "\n" . $row->related_details : ''),
            $row->invoice ? $row->invoice->invoice_number : '',
            $row->type == 'd' ? $row->amount * $row->exchange_rate : null,
            $row->type == 'c' ? $row->amount * $row->exchange_rate : null,
            $row->currency != 'SAR' ? $row->amount : null,
            $row->currency != 'SAR' ? $row->currency : null,
            $row->currency != 'SAR' ? $row->exchange_rate : null,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }
}
