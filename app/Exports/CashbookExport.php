<?php

namespace App\Exports;

use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CashbookExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    private $filters;

    public function __construct($filters)
    {
        $this->filters = $filters;
    }

    public function query()
    {
        return JournalEntryItem::query()
            ->leftJoin('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->leftJoin('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
            ->where(fn ($query) => $query
                ->whereIn('cash_accounts.group', ['cash', 'bank'])
                ->where('cash_accounts.code', '!=', config('finance.coa.advance_cash')))
            ->when($this->filters['entry_date']['entry_date'] ?? null, function ($query, $range) {
                $dates = explode(' - ', $range);
                $query->whereBetween('entry_date', [
                    Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                    Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                ]);
            })
            ->select([
                'journal_entries.entry_date',
                'journal_entries.details',
                'journal_entry_items.*',
                'cash_accounts.name AS account_name',
                DB::raw("IF(type = 'd', amount, null) AS debit"),
                DB::raw("IF(type = 'c', amount, null) AS credit"),
            ])
            ->withCasts([
                'entry_date' => 'datetime',
            ])
            ->orderByRaw("FIELD(owner_type, 'balance')")
            ->orderBy('entry_date');
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ];
    }

    public function headings(): array
    {
        return [
            'Account',
            'Date',
            'Details',
            'Debit',
            'Credit',
        ];
    }

    public function map($row): array
    {
        return [
            $row->account_name,
            Date::dateTimeToExcel($row->entry_date),
            $row->details,
            $row->debit,
            $row->credit,
        ];
    }
}
