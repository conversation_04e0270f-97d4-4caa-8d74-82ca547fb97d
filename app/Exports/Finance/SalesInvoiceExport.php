<?php

namespace App\Exports\Finance;

use App\Enums\InvoiceStatus;
use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\Group;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class SalesInvoiceExport implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Invoices';

    protected string $type = 'xlsx';

    public function __construct(private array $filters = [], $type = 'xlsx')
    {
        $this->type = $type;
        $fileName = [$this->fileName];

        $dateRange = $filters['invoice_date']['date_from'] ?? null;
        $dateUntil = $filters['invoice_date']['date_until'] ?? null;
        if ($dateRange || $dateUntil) {
            $dateStr = [];
            if ($dateRange) {
                $dateStr[] = Carbon::parse($dateRange)->format('d-M-y');
            }
            if ($dateUntil) {
                $dateStr[] = Carbon::parse($dateUntil)->format('d-M-y');
            }
            $fileName[] = '[' . implode(' to ', $dateStr) . ']';
        }

        $customerId = $filters['customer_id']['value'] ?? null;
        if ($customerId) {
            $fileName[] = Customer::find($customerId)?->name ?? null;
        }

        $groupId = $filters['group']['group_id'] ?? null;
        if ($groupId) {
            $fileName[] = Group::find($groupId)?->name ?? null;
        }

        $status = $filters['status']['values'] ?? [];
        if (filled($status)) {
            $fileName[] = collect($status)
                ->map(fn ($s) => InvoiceStatus::tryFrom($s)?->name ?? $s)
                ->join(' + ');
        }

        $this->fileName = collect($fileName)->filter()->join(' - ') . ".{$type}";
    }

    public function collection()
    {
        $dateFrom = $this->filters['invoice_date']['date_from'] ?? null;
        $dateUntil = $this->filters['invoice_date']['date_until'] ?? null;
        $customerId = $this->filters['customer_id']['value'] ?? null;
        $groupId = $this->filters['group']['group_id'] ?? null;
        $status = $this->filters['status']['values'] ?? [];

        $invoices = Invoice::query()
            ->with(['customer', 'group', 'package', 'items.product'])
            ->when(
                $dateFrom,
                fn ($query) => $query->whereDate('invoice_date', '>=', $dateFrom)
            )
            ->when(
                $dateUntil,
                fn ($query) => $query->whereDate('invoice_date', '<=', $dateUntil)
            )
            ->when($customerId, fn ($query) => $query->where('customer_id', $customerId))
            ->when($groupId, fn ($query) => $query->where('group_id', $groupId))
            ->when(filled($status), fn ($query) => $query->whereIn('status', $status))
            ->orderBy('invoice_date', 'desc')
            ->get();

        // Create a collection that includes invoice items
        $rows = new Collection;

        foreach ($invoices as $invoice) {
            // Add rows for each item
            foreach ($invoice->items as $item) {
                $rows->push((object) [
                    'invoice' => $invoice,
                    'item' => $item,
                ]);
            }
        }

        return $rows;
    }

    public function columnFormats(): array
    {
        return $this->type === 'csv' ? [
            'C' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'P' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ] : [
            'C' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'D' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'J' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'K' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'L' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            // 'N' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'P' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'V' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'X' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'Y' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function headings(): array
    {
        return [
            'Invoice ID',
            'Invoice Number',
            'Invoice Date',
            'Due Date',
            'Customer ID',
            'Customer Name',
            'Group ID',
            'Group Name',
            'Package ID',
            'Total',
            'Paid',
            'Balance',
            'Currency Code',
            'Exchange Rate',
            'Status',
            'Created At',
            'Subject',
            'Notes',

            'Item ID',
            'Product ID',
            'Item Name',
            'Item Desc',
            'Quantity',
            'Item Price',
            'Subtotal',
        ];
    }

    public function map($row): array
    {
        $invoice = $row->invoice;
        $item = $row->item;
        $status = $invoice->status instanceof InvoiceStatus ? $invoice->status->value : $invoice->status;

        $data = [
            $invoice->id,
            $invoice->invoice_number,
            Date::dateTimeToExcel($invoice->invoice_date),
            Date::dateTimeToExcel($invoice->due_date),
            $invoice->customer_id,
            $invoice->customer?->name ?? '',
            $invoice->group_id,
            $invoice->group?->name ?? '',
            $invoice->package_id,
            $invoice->total,
            $invoice->paid,
            $invoice->total - $invoice->paid,
            $invoice->currency_code,
            $invoice->exchange_rate,
            $status,
            Date::dateTimeToExcel($invoice->created_at),
            $invoice->subject,
            $invoice->notes,

            $item->id,
            $item->product_id,
            $item->name,
            $item->description,
            $item->quantity,
            $item->unit_price,
            $item->quantity * $item->unit_price,
        ];

        return $data;
    }
}
