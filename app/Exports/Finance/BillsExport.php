<?php

namespace App\Exports\Finance;

use App\Enums\InvoiceStatus;
use App\Models\Bill;
use App\Models\Group;
use App\Models\Vendor;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BillsExport implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Bills';

    protected string $type = 'xlsx';

    public function __construct(private array $filters = [], $type = 'xlsx')
    {
        $this->type = $type;
        $fileName = [$this->fileName];
        $dateRange = $filters['bill_date']['date_from'] ?? null;
        $dateUntil = $filters['bill_date']['date_until'] ?? null;
        if ($dateRange || $dateUntil) {
            $dateStr = [];
            if ($dateRange) {
                $dateStr[] = Carbon::parse($dateRange)->format('d-M-y');
            }
            if ($dateUntil) {
                $dateStr[] = Carbon::parse($dateUntil)->format('d-M-y');
            }
            $fileName[] = '[' . implode(' to ', $dateStr) . ']';
        }

        $vendorId = $filters['vendor_id']['value'] ?? null;
        if ($vendorId) {
            $fileName[] = Vendor::find($vendorId)?->company_name ?? null;
        }

        $groupId = $filters['group']['group_id'] ?? null;
        if ($groupId) {
            $fileName[] = Group::find($groupId)?->name ?? null;
        }

        $status = $filters['status']['value'] ?? null;
        if ($status) {
            $fileName[] = InvoiceStatus::tryFrom($status)?->name ?? $status;
        }

        $this->fileName = collect($fileName)->filter()->join(' - ') . ".{$type}";
    }

    public function collection()
    {
        $dateFrom = $this->filters['bill_date']['date_from'] ?? null;
        $dateUntil = $this->filters['bill_date']['date_until'] ?? null;
        $vendorId = $this->filters['vendor_id']['value'] ?? null;
        $groupId = $this->filters['group']['group_id'] ?? null;
        $status = $this->filters['status']['value'] ?? null;

        $bills = Bill::query()
            ->with(['vendor', 'order', 'items.product', 'items.group'])
            ->when(
                $dateFrom,
                fn ($query) => $query->whereDate('bill_date', '>=', $dateFrom)
            )
            ->when(
                $dateUntil,
                fn ($query) => $query->whereDate('bill_date', '<=', $dateUntil)
            )
            ->when($vendorId, fn ($query) => $query->where('vendor_id', $vendorId))
            ->when($groupId, fn ($query) => $query->whereHas('items', fn ($q) => $q->where('group_id', $groupId)))
            ->when($status, fn ($query) => $query->where('status', $status))
            ->orderBy('bill_date', 'desc')
            ->get();

        // Create a collection that includes bill items
        $rows = new Collection;

        foreach ($bills as $bill) {
            // Add rows for each item
            foreach ($bill->items as $item) {
                $rows->push((object) [
                    'bill' => $bill,
                    'item' => $item,
                ]);
            }
        }

        return $rows;
    }

    public function columnFormats(): array
    {
        return $this->type === 'csv' ? [
            'C' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'D' => NumberFormat::FORMAT_DATE_YYYYMMDD,
            'O' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ] : [
            'C' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'D' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'I' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'J' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'K' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'O' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'X' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'Y' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'Z' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'AA' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function headings(): array
    {
        return [
            'Bill ID',
            'Bill Number',
            'Bill Date',
            'Due Date',
            'Vendor ID',
            'Vendor Name',
            'Order ID',
            'Order Number',
            'Total',
            'Paid',
            'Balance',
            'Currency Code',
            'Exchange Rate',
            'Status',
            'Created At',
            'Subject',
            'Notes',

            'Item ID',
            'Group ID',
            'Group Name',
            'Product ID',
            'Item Name',
            'Item Desc',
            'Quantity',
            'Unit Price',
            'VAT %',
            'Subtotal',
        ];
    }

    public function map($row): array
    {
        $bill = $row->bill;
        $item = $row->item;

        $subtotal = $item->quantity * $item->unit_price * (1 + $item->vat / 100);

        $data = [
            $bill->id,
            $bill->bill_number,
            Date::dateTimeToExcel($bill->bill_date),
            Date::dateTimeToExcel($bill->due_date),
            $bill->vendor_id,
            $bill->vendor?->company_name ?? '',
            $bill->order_id,
            $bill->order?->order_number ?? '',
            $bill->total,
            $bill->paid,
            $bill->total - $bill->paid,
            $bill->currency_code,
            $bill->exchange_rate,
            $bill->status->value,
            Date::dateTimeToExcel($bill->created_at),
            $bill->subject,
            $bill->notes,

            $item->id,
            $item->group_id,
            $item->group?->name ?? '',
            $item->product_id,
            $item->name,
            $item->description,
            $item->quantity,
            $item->unit_price,
            $item->vat,
            $subtotal,
        ];

        return $data;
    }
}
