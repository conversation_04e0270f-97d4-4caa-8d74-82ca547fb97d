<?php

namespace App\Exports\Finance;

use App\Models\Finance\CashAccount;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class SalesInvoicePaymentExport implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Invoice Payments';

    protected string $type = 'xlsx';

    public function __construct(private array $filters = [], $type = 'xlsx')
    {
        $this->type = $type;
        $fileName = [$this->fileName];

        $invoiceId = $filters['invoice_id']['value'] ?? null;
        if ($invoiceId) {
            $fileName[] = Invoice::find($invoiceId)?->invoice_number ?? null;
        }

        $dateRange = $filters['paid_at']['date_from'] ?? null;
        $dateUntil = $filters['paid_at']['date_until'] ?? null;
        if ($dateRange || $dateUntil) {
            $dateStr = [];
            if ($dateRange) {
                $dateStr[] = Carbon::parse($dateRange)->format('d-M-y');
            }
            if ($dateUntil) {
                $dateStr[] = Carbon::parse($dateUntil)->format('d-M-y');
            }
            $fileName[] = '[' . implode(' to ', $dateStr) . ']';
        }

        $cashAccountId = $filters['cash_account_id']['value'] ?? null;
        if ($cashAccountId) {
            $fileName[] = CashAccount::find($cashAccountId)?->name ?? null;
        }

        $this->fileName = collect($fileName)->filter()->join(' - ') . ".{$type}";
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $invoiceId = $this->filters['invoice_id']['value'] ?? null;
        $dateFrom = $this->filters['paid_at']['date_from'] ?? null;
        $dateUntil = $this->filters['paid_at']['date_until'] ?? null;
        $cashAccountId = $this->filters['cash_account_id']['value'] ?? null;

        return InvoicePayment::query()
            ->with(['invoice', 'cash_account'])
            ->when($invoiceId, fn ($query) => $query->where('invoice_id', $invoiceId))
            ->when(
                $dateFrom,
                fn ($query) => $query->whereDate('paid_at', '>=', $dateFrom)
            )
            ->when(
                $dateUntil,
                fn ($query) => $query->whereDate('paid_at', '<=', $dateUntil)
            )
            ->when($cashAccountId, fn ($query) => $query->where('cash_account_id', $cashAccountId))
            ->orderBy('paid_at', 'desc')
            ->get();
    }

    public function headings(): array
    {
        return [
            'Payment Number',
            'Invoice Number',
            'Date',
            'Payment Method',
            'Deposit To',
            'Deposit To Account Code',
            'Amount',
            'Amount (SAR)',
            'Currency Code',
            'Exchange Rate',
            'Description',
        ];
    }

    public function columnFormats(): array
    {
        return $this->type === 'csv' ? [
            'C' => NumberFormat::FORMAT_DATE_YYYYMMDD,
        ] : [
            'C' => NumberFormat::FORMAT_DATE_DDMMYYYY,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'H' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
            'J' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1,
        ];
    }

    public function map($row): array
    {
        return [
            '#' . str_pad($row->id, 6, '0', STR_PAD_LEFT),
            $row->invoice?->invoice_number ?? '',
            Date::dateTimeToExcel($row->paid_at),
            $row->payment_method->getLabel(),
            $row->cash_account?->name ?? '',
            $row->cash_account?->code ?? '',
            $row->amount,
            $row->amount * $row->exchange_rate,
            $row->currency_code,
            $row->exchange_rate,
            $row->description,
        ];
    }
}
