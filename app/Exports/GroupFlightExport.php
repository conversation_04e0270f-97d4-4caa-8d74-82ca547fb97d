<?php

namespace App\Exports;

use App\Models\GroupFlight;
use App\Models\Vendors\AirportHandler;
use App\Settings\AirportHandlingSettings;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class GroupFlightExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping
{
    use Exportable;

    protected string $fileName = 'Flights';

    public function __construct(protected Builder $query, private array $filters = [], private string $type = 'xlsx')
    {
        $fileName = [$this->fileName];
        $month = $filters['month']['month'] ?? null;
        $monthCarbon = $month
            ? Carbon::parse("{$month}-01")
            : today()->startOfMonth();

        $fileName[] = $monthCarbon->format('F Y');

        $flightType = $filters['type']['value'] ?? null;
        if ($flightType) {
            $fileName[] = str($flightType)->headline();
        }

        $from = $filters['from']['values'] ?? [];
        if (filled($from)) {
            $fileName[] = collect($from)->join(' + ');
        }

        $to = $filters['to']['values'] ?? [];
        if (filled($to)) {
            $fileName[] = collect($to)->join(' + ');
        }

        $handlerId = $filters['handler']['value'] ?? null;
        if ($handlerId) {
            $fileName[] = AirportHandler::find($handlerId)?->company_name;
        }

        $this->fileName = collect($fileName)
            ->filter()
            ->join(' - ')
            . ".{$type}";
    }

    public function query()
    {
        return $this->query->orderBy('date_etd');
    }

    public function columnFormats(): array
    {
        return $this->type === 'csv' ? [
            'I' => NumberFormat::FORMAT_DATE_YYYYMMDD . ' hh:mm',
            'J' => NumberFormat::FORMAT_DATE_YYYYMMDD . ' hh:mm',
        ] : [
            'I' => NumberFormat::FORMAT_DATE_DDMMYYYY . ' hh:mm',
            'J' => NumberFormat::FORMAT_DATE_DDMMYYYY . ' hh:mm',
        ];
    }

    public function headings(): array
    {
        return [
            'Type',
            'Customer',
            'Group ID',
            'Group',
            'Flight No.',
            'From',
            'Via',
            'To',
            'ETD',
            'ETA',
            'Pax',
            'Handler',
            'Mealbox',
            'Meal Price',
            'Handling Rate / Pax',
        ];
    }

    /**
     * @param  GroupFlight  $row
     */
    public function map($row): array
    {
        $settings = app(AirportHandlingSettings::class);

        $mealPrice = collect($row->mealbox)
            ->map(fn ($meal) => (float) $settings->meal_prices[$meal] ?? 0)
            ->sum();
        $handlingRate = collect($settings->handling_rates)
            ->firstWhere('airport_code', $row->type == 'arrival' ? $row->to : $row->from) ?? [];

        return [
            str($row->type)->headline(),
            $row->group->customer->name,
            $row->group_id,
            $row->group->name,
            collect([$row->flight_number, $row->via_number])->filter()->join(', '),
            $row->from,
            $row->via,
            $row->to,
            Date::dateTimeToExcel($row->date_etd),
            Date::dateTimeToExcel($row->date_eta),
            $row->pax ?? $row->group->total_pax,
            $row->handler?->company_name,
            collect($row->mealbox)->join(', '),
            $mealPrice,
            $handlingRate[$row->type] ?? 0,
        ];
    }
}
