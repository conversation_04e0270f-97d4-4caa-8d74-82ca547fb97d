<?php

namespace App\Listeners;

use Lab404\Impersonate\Services\ImpersonateManager;

class LoginListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        if (! app(ImpersonateManager::class)->isImpersonating()) {
            $event->user->update([
                'last_login_at' => now(),
                'last_login_ip' => request()->getClientIp(),
            ]);
        }
    }
}
