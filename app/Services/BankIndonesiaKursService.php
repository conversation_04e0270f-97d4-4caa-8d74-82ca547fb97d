<?php

namespace App\Services;

use App\Contracts\CurrencyHandler;
use DOMDocument;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BankIndonesiaKursService implements CurrencyHandler
{
    private const TARGET_CURRENCY = 'IDR';

    private const BI_KURS_URL = 'https://www.bi.go.id/id/statistik/informasi-kurs/transaksi-bi/default.aspx';

    public function isEnabled(): bool
    {
        return true;
    }

    public function getSupportedCurrencies(): ?array
    {
        return Cache::remember('BI_supported_currency_codes', now()->addMonth(), function () {
            $rates = $this->findLatestKurs();

            return $rates ? [...array_keys($rates)] : null;
        });
    }

    public function getExchangeRates(string $baseCurrency, array $targetCurrencies): ?array
    {
        $cacheKey = "BI_currency_rates_{$baseCurrency}";
        $cachedRates = Cache::get($cacheKey);

        if (Cache::missing($cachedRates)) {
            $cachedRates = $this->updateCurrencyRatesCache($baseCurrency);

            if (empty($cachedRates)) {
                return null;
            }
        }

        $filteredRates = array_intersect_key($cachedRates, array_flip($targetCurrencies));
        $filteredRates = array_filter($filteredRates);

        $filteredCurrencies = array_keys($filteredRates);
        $missingCurrencies = array_diff($targetCurrencies, $filteredCurrencies);

        return filled($missingCurrencies) ? null : $filteredRates;
    }

    public function getCachedExchangeRates(string $baseCurrency, array $targetCurrencies): ?array
    {
        return $this->getExchangeRates($baseCurrency, $targetCurrencies);
    }

    public function getCachedExchangeRate(string $baseCurrency, string $targetCurrency): ?float
    {
        $rates = $this->getCachedExchangeRates($baseCurrency, [$targetCurrency]);

        return isset($rates[$targetCurrency]) ? (float) $rates[$targetCurrency] : null;
    }

    public function updateCurrencyRatesCache(string $baseCurrency): ?array
    {
        $rates = $this->findLatestKurs();

        if ($rates) {
            if ($baseCurrency === self::TARGET_CURRENCY) {
                $result = [];
                foreach ($rates as $currency => $rate) {
                    $result[$currency] = 1 / $rate;
                }
                $rates = $result;
            } else {
                if (! isset($rates[$baseCurrency])) {
                    return null;
                }

                $baseRate = $rates[$baseCurrency];
                $result = [];
                foreach ($rates as $currency => $rate) {
                    if ($currency === self::TARGET_CURRENCY) {
                        $result[$currency] = $baseRate;
                    } else {
                        $result[$currency] = $baseRate / $rate;
                    }
                }
                $rates = $result;
            }

            Cache::put("BI_currency_rates_{$baseCurrency}", $rates, now()->addDay());

            return $rates;
        }

        return null;
    }

    private function findLatestKurs(): ?array
    {
        try {
            $response = Http::withUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
                ->get(self::BI_KURS_URL);
            if (! $response->successful()) {
                Log::error('Failed to connect to Bank Indonesia API');

                return null;
            }

            $html = $response->body();
            $doc = new DOMDocument;
            @$doc->loadHTML($html);
            $xpath = new \DOMXPath($doc);

            $result = [];
            $rows = $xpath->query("//table[@class='table table-striped table-no-bordered table-lg']/tbody/tr");

            foreach ($rows as $row) {
                $cells = $xpath->query('.//td', $row);
                if ($cells->length >= 4) {
                    $currency = trim($cells->item(0)->textContent);
                    $value = $this->parseNumber($cells->item(1)->textContent);
                    $sell = $this->parseNumber($cells->item(2)->textContent);
                    $buy = $this->parseNumber($cells->item(3)->textContent);

                    if ($value && $sell && $buy) {
                        $result[$currency] = ($sell + $buy) / (2 * $value);
                    }
                }
            }

            return empty($result) ? null : [...$result, self::TARGET_CURRENCY => 1.0];
        } catch (Exception $e) {
            Log::error('Error parsing Bank Indonesia rates: ' . $e->getMessage());

            return null;
        }
    }

    private function parseNumber(string $text): ?float
    {
        $text = str_replace('.', '', trim($text));
        $text = str_replace(',', '.', $text);

        return is_numeric($text) ? (float) $text : null;
    }
}
