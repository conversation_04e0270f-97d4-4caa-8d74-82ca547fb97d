<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AiChatService
{
    private string $apiKey;

    private string $apiUrl;

    private array $allowedTables;

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
        $this->apiUrl = config('services.openai.api_url', 'https://api.openai.com/v1/chat/completions');
        $this->allowedTables = config('ai-chat.allowed_tables', []);
    }

    public function processQuery(string $query): array
    {
        try {
            // Extract database query intent from user question
            $dbQueryIntent = $this->extractDatabaseQueryIntent($query);

            // Execute the database query (read-only)
            $dbResults = $this->executeReadOnlyQuery($dbQueryIntent);

            // Generate response using AI with database results as context
            $response = $this->generateAiResponse($query, $dbResults);

            return [
                'success' => true,
                'response' => $response,
                'data' => $dbResults,
            ];
        } catch (\Throwable $th) {
            Log::error('AI Chat error: ' . $th->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to process your query: ' . $th->getMessage(),
            ];
        }
    }

    private function extractDatabaseQueryIntent(string $query): array
    {
        $schemaInfo = $this->getDatabaseSchemaInfo();
        $today = date('Y-m-d');

        // Use AI to convert natural language to database query intent
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post($this->apiUrl, [
            'model' => 'gpt-4.1',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => "You are a database query analyzer for an Umrah service system. Today's date is {$today}. Convert the user question into a structured READ-ONLY query intent.

    Database Schema Information:
    {$schemaInfo}

    Response format (JSON only):
    {
      \"table\": \"main_table_name\",
      \"fields\": [
        \"field1\",
        \"table.field\",
        {\"raw\": \"COUNT(*) as total_count\"},
        {\"raw\": \"SUM(amount) as total_amount\"}
      ],
      \"joins\": [
        {\"table\": \"joined_table\", \"type\": \"inner|left|right\", \"on\": \"main_table.id = joined_table.main_id\"}
      ],
      \"conditions\": [
        {\"field\": \"field_name\", \"operator\": \"=\", \"value\": \"value\"}
      ],
      \"group_by\": [\"field1\", \"table.field\"],
      \"having\": [
        {\"field\": \"COUNT(*)\", \"operator\": \">\", \"value\": \"5\"}
      ],
      \"order_by\": [
        {\"field\": \"field1\", \"direction\": \"asc\"},
        {\"field\": \"COUNT(*)\", \"direction\": \"desc\"}
      ],
      \"limit\": 10
    }

    Rules:
    - Only use tables and columns from the schema above
    - Use joins when data from multiple tables is needed (refer to foreign key relationships)
    - Never include operations that modify data (INSERT, UPDATE, DELETE, etc.)
    - Use proper table.field notation for ambiguous fields
    - For calculations, aggregations, or functions, use raw field format: {\"raw\": \"SQL_EXPRESSION as alias\"}
    - Raw fields should only contain safe SQL functions (COUNT, SUM, AVG, MAX, MIN, CONCAT, etc.)
    - When using aggregate functions (COUNT, SUM, AVG, MAX, MIN) with regular columns, ALWAYS include group_by with the non-aggregate columns
    - Use having for filtering on aggregate results
    - Use order_by for sorting results
    - Pay attention to column data types when creating conditions (e.g., use proper date formats for date columns)
    - Use foreign key relationships to determine proper join conditions
    - Respond ONLY with the JSON object",
                ],
                [
                    'role' => 'user',
                    'content' => $query,
                ],
            ],
            'temperature' => 0.3,
        ]);

        $content = json_decode($response->body(), true)['choices'][0]['message']['content'];

        return json_decode($content, true) ?: [];
    }

    public function executeReadOnlyQuery(array $queryIntent): array
    {
        // Safety checks and validation
        $table = $queryIntent['table'] ?? null;
        $fields = $queryIntent['fields'] ?? ['*'];
        $joins = $queryIntent['joins'] ?? [];
        $conditions = $queryIntent['conditions'] ?? [];
        $groupBy = $queryIntent['group_by'] ?? [];
        $having = $queryIntent['having'] ?? [];
        $orderBy = $queryIntent['order_by'] ?? [];
        $limit = min($queryIntent['limit'] ?? 10, 100); // Cap limit at 100

        // Ensure main table is allowed
        if (! $table || ! in_array($table, $this->allowedTables)) {
            throw new \Exception("Table '{$table}' is not allowed for querying");
        }

        // Validate all joined tables are allowed
        foreach ($joins as $join) {
            $joinTable = $join['table'] ?? null;
            if (! $joinTable || ! in_array($joinTable, $this->allowedTables)) {
                throw new \Exception("Join table '{$joinTable}' is not allowed for querying");
            }
        }

        // Create a read-only transaction
        return DB::transaction(function () use ($table, $fields, $joins, $conditions, $groupBy, $having, $orderBy, $limit) {
            // Set session to read-only
            // DB::statement('SET TRANSACTION READ ONLY');

            // Process fields to handle raw expressions
            $processedFields = $this->processFields($fields);
            $query = DB::table($table)->select($processedFields);

            // Add joins
            foreach ($joins as $join) {
                $joinTable = $join['table'];
                $joinType = strtolower($join['type'] ?? 'inner');
                $joinCondition = $join['on'] ?? '';

                // Validate join type
                $allowedJoinTypes = ['inner', 'left', 'right'];
                if (! in_array($joinType, $allowedJoinTypes)) {
                    throw new \Exception("Join type '{$joinType}' is not allowed");
                }

                // Parse and validate join condition
                $joinParts = $this->parseJoinCondition($joinCondition);
                if (! $joinParts) {
                    throw new \Exception("Invalid join condition: '{$joinCondition}'");
                }

                // Apply the join with proper column references
                switch ($joinType) {
                    case 'left':
                        $query->leftJoin($joinTable, $joinParts['left'], $joinParts['operator'], $joinParts['right']);
                        break;
                    case 'right':
                        $query->rightJoin($joinTable, $joinParts['left'], $joinParts['operator'], $joinParts['right']);
                        break;
                    default: // inner
                        $query->join($joinTable, $joinParts['left'], $joinParts['operator'], $joinParts['right']);
                        break;
                }
            }

            // Add conditions
            foreach ($conditions as $condition) {
                if (isset($condition['field']) && isset($condition['operator']) && isset($condition['value'])) {
                    // Sanitize operator to prevent SQL injection
                    $allowedOperators = ['=', '!=', '>', '<', '>=', '<=', 'like'];
                    $operator = in_array(strtolower($condition['operator']), $allowedOperators)
                        ? $condition['operator']
                        : '=';

                    $query->where($condition['field'], $operator, $condition['value']);
                }
            }

            // Add GROUP BY
            if (! empty($groupBy)) {
                $query->groupBy($this->processFields($groupBy));
            }

            // Add HAVING conditions
            foreach ($having as $havingCondition) {
                if (isset($havingCondition['field']) && isset($havingCondition['operator']) && isset($havingCondition['value'])) {
                    // Sanitize operator to prevent SQL injection
                    $allowedOperators = ['=', '!=', '>', '<', '>=', '<='];
                    $operator = in_array(strtolower($havingCondition['operator']), $allowedOperators)
                        ? $havingCondition['operator']
                        : '=';

                    $query->havingRaw("{$havingCondition['field']} {$operator} ?", [$havingCondition['value']]);
                }
            }

            // Add ORDER BY
            foreach ($orderBy as $order) {
                if (isset($order['field'])) {
                    $direction = strtolower($order['direction'] ?? 'asc');
                    $direction = in_array($direction, ['asc', 'desc']) ? $direction : 'asc';

                    // Check if it's a raw expression (aggregate function)
                    if (str_contains($order['field'], '(') && str_contains($order['field'], ')')) {
                        $query->orderByRaw("{$order['field']} {$direction}");
                    } else {
                        $query->orderBy($order['field'], $direction);
                    }
                }
            }

            return $query->limit($limit)->get()->toArray();
        }, 5); // Retry 5 times
    }

    private function generateAiResponse(string $originalQuery, array $dbResults): string
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post($this->apiUrl, [
            'model' => 'gpt-4.1',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a helpful assistant with access to database information. Answer the user\'s question based on the database results provided. You can only access data, not modify it.',
                ],
                [
                    'role' => 'user',
                    'content' => "Question: {$originalQuery}\n\nDatabase results: " . json_encode($dbResults),
                ],
            ],
            'temperature' => 0.7,
        ]);

        return json_decode($response->body(), true)['choices'][0]['message']['content'];
    }

    /**
     * Process fields array to handle raw expressions
     */
    private function processFields(array $fields): array
    {
        $processedFields = [];

        foreach ($fields as $field) {
            if (is_array($field) && isset($field['raw'])) {
                // Handle raw field
                $rawExpression = $field['raw'];

                // Validate raw expression for safety
                if ($this->isValidRawExpression($rawExpression)) {
                    $processedFields[] = DB::raw($rawExpression);
                } else {
                    throw new \Exception("Invalid raw expression: '{$rawExpression}'");
                }
            } else {
                // Handle regular field
                $processedFields[] = $field;
            }
        }

        return $processedFields;
    }

    /**
     * Validate raw SQL expressions for safety
     */
    public function isValidRawExpression(string $expression): bool
    {
        // Convert to lowercase for checking
        $lowerExpression = strtolower(trim($expression));

        // Block dangerous keywords
        $dangerousKeywords = [
            'drop', 'delete', 'insert', 'update', 'alter', 'create', 'truncate',
            'grant', 'revoke', 'exec', 'execute', 'sp_', 'xp_', 'into outfile',
            'load_file', 'benchmark', 'sleep', 'waitfor', 'delay',
        ];

        foreach ($dangerousKeywords as $keyword) {
            // Use word boundaries to match whole words only
            if (preg_match('/\b' . preg_quote($keyword, '/') . '\b/i', $lowerExpression)) {
                return false;
            }
        }

        // Allow only safe SQL functions and operators
        $allowedPatterns = [
            // SQL functions with optional alias
            '/^(count|sum|avg|max|min|concat|coalesce|case|when|then|else|end|if|ifnull|nullif|length|upper|lower|trim|ltrim|rtrim|substring|left|right|replace|round|ceil|floor|abs|mod|greatest|least|date|year|month|day|hour|minute|second|now|curdate|curtime|timestampdiff|date_format|str_to_date)\s*\([^)]*\)(\s+as\s+[a-zA-Z_][a-zA-Z0-9_]*)?$/i',
            // table.column as alias
            '/^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*(\s+as\s+[a-zA-Z_][a-zA-Z0-9_]*)?$/i',
            // column as alias
            '/^[a-zA-Z_][a-zA-Z0-9_]*(\s+as\s+[a-zA-Z_][a-zA-Z0-9_]*)?$/i',
            // SELECT *
            '/^\*$/',
        ];

        // Check if expression matches any allowed pattern
        foreach ($allowedPatterns as $pattern) {
            if (preg_match($pattern, $expression)) {
                return true;
            }
        }

        // Check for arithmetic operations and basic expressions
        if (preg_match('/^[\w\s\.\+\-\*\/\(\)\,\'\"]+(\s+as\s+[a-zA-Z_][a-zA-Z0-9_]*)?$/i', $expression)) {
            return true;
        }

        return false;
    }

    /**
     * Parse join condition string into components
     * Expected format: "table1.column1 = table2.column2"
     */
    public function parseJoinCondition(string $condition): ?array
    {
        // Remove extra spaces and validate basic format
        $condition = trim($condition);

        // Support common operators
        $operators = ['=', '!=', '<>', '>', '<', '>=', '<='];
        $operator = null;

        foreach ($operators as $op) {
            if (str_contains($condition, " {$op} ")) {
                $operator = $op;
                break;
            }
        }

        if (! $operator) {
            return null;
        }

        $parts = explode(" {$operator} ", $condition);
        if (count($parts) !== 2) {
            return null;
        }

        $left = trim($parts[0]);
        $right = trim($parts[1]);

        // Basic validation: should contain table.column format
        if (! str_contains($left, '.') || ! str_contains($right, '.')) {
            return null;
        }

        // Additional validation: check if table names are in allowed tables
        $leftTable = explode('.', $left)[0];
        $rightTable = explode('.', $right)[0];

        if (! in_array($leftTable, $this->allowedTables) || ! in_array($rightTable, $this->allowedTables)) {
            return null;
        }

        return [
            'left' => $left,
            'operator' => $operator,
            'right' => $right,
        ];
    }

    /**
     * Get comprehensive database schema information for allowed tables
     */
    private function getDatabaseSchemaInfo(): string
    {
        // Cache schema info for 1 hour since it doesn't change frequently
        return Cache::remember('ai_chat_schema_info', 3600, function () {
            return $this->buildSchemaInfo();
        });
    }

    /**
     * Build the database schema information
     */
    private function buildSchemaInfo(): string
    {
        $schemaInfo = [];

        foreach ($this->allowedTables as $tableName) {
            try {
                // Get table structure
                $columns = DB::select("DESCRIBE {$tableName}");

                $tableInfo = [
                    'table' => $tableName,
                    'columns' => [],
                ];

                foreach ($columns as $column) {
                    $tableInfo['columns'][] = [
                        'name' => $column->Field,
                        'type' => $column->Type,
                        'nullable' => $column->Null === 'YES',
                        'key' => $column->Key,
                        'default' => $column->Default,
                        'extra' => $column->Extra,
                    ];
                }

                // Get foreign key relationships
                $foreignKeys = DB::select('
                    SELECT
                        COLUMN_NAME,
                        REFERENCED_TABLE_NAME,
                        REFERENCED_COLUMN_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = ?
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ', [$tableName]);

                if (! empty($foreignKeys)) {
                    $tableInfo['foreign_keys'] = [];
                    foreach ($foreignKeys as $fk) {
                        $tableInfo['foreign_keys'][] = [
                            'column' => $fk->COLUMN_NAME,
                            'references_table' => $fk->REFERENCED_TABLE_NAME,
                            'references_column' => $fk->REFERENCED_COLUMN_NAME,
                        ];
                    }
                }

                $schemaInfo[] = $tableInfo;

            } catch (\Exception $e) {
                // Skip tables that can't be described (might not exist)
                Log::warning("Could not get schema info for table {$tableName}: " . $e->getMessage());

                continue;
            }
        }

        // Format schema information for AI consumption
        $formattedSchema = "Available Tables and Schema:\n\n";

        foreach ($schemaInfo as $table) {
            $formattedSchema .= "Table: {$table['table']}\n";
            $formattedSchema .= "Columns:\n";

            foreach ($table['columns'] as $column) {
                $nullable = $column['nullable'] ? 'NULL' : 'NOT NULL';
                $key = $column['key'] ? " ({$column['key']})" : '';
                $formattedSchema .= "  - {$column['name']}: {$column['type']} {$nullable}{$key}\n";
            }

            if (isset($table['foreign_keys']) && ! empty($table['foreign_keys'])) {
                $formattedSchema .= "Foreign Keys:\n";
                foreach ($table['foreign_keys'] as $fk) {
                    $formattedSchema .= "  - {$fk['column']} -> {$fk['references_table']}.{$fk['references_column']}\n";
                }
            }

            $formattedSchema .= "\n";
        }

        // Add some helpful context about the Umrah service domain
        $formattedSchema .= "Domain Context:\n";
        $formattedSchema .= "This is an Umrah service management system with the following key concepts:\n";
        $formattedSchema .= "- Customers: People who book Umrah services\n";
        $formattedSchema .= "- Groups: Collections of pilgrims traveling together. If asked about them, make sure to include the customer names\n";
        $formattedSchema .= "- Pilgrims: Individual travelers in a group\n";
        $formattedSchema .= "- Hotels: Accommodation for pilgrims\n";
        $formattedSchema .= "- Flights: Air transportation\n";
        $formattedSchema .= "- Itineraries: Daily schedules and activities\n";
        $formattedSchema .= "- Invoices/Bills: Financial transactions (sales & purchases)\n";
        $formattedSchema .= "- Rooms: Hotel room assignments for pilgrims\n";
        $formattedSchema .= "- Vendors: Service providers (hotels, transport, etc.)\n\n";
        // Add instruction for exchange_rate usage
        $formattedSchema .= "Special Instruction:\n";
        $formattedSchema .= "- The base currency is SAR.\n";
        $formattedSchema .= "- For all tables that have an 'exchange_rate' field, use it for calculating the final amount (e.g., final_amount = amount * exchange_rate).\n\n";

        return $formattedSchema;
    }

    /**
     * Clear the cached schema information
     */
    public function clearSchemaCache(): void
    {
        Cache::forget('ai_chat_schema_info');
    }
}
