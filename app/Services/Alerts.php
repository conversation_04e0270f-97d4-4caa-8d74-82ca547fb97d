<?php

namespace App\Services;

use App\Filament\Pages\TodaysSchedule;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Umrahservice\Groups\Enums\GroupStatus;

class Alerts
{
    public static function render()
    {
        $alerts = [];

        if (auth()->user()->hasRole(['Admin', 'Operator'])) {
            $start_date = Carbon::now()->startOfDay();
            $end_date = Carbon::now()->addDay()->endOfDay();

            $bookings = DB::table('group_hotel')
                ->join('groups', 'groups.id', '=', 'group_hotel.group_id')
                ->where('groups.status', GroupStatus::Confirmed)
                ->whereBetween('check_in', [$start_date, $end_date])
                ->where('is_confirmed', false)
                ->count();

            if ($bookings) {
                $alerts[] = [
                    'text' => "There are {$bookings} hotels that need confirmation for tomorrow.",
                    'link' => TodaysSchedule::getUrl(),
                ];
            }
        }

        return view('partials.alerts', compact('alerts'));
    }
}
