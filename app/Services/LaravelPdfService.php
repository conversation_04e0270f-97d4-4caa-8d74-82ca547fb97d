<?php

namespace App\Services;

use App\Contracts\PdfService;
use Illuminate\Support\Facades\File;

use function Motekar\LaravelPdf\Support\pdf;

class LaravelPdfService implements PdfService
{
    public string $viewName = '';

    public array $viewData = [];

    public string $fileName = 'document';

    public function view(string $view, array $data = []): PdfService
    {
        $this->viewName = $view;
        $this->viewData = $data;

        return $this;
    }

    public function name(string $fileName): PdfService
    {
        if (! str_ends_with(strtolower($fileName), '.pdf')) {
            $fileName .= '.pdf';
        }
        $this->fileName = $fileName;

        return $this;
    }

    public function save(string $dirPath): string
    {
        $filePath = "$dirPath/{$this->fileName}";

        File::ensureDirectoryExists($dirPath);

        pdf($this->viewName, $this->viewData)
            ->save($filePath);

        return $filePath;
    }
}
