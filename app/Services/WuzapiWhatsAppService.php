<?php

namespace App\Services;

use App\Contracts\WhatsAppService;
use Illuminate\Support\Facades\Http;

class WuzapiWhatsAppService implements WhatsAppService
{
    public function __construct(private readonly string $apiUrl, private readonly string $userToken) {}

    public function sendText(string $to, string $message, ?string $footer = null, ?array $buttons = null): bool
    {
        $url = filled($footer) || filled($buttons) ? '/chat/send/template' : '/chat/send/text';
        $payload = [
            'Phone' => $to,
            'Body' => $message,
        ];
        if (filled($footer)) {
            $payload['Footer'] = $footer;
        }
        if (filled($buttons)) {
            $payload['Buttons'] = $buttons;
        }

        return $this->sendRequest($url, $payload);
    }

    public function sendImage(string $to, string $imageData, ?string $caption = null): bool
    {
        $payload = [
            'Phone' => $to,
            'Image' => $imageData,
            'Caption' => $caption,
        ];

        return $this->sendRequest('/chat/send/image', $payload);
    }

    public function sendDocument(string $to, string $documentData, string $fileName): bool
    {
        $payload = [
            'Phone' => $to,
            'Document' => $documentData,
            'FileName' => $fileName,
        ];

        return $this->sendRequest('/chat/send/document', $payload);
    }

    private function sendRequest(string $url, array $payload): bool
    {
        try {
            $res = Http::withHeader('token', $this->userToken)
                ->post($this->apiUrl.$url, $payload);

            return $res->json('success', false);
        } catch (\Throwable $th) {
            return false;
        }
    }
}
