<?php

namespace App\Services;

use App\Contracts\PdfService;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Illuminate\Support\Facades\File;

class GotenbergPdfService implements PdfService
{
    public string $viewName = '';

    public array $viewData = [];

    public string $fileName = 'document';

    public function view(string $view, array $data = []): PdfService
    {
        $this->viewName = $view;
        $this->viewData = $data;

        return $this;
    }

    public function name(string $fileName): PdfService
    {
        $this->fileName = $fileName;

        return $this;
    }

    public function save(string $dirPath): string
    {
        $view = view($this->viewName, $this->viewData);

        File::ensureDirectoryExists($dirPath);

        $request = Gotenberg::chromium(config('services.pdf.gotenberg.url'))
            ->pdf()
            ->preferCssPageSize()
            ->outputFilename($this->fileName)
            ->html(Stream::string('index.html', $view->render()));
        $fileName = Gotenberg::save($request, $dirPath);

        return "$dirPath/$fileName";
    }
}
