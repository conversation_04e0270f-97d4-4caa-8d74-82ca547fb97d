<?php

namespace App\Services;

use App\Contracts\CurrencyHandler;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BankIndonesiaKursServiceV2 implements CurrencyHandler
{
    private const TARGET_CURRENCY = 'IDR';

    private const BI_KURS_URL = 'https://www.bi.go.id/biwebservice/wskursbi.asmx/getSubKursLokal4';

    private function getLastWeekday(): Carbon
    {
        $date = Carbon::now('Asia/Jakarta');

        if ($date->dayOfWeek === Carbon::SUNDAY) {
            return $date->subDays(2);
        }

        if ($date->dayOfWeek === Carbon::SATURDAY) {
            return $date->subDay();
        }

        if ($date->hour < 8) {
            return $date->subDay()->isWeekday() ? $date->subDay() : $date->subDays(3);
        }

        return $date;
    }

    public function isEnabled(): bool
    {
        return true;
    }

    public function getSupportedCurrencies(): ?array
    {
        return Cache::remember('BI_V2_supported_currency_codes', now()->addMonth(), function () {
            $rates = $this->findLatestKurs();

            return $rates ? [...array_keys($rates)] : null;
        });
    }

    public function getExchangeRates(string $baseCurrency, array $targetCurrencies): ?array
    {
        $cacheKey = "BI_V2_currency_rates_{$baseCurrency}";
        $cachedRates = Cache::get($cacheKey);

        if (Cache::missing($cachedRates)) {
            $cachedRates = $this->updateCurrencyRatesCache($baseCurrency);

            if (empty($cachedRates)) {
                return null;
            }
        }

        $filteredRates = array_intersect_key($cachedRates, array_flip($targetCurrencies));
        $filteredRates = array_filter($filteredRates);

        $filteredCurrencies = array_keys($filteredRates);
        $missingCurrencies = array_diff($targetCurrencies, $filteredCurrencies);

        return filled($missingCurrencies) ? null : $filteredRates;
    }

    public function getCachedExchangeRates(string $baseCurrency, array $targetCurrencies): ?array
    {
        return $this->getExchangeRates($baseCurrency, $targetCurrencies);
    }

    public function getCachedExchangeRate(string $baseCurrency, string $targetCurrency): ?float
    {
        $rates = $this->getCachedExchangeRates($baseCurrency, [$targetCurrency]);

        return isset($rates[$targetCurrency]) ? (float) $rates[$targetCurrency] : null;
    }

    public function updateCurrencyRatesCache(string $baseCurrency): ?array
    {
        $rates = $this->findLatestKurs();

        if ($rates) {
            if ($baseCurrency === self::TARGET_CURRENCY) {
                $result = [];
                foreach ($rates as $currency => $rate) {
                    $result[$currency] = 1 / $rate;
                }
                $rates = $result;
            } else {
                if (! isset($rates[$baseCurrency])) {
                    return null;
                }

                $baseRate = $rates[$baseCurrency];
                $result = [];
                foreach ($rates as $currency => $rate) {
                    if ($currency === self::TARGET_CURRENCY) {
                        $result[$currency] = $baseRate;
                    } else {
                        $result[$currency] = $baseRate / $rate;
                    }
                }
                $rates = $result;
            }

            Cache::put("BI_V2_currency_rates_{$baseCurrency}", $rates, now()->addDay());

            return $rates;
        }

        return null;
    }

    private function findLatestKurs(): ?array
    {
        try {
            $date = $this->getLastWeekday();

            $response = Http::withUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
                ->asForm()
                ->withHeaders([
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ])
                ->post(self::BI_KURS_URL, [
                    'startdate' => $date->format('Y-m-d'),
                ]);

            if (! $response->successful()) {
                Log::error('Failed to connect to Bank Indonesia API', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'date' => $date->format('m/d/Y'),
                ]);

                return null;
            }

            $xml = simplexml_load_string($response->body());
            if ($xml === false) {
                Log::error('Failed to parse XML response from Bank Indonesia API', [
                    'date' => $date->format('m/d/Y'),
                ]);

                return null;
            }

            $tables = $xml->children('diffgr', true)->diffgram->children()->NewDataSet->Table;

            $result = [];
            if ($tables) {
                foreach ($tables as $table) {
                    $currency = trim($table->mts_subkurslokal);
                    $value = $this->parseNumber($table->nil_subkurslokal);
                    $sell = $this->parseNumber($table->jual_subkurslokal);
                    $buy = $this->parseNumber($table->beli_subkurslokal);

                    if ($value && $sell && $buy) {
                        $result[$currency] = ($sell + $buy) / (2 * $value);
                    }
                }
            }

            if (empty($result)) {
                Log::error('No valid currency rates found in response', [
                    'date' => $date->format('m/d/Y'),
                ]);

                return null;
            }

            return [...$result, self::TARGET_CURRENCY => 1.0];

        } catch (Exception $e) {
            Log::error('Error parsing Bank Indonesia rates: ' . $e->getMessage());

            return null;
        }
    }

    private function parseNumber(string $text): ?float
    {
        return is_numeric($text) ? (float) $text : null;
    }
}
