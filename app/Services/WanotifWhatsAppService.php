<?php

namespace App\Services;

use App\Contracts\WhatsAppService;
use Illuminate\Support\Facades\Http;

class WanotifWhatsAppService implements WhatsAppService
{
    public function __construct(private readonly string $apiUrl, private readonly string $userToken, private readonly string $deviceId) {}

    public function sendText(string $to, string $message, ?string $footer = null, ?array $buttons = null): bool
    {
        try {
            $res = Http::withToken($this->userToken)->post($this->apiUrl.'/api/send-message', [
                'device_id' => $this->deviceId,
                'phone' => $to,
                'message' => $message,
            ]);
            if ($res->json('success')) {
                return true;
            }
        } catch (\Throwable $th) {
        }

        return false;
    }

    public function sendImage(string $to, string $imageData, ?string $caption = null): bool
    {
        return false;
    }

    public function sendDocument(string $to, string $documentData, string $fileName): bool
    {
        return false;
    }
}
