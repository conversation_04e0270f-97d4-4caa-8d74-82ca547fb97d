<?php

namespace App\Services;

use App\Models\Reminder;
use App\ReminderTypes\ReminderType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Filesystem\Filesystem;
use ReflectionClass;
use <PERSON>ymfony\Component\Finder\SplFileInfo;

class ReminderService
{
    protected array $reminderTypes = [];

    protected array $reminderTypesOptions = [];

    public function __construct()
    {
        $filesystem = app(Filesystem::class);

        $this->reminderTypes = collect($filesystem->allFiles(app_path('ReminderTypes')))
            ->map(function (SplFileInfo $file) {
                return (string) str(
                    app()->getNamespace().
                    'ReminderTypes\\'.
                    $file->getRelativePathname()
                )
                    ->replace(['/', '.php'], ['\\', '']);
            })
            ->filter(fn (string $class) => is_subclass_of($class, ReminderType::class) && ! (new ReflectionClass($class))->isAbstract())
            ->toArray();

        $this->reminderTypesOptions = collect($this->reminderTypes)
            ->mapWithKeys(fn (string $class) => [
                class_basename($class) => $class::getLabel(),
            ])
            ->sort()
            ->toArray();
    }

    public function getReminderTypes(): array
    {
        return $this->reminderTypes;
    }

    public function getReminderTypesOptions(): array
    {
        return $this->reminderTypesOptions;
    }

    protected function getFQDN(string $type): string
    {
        return app()->getNamespace().'ReminderTypes\\'.$type;
    }

    public function getLabel(string $type): string
    {
        return $this->getFQDN($type)::getLabel();
    }

    public function getDefaultTemplate(string $type): string
    {
        return $this->getFQDN($type)::getDefaultTemplate();
    }

    public function getTemplateTags(string $type): array
    {
        return $this->getFQDN($type)::getTemplateTags();
    }

    public function findRelevantModels(string $type, int $interval, array $additionalData = []): Collection
    {
        return $this->getFQDN($type)::findRelevantModels($interval, $additionalData);
    }

    public function getEventDate(string $type, Model $model): ?Carbon
    {
        return $this->getFQDN($type)::getEventDate($model);
    }

    public function getTagsValues(string $type, Model $model): array
    {
        return $this->getFQDN($type)::getTagsValues($model);
    }

    public function getModelClass(string $type): string
    {
        return $this->getFQDN($type)::getModelClass();
    }

    public function generateContent(Reminder $reminder, Model $model): string
    {
        $template = $reminder->template;
        $tags = $this->getTagsValues($reminder->reminder_type, $model);

        foreach ($tags as $tag => $value) {
            $template = str_replace("{{$tag}}", $value, $template);
        }

        return $template;
    }

    public function getAdditionalFormSchema(string $type): array
    {
        return $this->getFQDN($type)::getAdditionalFormSchema();
    }
}
