<?php

namespace APp\Filament\Actions;

use Closure;
use Filament\Tables\Actions\Action;
use Illuminate\Contracts\Support\Htmlable;

class WhatsAppTableAction extends Action
{
    protected string|Closure|null $phone = null;

    public static function getDefaultName(): ?string
    {
        return 'whatsapp';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->icon('tabler-brand-whatsapp');
        $this->color('whatsapp');
    }

    public function getLabel(): string|Htmlable|null
    {
        if ($this->getName() == 'whatsapp') {
            return 'WhatsApp';
        }

        return parent::getLabel();
    }

    public function phone(string|Closure|null $phone): static
    {
        $this->phone = $phone;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->evaluate($this->phone);
    }

    public function getUrl(): ?string
    {
        return wa_chat_url($this->getPhone() ?? '');
    }
}
