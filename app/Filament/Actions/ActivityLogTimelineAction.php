<?php

namespace App\Filament\Actions;

use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Rmsramos\Activitylog\Actions\ActivityLogTimelineSimpleAction;
use Spatie\Activitylog\Models\Activity;

class ActivityLogTimelineAction extends ActivityLogTimelineSimpleAction
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->color('warning');
        $this->outlined();
        $this->tooltip(null);
        $this->icon('tabler-history');
        $this->modalIcon('tabler-history');
        $this->query = function (?Model $record) {
            return Activity::query()
                ->with(['subject', 'causer'])
                ->where(function (Builder $query) use ($record) {
                    $query->where(function (Builder $q) use ($record) {
                        $q->where('subject_type', $record->getMorphClass())
                            ->where('subject_id', $record->getKey());
                    })->when($this->getWithRelations(), function (Builder $query, array $relations) use ($record) {
                        foreach ($relations as $relation) {
                            $model = get_class($record->{$relation}()->getRelated());
                            $query->orWhere(function (Builder $q) use ($record, $model, $relation) {
                                $q->where('subject_type', (new $model)->getMorphClass())
                                    ->whereIn('subject_id', $record->{$relation}()->pluck('id'));
                            });
                        }
                    });
                });
        };
    }

    protected function formatActivityData($activity): array
    {
        return [
            'log_name' => $activity->log_name,
            'description' => $activity->description,
            'subject' => $activity->subject,
            'event' => $activity->event,
            'causer' => $activity->causer,
            'properties' => $this->formatDateValues(json_decode($activity->properties, true)),
            'batch_uuid' => $activity->batch_uuid,
            'update' => $activity->updated_at,
        ];
    }

    private static function formatDateValues(array | string | null $value): array | string | null
    {
        if (is_null($value) || is_numeric($value)) {
            return $value;
        }

        if (is_array($value)) {
            foreach ($value as &$item) {
                $item = self::formatDateValues($item);
            }

            return $value;
        }

        try {
            return Carbon::parse($value)
                ->format(config('filament-activitylog.datetime_format', 'd/m/Y H:i:s'));
        } catch (InvalidFormatException $e) {
            return $value;
        }
    }
}
