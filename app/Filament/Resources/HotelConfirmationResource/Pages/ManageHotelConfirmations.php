<?php

namespace App\Filament\Resources\HotelConfirmationResource\Pages;

use App\Filament\Resources\HotelConfirmationResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords\Tab;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;

class ManageHotelConfirmations extends ManageRecords
{
    protected static string $resource = HotelConfirmationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('lg'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            'no-groups' => Tab::make()
                ->modifyQueryUsing(
                    fn (Builder $query) => $query
                        ->whereNull('group_id')
                ),
        ];
    }
}
