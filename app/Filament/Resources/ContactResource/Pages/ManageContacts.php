<?php

namespace App\Filament\Resources\ContactResource\Pages;

use App\Enums\ContactType;
use App\Filament\Resources\ContactResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;

class ManageContacts extends ManageRecords
{
    protected static string $resource = ContactResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('sm'),
        ];
    }

    public function getTabs(): array
    {
        return collect(ContactType::cases())
            ->mapWithKeys(function ($type) {
                $tabKey = strtolower($type->name);

                return [
                    $tabKey => Tab::make($type->getLabel())->modifyQueryUsing(
                        fn (Builder $query) => $query->where('type', $type)
                    ),
                ];
            })
            ->toArray();
    }
}
