<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HotelResource\Pages;
use App\Filament\Resources\HotelResource\RelationManagers;
use App\Models\Hotel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class HotelResource extends Resource
{
    protected static ?string $model = Hotel::class;

    protected static ?string $navigationGroup = 'Hotels';

    protected static ?string $navigationLabel = 'All Hotels';

    protected static ?int $navigationSort = 0;

    public static function getGloballySearchableAttributes(): array
    {
        return ['name'];
    }

    public static function getGlobalSearchResultTitle(Model $record): string
    {
        return $record->fullname;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Name')
                    ->required(),
                Forms\Components\Select::make('city')
                    ->label('City')
                    ->options(Hotel::getCities())
                    ->required(),
                // Rating::make('stars'),
                Forms\Components\Select::make('stars')
                    ->options([
                        '1' => '★',
                        '2' => '★★',
                        '3' => '★★★',
                        '4' => '★★★★',
                        '5' => '★★★★★',
                    ]),
                Forms\Components\TextInput::make('distance')
                    ->numeric()
                    ->suffix('m'),
                Forms\Components\Fieldset::make('Pricing')
                    ->schema([
                        Forms\Components\TextInput::make('price_quad')
                            ->label('Quad')
                            ->numeric()
                            ->prefix('SAR'),
                        Forms\Components\TextInput::make('price_triple')
                            ->label('Triple')
                            ->numeric()
                            ->prefix('SAR'),
                        Forms\Components\TextInput::make('price_double')
                            ->label('Double')
                            ->numeric()
                            ->prefix('SAR'),
                    ])
                    ->columns(3)
                    ->columnSpanFull(),
            ])
            ->columns(['md' => 2]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->description(fn ($record) => $record->city)
                    ->searchable(['name', 'city'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('distance')
                    ->getStateUsing(fn ($record) => $record->distance ? $record->distance . 'm' : null)
                    ->sortable(),
                Tables\Columns\ViewColumn::make('stars')
                    ->view('filament.tables.columns.rating')
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->label('Price range')
                    ->getStateUsing(fn ($record) => (int) $record->price_quad ? (int) $record->price_quad . ' - ' . (int) $record->price_double . ' SAR' : null)
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('city')
                    ->options(Hotel::getCities()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function ($data) {
                        $data['stars'] = $data['stars'] ?? 0;
                        $data['distance'] = $data['distance'] ?? 0;
                        $data['price_quad'] = $data['price_quad'] ?? 0;
                        $data['price_triple'] = $data['price_triple'] ?? 0;
                        $data['price_double'] = $data['price_double'] ?? 0;

                        return $data;
                    }),
                Tables\Actions\DeleteAction::make()
                    ->modalDescription(function ($record) {
                        $text = '<p>Are you sure you would like to do this?</p>';
                        $groups = $record->groups()->with(['customer'])->get();
                        if (count($groups) > 0) {
                            $text = '
                            <p>This hotel cannot be removed because it is used in these groups:</p>
                            <table class="w-full divide-y table-auto text-start">
                                <thead>
                                    <tr class="bg-gray-500/5">
                                        <th class="p-0">
                                            <div class="flex gap-x-1 items-center px-4 py-2 w-full text-sm font-medium text-gray-600 whitespace-nowrap cursor-default">Group</div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="whitespace-nowrap divide-y">
                            ';
                            foreach ($groups as $g) {
                                $text .= '<tr><td class="px-4 py-2">'
                                    . '<div class="text-sm text-gray-500">'
                                    . $g->customer->name
                                    . '</div> '
                                    . $g->name
                                    . '</td></tr>';
                            }
                            $text .= '
                                </tbody>
                            </table>
                            ';
                        }

                        return new HtmlString($text);
                    }),
                Tables\Actions\Action::make('merge')
                    ->color('gray')
                    ->action(function ($record, $data) {
                        $hotel_ids = $data['hotel_ids'];
                        DB::table('group_hotel')
                            ->whereIn('hotel_id', $hotel_ids)
                            ->update(['hotel_id' => $record->id]);
                        DB::table('hotels')
                            ->whereIn('id', $hotel_ids)
                            ->delete();
                    })
                    ->form(function ($record) {
                        return [
                            Forms\Components\CheckboxList::make('hotel_ids')
                                ->label('Hotel')
                                ->options(
                                    Hotel::query()->where('city', $record->city)
                                        ->where('id', '!=', $record->id)
                                        ->orderBy('name')
                                        ->pluck('name', 'id')
                                ),
                        ];
                    })
                    ->visible(fn () => auth()->user()->isSuperAdmin())
                    ->modalWidth('sm'),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageHotels::route('/'),
            'view' => Pages\ViewHotel::route('{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\GroupHotelsRelationManager::class,
        ];
    }
}
