<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HotelBrokerResource\Pages;
use App\Filament\Resources\HotelBrokerResource\RelationManagers;
use App\Models\Vendors\HotelBroker;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class HotelBrokerResource extends Resource
{
    protected static ?string $model = HotelBroker::class;

    protected static ?string $navigationGroup = 'Vendors';

    protected static ?int $navigationSort = 1;

    public static function getGloballySearchableAttributes(): array
    {
        return ['company_name', 'contact_name', 'contact_phone'];
    }

    public static function getGlobalSearchResultTitle(Model $record): string
    {
        return $record->company_name;
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'Contact' => $record->contact_details,
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                InfoLists\Components\Section::make()
                    ->schema([
                        InfoLists\Components\TextEntry::make('company_name'),
                        InfoLists\Components\TextEntry::make('contact_name'),
                        InfoLists\Components\TextEntry::make('contact_phone')
                            ->hintAction(
                                fn ($state) => InfoLists\Components\Actions\Action::make('chat')
                                    ->icon('tabler-brand-whatsapp')
                                    ->color('success')
                                    ->url(wa_chat_url($state))
                                    ->openUrlInNewTab()
                                    ->visible((bool) $state)
                            ),
                    ])
                    ->columns(['lg' => 3]),
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('company_name')
                    ->required(),
                Forms\Components\TextInput::make('contact_name'),
                Forms\Components\TextInput::make('contact_phone'),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('company_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('contact_name')
                    ->searchable()
                    ->sortable(),
                PhoneColumn::make('contact_phone')
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_payment_at')
                    ->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->modalWidth('lg'),
                    Tables\Actions\DeleteAction::make()
                        ->modalDescription(function ($record) {
                            $text = '<p>Are you sure you would like to do this?</p>';
                            $groups = $record->group_hotels()->with(['group.customer'])->get();
                            if (count($groups) > 0) {
                                $text = '
                                <p>This data cannot be removed because it is used in these groups:</p>
                                <table class="w-full divide-y table-auto text-start">
                                    <thead>
                                        <tr class="bg-gray-500/5">
                                            <th class="p-0">
                                                <div class="flex gap-x-1 items-center px-4 py-2 w-full text-sm font-medium text-gray-600 whitespace-nowrap cursor-default">Group</div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="whitespace-nowrap divide-y">
                                ';
                                foreach ($groups as $g) {
                                    $text .= '<tr><td class="px-4 py-2">'
                                        . '<div class="text-sm text-gray-500">'
                                        . $g->group->customer->name
                                        . '</div> '
                                        . $g->group->name
                                        . '</td></tr>';
                                }
                                $text .= '
                                    </tbody>
                                </table>
                                ';
                            }

                            return new HtmlString($text);
                        }),
                ]),
            ])
            ->defaultSort('company_name');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\HotelsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageHotelBrokers::route('/'),
            'view' => Pages\ViewHotelBroker::route('/{record}'),
        ];
    }
}
