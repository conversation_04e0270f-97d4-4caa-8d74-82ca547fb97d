<?php

namespace App\Filament\Resources;

use App\Filament\Resources\InstitutionResource\Pages;
use App\Filament\Resources\InstitutionResource\RelationManagers;
use App\Filament\Resources\InstitutionResource\Widgets;
use App\Models\Institution;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

/**
 * @deprecated 2025.02.17
 */
class InstitutionResource extends Resource
{
    protected static ?string $model = Institution::class;

    protected static ?string $navigationGroup = 'Master Data';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'muassasah';

    protected static ?int $navigationSort = 3;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Name')
                    ->required(),
                Forms\Components\TextInput::make('email')
                    ->email(),
                Forms\Components\TextInput::make('phone'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_pax'),
                Tables\Columns\TextColumn::make('latest_arrival')
                    ->date(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->modalSubheading(function ($record) {
                        $text = '<p>Are you sure you would like to do this?</p>';
                        $groups = $record->groups()->with(['customer'])->get();
                        if (count($groups) > 0) {
                            $text = '
                            <p>This data cannot be removed because it is used in these groups:</p>
                            <table class="w-full divide-y table-auto text-start">
                                <thead>
                                    <tr class="bg-gray-500/5">
                                        <th class="p-0">
                                            <div class="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-600 cursor-default gap-x-1 whitespace-nowrap ">Group</div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y whitespace-nowrap">
                            ';
                            foreach ($groups as $g) {
                                $text .= '<tr><td class="px-4 py-2">'
                                    . '<div class="text-sm text-gray-500">'
                                    . $g->customer->name
                                    . '</div> '
                                    . $g->name
                                    . '</td></tr>';
                            }
                            $text .= '
                                </tbody>
                            </table>
                            ';
                        }

                        return new HtmlString($text);
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\GroupsRelationManager::class,
            RelationManagers\PaymentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageInstitutions::route('/'),
            'view' => Pages\ViewInstitution::route('/{record}'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\InstitutionOverview::class,
        ];
    }
}
