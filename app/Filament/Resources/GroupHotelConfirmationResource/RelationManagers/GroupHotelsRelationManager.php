<?php

namespace App\Filament\Resources\GroupHotelConfirmationResource\RelationManagers;

use App\Enums\HotelRoomType;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class GroupHotelsRelationManager extends RelationManager
{
    protected static string $relationship = 'group_hotels';

    protected static ?string $recordTitleAttribute = 'hotel.name';

    protected static ?string $modelLabel = 'hotel';

    protected static ?string $pluralModelLabel = 'hotels';

    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('broker')
                    ->relationship('broker', 'company_name')
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('confirmation_number')
                    ->label('Confirmation No.'),
                Forms\Components\FileUpload::make('confirmation_file')
                    ->previewable(false),
                Forms\Components\Toggle::make('is_confirmed')
                    ->label('Confirmed'),
                Forms\Components\Textarea::make('more_info'),
            ])
            ->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                BadgeableColumn::make('hotel.name')
                    ->description(fn ($record) => $record->hotel?->city ?? null, 'above')
                    ->suffixBadges([
                        Badge::make('type')
                            ->label(fn ($record) => HotelRoomType::tryFrom($record->meta['room_type'] ?? '')?->getShortLabel())
                            ->visible(fn ($record) => $record->meta['room_type'] ?? null),
                        Badge::make('meal')
                            ->label(fn ($record) => $record->meta['meal'] ?? null)
                            ->visible(fn ($record) => $record->meta['meal'] ?? null),
                    ]),
                Tables\Columns\TextColumn::make('composition')
                    ->badge()
                    ->getStateUsing(fn ($record) => collect([
                        $record->room_single_count ? $record->room_single_count . ' single' : null,
                        $record->room_double_count ? $record->room_double_count . ' double' : null,
                        $record->room_triple_count ? $record->room_triple_count . ' triple' : null,
                        $record->room_quad_count ? $record->room_quad_count . ' quad' : null,
                        $record->room_quint_count ? $record->room_quint_count . ' quint' : null,
                    ])->filter()->toArray()),
                Tables\Columns\TextColumn::make('check_in')
                    ->date(),
                Tables\Columns\TextColumn::make('broker.company_name')
                    ->default('-')
                    ->description(fn ($record) => $record->broker?->contact_details ?? null),
                Tables\Columns\IconColumn::make('is_confirmed')
                    ->boolean()
                    ->label('Confirmation')
                    ->view('filament.tables.columns.hotel-booking-hotel-confirmation')
                    ->disabledClick(),
                Tables\Columns\TextColumn::make('more_info')
                    ->wrap()
                    ->extraAttributes([
                        'class' => 'text-sm',
                    ]),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('lg'),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('check_in');
    }
}
