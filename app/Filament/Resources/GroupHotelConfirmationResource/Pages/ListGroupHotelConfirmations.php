<?php

namespace App\Filament\Resources\GroupHotelConfirmationResource\Pages;

use App\Filament\Resources\GroupHotelConfirmationResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListGroupHotelConfirmations extends ListRecords
{
    protected static string $resource = GroupHotelConfirmationResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    protected function getTableQuery(): Builder
    {
        return static::getModel()::query()
            ->with([
                'customer',
                'hotels' => fn ($query) => $query->orderByPivot('check_in'),
            ]);
    }
}
