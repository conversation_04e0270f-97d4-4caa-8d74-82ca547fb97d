<?php

namespace App\Filament\Resources\UserCashResource\Pages;

use App\Actions\ExportUserCashAttachments;
use App\Exports\UserCashExport;
use App\Filament\Resources\UserCashResource;
use App\Filament\Resources\UserCashResource\Widgets;
use App\Imports\UserCashImport;
use EightyNine\ExcelImport\ExcelImportAction;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageUserCashes extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = UserCashResource::class;

    protected static ?string $title = 'Kas Advance';

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('download_template')
                    ->icon('phosphor-file')
                    ->action(function () {
                        return response()->download(storage_path('templates/template kas advance.xlsx'));
                    }),
                ExcelImportAction::make()
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->color('gray')
                    ->uploadField(fn ($upload) => $upload->label('Excel file'))
                    ->use(UserCashImport::class),
            ])
                ->label('Import')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('gray')
                ->button()
                ->visible(auth('web')->user()->hasRole(['Admin', 'Finance'])),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new UserCashExport($this->tableFilters))->download();
                    }),
                Actions\Action::make('attachments')
                    ->icon('phosphor-file-archive')
                    ->action(function () {
                        return response()->download(ExportUserCashAttachments::run($this->tableFilters));
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button()
                ->visible(auth('web')->user()->hasRole(['Admin', 'Finance'])),
            Actions\CreateAction::make()
                ->label('Add')
                ->modalHeading('Add cash'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\UserCashOverview::class,
        ];
    }
}
