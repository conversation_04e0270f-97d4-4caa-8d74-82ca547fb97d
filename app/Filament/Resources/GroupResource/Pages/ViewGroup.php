<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Actions\Group\GeneratePIF;
use App\Filament\Finance\Resources\GroupFinanceResource;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Filament\Resources\GroupHotelConfirmationResource;
use App\Filament\Resources\GroupResource;
use App\Filament\Resources\UserCashResource;
use App\Models\Group;
use App\Models\GroupData;
use Awcodes\Shout\Components\ShoutEntry;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions;
use Filament\Forms;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\HtmlString;
use Umrahservice\Groups\Filament\Infolist\GroupPifPreview;

/**
 * @property Group $record
 */
class ViewGroup extends ViewRecord
{
    protected static string $resource = GroupResource::class;

    // protected static string $view = 'filament.resources.groups.pages.view-group';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'PIF';

    public function mount(int | string $record): void
    {
        parent::mount($record);

        $this->record->load([
            'airport_handler_arr', 'airport_handler_dep',
            'mutawif', 'mutawif_2', 'mutawif_3',
            'group_hotels' => fn ($q) => $q->with(['hotel'])->orderBy('sort')->orderBy('check_in'),
            'manasiks' => fn ($q) => $q->orderBy('sort')->orderBy('date'),
            'itineraries' => fn ($q) => $q->orderBy('sort')->orderBy('date'),
        ]);
    }

    public function getRelationManagers(): array
    {
        return [];
    }

    protected function getFormSchema(): array
    {
        return [];
    }

    protected function getHeaderActions(): array
    {
        $data = $this->record->group_data;
        $files = [];
        if ($data) {
            if ($data->visa) {
                $files[] = [
                    'name' => 'Visa',
                    'url' => route('admin.groups.file', ['group' => $data->group_id, 'name' => 'visa', 'file' => $data->visa]),
                ];
            }
            if ($data->ticket) {
                $files[] = [
                    'name' => 'Ticket',
                    'url' => route('admin.groups.file', ['group' => $data->group_id, 'name' => 'ticket', 'file' => $data->ticket]),
                ];
            }
            if ($data->roomlist) {
                $files[] = [
                    'name' => 'Roomlist',
                    'url' => route('admin.groups.file', ['group' => $data->group_id, 'name' => 'roomlist', 'file' => $data->roomlist]),
                ];
            }
            if ($data->manifest) {
                $files[] = [
                    'name' => 'Manifest',
                    'url' => route('admin.groups.file', ['group' => $data->group_id, 'name' => 'manifest', 'file' => $data->manifest]),
                ];
            }
            if (filled($data->files)) {
                foreach ($data->files as $file) {
                    if ($file['file']) {
                        $files[] = [
                            'name' => $file['name'],
                            'url' => route('admin.groups.file', ['group' => $data->group_id, 'name' => $file['name'], 'file' => $file['file']]),
                        ];
                    }
                }
            }
        }

        return [
            Actions\ActionGroup::make(collect($files)->map(
                fn ($f, $k) => Actions\Action::make('download_' . $k)
                    ->label($f['name'])
                    ->url($f['url'])
                    ->icon('heroicon-o-document-magnifying-glass')
                    ->openUrlInNewTab(),
            )->toArray())
                ->icon('heroicon-o-arrow-down-tray')
                ->visible(fn () => auth()->user()->can('groups.viewData') || auth()->user()->hasRole(['Admin', 'Operator', 'Airport Handler', 'Customer'])),
            Actions\ActionGroup::make([
                Actions\Action::make('group_data')
                    ->form([
                        TableRepeater::make('files')
                            ->label('Files')
                            ->headers([
                                Header::make('Name'),
                                Header::make('File'),
                            ])
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->datalist(GroupData::FILES)
                                    ->required(),
                                Forms\Components\FileUpload::make('file')
                                    ->previewable(false),
                            ])
                            ->defaultItems(0)
                            ->addActionLabel('Add File'),
                    ])
                    ->mountUsing(function ($form) {
                        $group_data = GroupData::where('group_id', $this->record->id)->first();

                        $form->fill([
                            'files' => $group_data ? $group_data->files : collect(GroupData::FILES)
                                ->map(fn ($f) => ['name' => $f, 'file' => null])
                                ->toArray(),
                        ]);
                    })
                    ->action(function ($data) {
                        GroupData::query()
                            ->updateOrCreate(['group_id' => $this->record->id], $data);
                    })
                    ->modalButton('Save'),
            ])
                ->icon('heroicon-o-plus')
                ->visible(fn () => auth()->user()->hasRole(['Admin', 'Operator'])),
            Actions\Action::make('booking')
                ->url(GroupHotelConfirmationResource::getUrl('view', ['record' => $this->record]))
                ->icon('heroicon-o-document-text')
                ->openUrlInNewTab()
                ->color('gray')
                ->visible(fn () => auth()->user()->hasRole('Admin')),
            // Actions\Action::make('print')
            //     ->iconButton()
            //     ->url(route('admin.groups.print', $this->record->id))
            //     ->icon('heroicon-o-printer')
            //     ->openUrlInNewTab()
            //     ->color('success')
            //     ->visible($this->record->is_confirmed),
            Actions\Action::make('pdf')
                ->label('PDF')
                ->action(function ($record) {
                    return response()->download(GeneratePIF::run($record));
                })
                ->color('gray')
                ->icon('heroicon-o-arrow-down-tray')
                ->visible($this->record->is_confirmed),
            Actions\ActionGroup::make([
                Actions\Action::make('invoices')
                    ->icon('phosphor-invoice')
                    ->url(InvoiceResource::getUrl('index', ['tableFilters[group][group_id]' => $this->record->id], panel: 'finance'))
                    ->visible(
                        (auth()->user()->can('invoices.viewAny') || auth()->user()->hasRole(['Admin', 'Finance']))
                    ),
                Actions\Action::make('kas_advance')
                    ->icon('heroicon-o-banknotes')
                    ->url(UserCashResource::getUrl('index', ['tableFilters[group][group_id]' => $this->record->id])),
                Actions\Action::make('expenses')
                    ->icon('heroicon-o-banknotes')
                    ->url(GroupFinanceResource::getUrl('expenses', ['record' => $this->record]))
                    ->visible(
                        (auth()->user()->can('groups.viewFinance') || auth()->user()->hasRole(['Admin', 'Finance']))
                    ),
                Actions\Action::make('closing_report')
                    ->icon('heroicon-o-document-text')
                    ->url(GroupFinanceResource::getUrl('closing-report', ['record' => $this->record]))
                    ->visible(
                        (auth()->user()->can('groups.viewFinance') || auth()->user()->hasRole(['Admin', 'Finance']))
                    ),
            ])
                ->color('gray')
                ->label('Finance')
                ->icon('heroicon-m-chevron-down')
                ->iconPosition('after')
                ->button()
                ->visible(
                    $this->record->is_confirmed
                ),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->columns(1)
            ->extraAttributes([
                'style' => 'overflow-x: auto;',
            ])
            ->schema([
                ShoutEntry::make('period')
                    ->content(function ($record) {
                        $period = $record->getPeriod();

                        return new HtmlString($period
                            ? 'Grup ini masuk dalam data periode <strong>' . $period->name . '</strong>.'
                            : 'Grup ini tidak masuk dalam data periode ini.');
                    })
                    ->type('warning')
                    ->visible(fn ($record) => ! $record->isInCurrentPeriod()),
                Infolists\Components\Section::make()
                    ->compact()
                    ->extraAttributes([
                        'style' => 'width: 210mm; min-height: 297mm; padding: 0;',
                        'class' => 'fi-print-preview mx-auto overflow-hidden',
                    ])
                    ->schema([
                        GroupPifPreview::make('preview')
                            ->group($this->getRecord()),
                    ]),
            ]);
    }
}
