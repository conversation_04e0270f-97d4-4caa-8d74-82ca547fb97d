<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Filament\Resources\GroupResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Umrahservice\Groups\Enums\GroupProgress;

class ListGroups extends ListRecords
{
    protected static string $resource = GroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return collect(GroupProgress::cases())
            ->prepend('all')
            ->filter(fn ($progress) => auth()->user()->hasExactRoles('Mutawif')
                    ? $progress == 'all' || $progress !== GroupProgress::Finished
                    : true)
            ->mapWithKeys(function ($progress, $key) {
                $tabKey = $key === 0 ? 'all' : strtolower($progress->name);

                return [
                    $tabKey => Tab::make()->when(
                        $progress !== 'all',
                        fn (Tab $tab) => $tab->modifyQueryUsing(
                            fn (Builder $query) => $query->where('progress', $progress)
                        )
                    ),
                ];
            })
            ->toArray();
    }

    public function table(Table $table): Table
    {
        return parent::table($table)
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->withCount('bill_items')
                ->currentPeriod());
    }
}
