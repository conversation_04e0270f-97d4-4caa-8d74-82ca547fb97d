<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Filament\Resources\GroupResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;

class ManageGroupActivities extends ManageRelatedRecords
{
    protected static string $resource = GroupResource::class;

    protected static string $relationship = 'activities';

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    protected static ?string $navigationLabel = 'History';

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return auth()->user()->hasRole(['Admin']);
    }

    protected function authorizeAccess(): void
    {
        abort_unless(auth()->user()->hasRole(['Admin']), 403);
    }

    public function getTitle(): string | Htmlable
    {
        return 'History ' . $this->getRecordTitle();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('causer')
                            ->content(fn ($record) => $record->causer?->name ?? 'System'),
                        Forms\Components\Placeholder::make('description')
                            ->content(fn ($record) => $record->description),
                    ])
                    ->columns(['md' => 2]),
                Forms\Components\KeyValue::make('properties.attributes')
                    ->label('Attributes')
                    ->columnSpan([
                        'default' => 2,
                        'sm' => 1,
                    ]),
                Forms\Components\KeyValue::make('properties.old')
                    ->label('Old attributes')
                    ->columnSpan([
                        'default' => 2,
                        'sm' => 1,
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('description')
            ->modifyQueryUsing(fn ($query) => $query->latest())
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('causer.name')
                    ->label('User'),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')
                    ->badge()
                    ->formatStateUsing(fn ($state) => str($state)->headline())
                    ->colors([
                        'success' => 'created',
                        'warning' => 'updated',
                        'danger' => 'deleted',
                    ])
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Logged at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }
}
