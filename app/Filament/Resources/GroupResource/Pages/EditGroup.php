<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Filament\Resources\GroupResource;
use App\Models\Finance\Invoice;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Kenepa\ResourceLock\Resources\Pages\Concerns\UsesResourceLock;

class EditGroup extends EditRecord
{
    use UsesResourceLock;

    protected static string $resource = GroupResource::class;

    public static function getNavigationLabel(): string
    {
        return 'Edit';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['invoice_ids'] = $this->getRecord()->invoices->pluck('id');

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['transport_bus_count'] = $data['transport_bus_count'] ?? 0;

        $data['updated_by_id'] = auth()->user()->id;

        if (isset($data['invoice_ids'])) {
            $invoiceIds = $data['invoice_ids'] ?? [];
            Invoice::whereNotIn('id', $invoiceIds)
                ->where('group_id', $this->getRecord()->id)
                ->update(['group_id' => null]);
            Invoice::whereIn('id', $invoiceIds)
                ->update(['group_id' => $this->getRecord()->id]);
            unset($data['invoice_ids']);
        }

        return $data;
    }
}
