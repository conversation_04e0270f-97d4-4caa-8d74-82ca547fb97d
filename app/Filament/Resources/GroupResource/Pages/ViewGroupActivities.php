<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Filament\Resources\GroupResource;
use Illuminate\Support\HtmlString;
use JaOcero\ActivityTimeline\Pages\ActivityTimelinePage;

class ViewGroupActivities extends ActivityTimelinePage
{
    protected static string $resource = GroupResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    protected static ?string $navigationLabel = 'History';

    protected function configuration(): array
    {
        return [
            'activity_section' => [
                'label' => 'Activities',
                'description' => 'These are the activities that have been recorded.',
                'show_items_count' => 0,
                'show_items_label' => 'Show more',
                'show_items_icon' => 'heroicon-o-chevron-right',
                'show_items_color' => 'gray',
                'aside' => true,
                'empty_state_heading' => 'No activities yet',
                'empty_state_description' => 'Check back later for activities that have been recorded.',
                'empty_state_icon' => 'heroicon-o-bolt-slash',
                'heading_visible' => true,
                'extra_attributes' => [],
            ],
            'activity_title' => [
                'placeholder' => 'No title is set',
                'allow_html' => true,
            ],
            'activity_description' => [
                'placeholder' => 'No description is set',
                'allow_html' => true,
            ],
            'activity_date' => [
                'name' => 'created_at',
                'date' => 'F j, Y g:i A',
                'placeholder' => 'No date is set',
                'modify_state' => function ($state) {
                    return new HtmlString($state);
                },
            ],
            'activity_icon' => [
                                       'icon' => fn (?string $state): ?string => match ($state) {
                                           default => null
                                       },
                                       'color' => fn (?string $state): ?string => match ($state) {
                                           default => null
                                       },
                                   ],
        ];
    }
}
