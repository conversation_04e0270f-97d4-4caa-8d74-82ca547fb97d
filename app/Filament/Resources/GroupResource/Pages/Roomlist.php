<?php

namespace App\Filament\Resources\GroupResource\Pages;

use App\Enums\RoomCapacity;
use App\Filament\Resources\GroupResource;
use App\Models\Group;
use App\Models\GroupPilgrim;
use App\Models\Room;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;

class Roomlist extends ManageRelatedRecords
{
    protected static string $resource = GroupResource::class;

    protected static string $relationship = 'rooms';

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $title = 'Roomlist';

    public function getHeading(): string | Htmlable
    {
        return 'Roomlist ' . $this->getRecordTitle();
    }

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return auth('web')->user()->can('groups.roomlist');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('luggage_tags')
                    ->icon('tabler-tags')
                    ->url(route('admin.groups.print.luggage-tags', $this->getOwnerRecord()))
                    ->openUrlInNewTab()
                    ->visible((bool) count($this->getOwnerRecord()->pilgrims)),
                Actions\Action::make('roomlist')
                    ->icon('tabler-bed')
                    ->url(route('admin.groups.print.roomlist', $this->getOwnerRecord()))
                    ->openUrlInNewTab()
                    ->visible((bool) count($this->getOwnerRecord()->rooms)),
            ])
                ->label('Print')
                ->color('gray')
                ->button()
                ->icon('heroicon-m-chevron-down')
                ->iconPosition('after'),
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->unique('rooms', ignoreRecord: true, modifyRuleUsing: fn ($rule) => $rule->where('group_id', $this->getRecord()->id))
                    ->columnSpanFull()
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator'])),
                Forms\Components\TextInput::make('number')
                    ->required()
                    ->unique('rooms', ignoreRecord: true, modifyRuleUsing: fn ($rule) => $rule->where('group_id', $this->getRecord()->id))
                    ->rule('numeric')
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator'])),
                Forms\Components\Select::make('capacity')
                    ->required()
                    ->options(RoomCapacity::class)
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator'])),
                Forms\Components\TextInput::make('meta.details')
                    ->label('Details')
                    ->columnSpanFull(),
                Forms\Components\CheckboxList::make('meta.group_hotel_ids')
                    ->label('Hotels')
                    ->options(
                        $this->getRecord()
                            ->group_hotels()
                            ->orderBy('check_in')
                            ->get()
                            ->mapWithKeys(function ($gh) {
                                return [
                                    $gh->id => sprintf(
                                        '%s (%s - %s)',
                                        $gh->hotel_name_with_type,
                                        $gh->check_in?->format('j M Y') ?? 'n/a',
                                        $gh->check_out?->format('j M Y') ?? 'n/a'
                                    ),
                                ];
                            })
                    )
                    ->columnSpanFull(),
                TableRepeater::make('meta.room_numbers')
                    ->deletable(false)
                    ->addable(false)
                    ->reorderable(false)
                    ->columnSpanFull()
                    ->headers([
                        Header::make('Hotel'),
                        Header::make('Room Number'),
                    ])
                    ->schema([
                        Forms\Components\Select::make('group_hotel_id')
                            ->disabled()
                            ->dehydrated()
                            ->options(
                                $this->getRecord()
                                    ->group_hotels()
                                    ->orderBy('check_in')
                                    ->get()
                                    ->mapWithKeys(fn ($gh) => [
                                        $gh->id => sprintf(
                                            '%s (%s - %s)',
                                            $gh->hotel_name_with_type,
                                            $gh->check_in?->format('j M Y') ?? 'n/a',
                                            $gh->check_out?->format('j M Y') ?? 'n/a'
                                        ),
                                    ])
                            )
                            ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                            ->required(),
                        Forms\Components\TextInput::make('room_number'),
                    ]),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        $groupHotels = $this->getRecord()->group_hotels()->orderBy('check_in')->with('hotel')->get()->keyBy('id');

        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['pilgrims']))
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->description(fn (Room $record) => $record->number),
                Tables\Columns\TextColumn::make('capacity'),
                Tables\Columns\TextColumn::make('pilgrims_mutawifs')
                    ->label('Pilgrims')
                    ->view('filament.tables.columns.room-pilgrims'),
                Tables\Columns\TextColumn::make('group_hotels')
                    ->label('Hotels')
                    ->getStateUsing(fn ($record) => $groupHotels->values()->whereIn('id', $record->meta['group_hotel_ids'] ?? [])->all())
                    ->formatStateUsing(fn ($state) => $state->hotel_name_with_type)
                    ->badge()
                    ->listWithLineBreaks(),
                Tables\Columns\TextColumn::make('meta.room_numbers')
                    ->label('Room Numbers')
                    ->wrapHeader()
                    ->formatStateUsing(function ($state) use ($groupHotels) {
                        if (blank($state['room_number'])) {
                            return null;
                        }
                        $groupHotel = $groupHotels->get($state['group_hotel_id'] ?? 0);

                        // return new HtmlString('<strong>'.($groupHotel?->hotel->fullname ?? '').':</strong> '.$state['room_number']);
                        return $state['room_number'];
                    })
                    ->listWithLineBreaks(),
                Tables\Columns\TextColumn::make('meta.details')
                    ->label('Details')
                    ->wrap(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('generate_rooms')
                    ->color('gray')
                    ->icon('heroicon-o-arrow-path')
                    ->action(function () {
                        /** @var Group $group */
                        $group = $this->getRecord();

                        $group->generateRooms();
                        $group->logEvent('updated_roomlist');
                    })
                    ->requiresConfirmation()
                    ->modalDescription('This will remove all the existing rooms. Are you sure you would like to do this?')
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator'])),
                Tables\Actions\CreateAction::make()
                    ->fillForm([
                        'meta' => [
                            'room_numbers' => $this->getRecord()
                                ->group_hotels()
                                ->orderBy('check_in')
                                ->get()
                                ->map(fn ($groupHotel) => [
                                    'group_hotel_id' => $groupHotel->id,
                                    'room_number' => '',
                                ])
                                ->toArray(),
                        ],
                    ])
                    ->after(function () {
                        $this->getRecord()->logEvent('updated_roomlist');
                    })
                    ->modalWidth('lg')
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator'])),
            ])
            ->actions([
                Tables\Actions\Action::make('edit_pilgrims')
                    ->form([
                        Forms\Components\TextInput::make('name')
                            ->disabled()
                            ->default(fn ($record) => $record->name),
                        Forms\Components\CheckboxList::make('mutawifs')
                            ->hiddenLabel()
                            ->options(function (Room $record) {
                                $group = $record->group;
                                $mutawifs = [];

                                if ($group->mutawif_id && $group->mutawif) {
                                    $mutawifs[1] = 'Mutawif 1 - ' . $group->mutawif->name;
                                }
                                if ($group->mutawif_2_id && $group->mutawif_2) {
                                    $mutawifs[2] = 'Mutawif 2 - ' . $group->mutawif_2->name;
                                }
                                if ($group->mutawif_3_id && $group->mutawif_3) {
                                    $mutawifs[3] = 'Mutawif 3 - ' . $group->mutawif_3->name;
                                }

                                return $mutawifs;
                            })
                            ->default(fn ($record) => $record->mutawifs),
                        Forms\Components\CheckboxList::make('pilgrim_ids')
                            ->label('Pilgrims')
                            ->options(function (Room $record) {
                                $groupPilgrims = GroupPilgrim::query()
                                    ->with(['pilgrim'])
                                    ->where('group_id', $record->group_id)
                                    ->where(fn ($query) => $query->whereNull('room_id')->orWhere('room_id', $record->id))
                                    ->get();
                                $options = [];
                                foreach ($groupPilgrims as $groupPilgrim) {
                                    $gender = $groupPilgrim->pilgrim->gender->getShortLabel();
                                    $options[$groupPilgrim->pilgrim->id] = new HtmlString("{$groupPilgrim->pilgrim->fullname} ({$gender})" . ($groupPilgrim->is_tour_leader ? ' (TL)' : ''));
                                }

                                return $options;
                            })
                            ->default(fn ($record) => collect($record->pilgrims->pluck('id'))->toArray())
                            ->searchable()
                            ->helperText(fn ($record, $state) => count($state) > $record->capacity->value ? new HtmlString('<span class="text-warning-600">Jumlah jamaah melebihi kapasitas kamar (' . $record->capacity->value . ' orang).</span>') : null)
                            ->live()
                            ->columns(2),
                    ])
                    ->action(function (array $data, Room $record) {
                        DB::transaction(function () use ($record, $data) {
                            $record->update(['mutawifs' => $data['mutawifs']]);
                            GroupPilgrim::query()
                                ->where('room_id', $record->id)
                                ->whereNotIn('pilgrim_id', $data['pilgrim_ids'])
                                ->update(['room_id' => null]);
                            GroupPilgrim::query()
                                ->where('group_id', $record->group_id)
                                ->whereIn('pilgrim_id', $data['pilgrim_ids'])
                                ->update(['room_id' => $record->id]);
                        });
                        $this->getRecord()->logEvent('updated_roomlist');
                    })
                    ->modalSubmitActionLabel('Save')
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator'])),
                Tables\Actions\EditAction::make()
                    ->modalWidth('lg')
                    ->mutateRecordDataUsing(function (Room $record, $data) {
                        $room_numbers = collect($record->meta['room_numbers'] ?? [])
                            ->filter(fn ($item) => filled($item['group_hotel_id'] ?? null))
                            ->keyBy('group_hotel_id')
                            ->map(fn ($room_number) => $room_number['room_number'])
                            ->toArray();
                        $groupHotels = $this->getRecord()->group_hotels;

                        return [
                            ...$data,
                            'meta' => [
                                ...($data['meta'] ?? []),
                                'room_numbers' => $groupHotels->map(fn ($groupHotel) => [
                                    'group_hotel_id' => $groupHotel->id,
                                    'room_number' => $room_numbers[$groupHotel->id] ?? '',
                                ])->toArray(),
                            ],
                        ];
                    })
                    ->after(function () {
                        $this->getRecord()->logEvent('updated_roomlist');
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(auth()->user()->hasRole(['Admin', 'Operator', 'Admin Operator']))
                    ->after(function () {
                        $this->getRecord()->logEvent('updated_roomlist');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->after(function () {
                            $this->getRecord()->logEvent('updated_roomlist');
                        }),
                ]),
                Tables\Actions\BulkAction::make('edit_hotels')
                    ->form([
                        Forms\Components\CheckboxList::make('group_hotel_ids')
                            ->label('Hotels')
                            ->options(
                                $this->getRecord()
                                    ->group_hotels()
                                    ->orderBy('check_in')
                                    ->get()
                                    ->mapWithKeys(fn ($gh) => [
                                        $gh->id => sprintf(
                                            '%s (%s - %s)',
                                            $gh->hotel->fullname,
                                            $gh->check_in?->format('j M Y') ?? 'n/a',
                                            $gh->check_out?->format('j M Y') ?? 'n/a'
                                        ),
                                    ])
                            )
                            ->required(),
                    ])
                    ->action(function ($records, $data) {
                        $records->each(function ($record) use ($data) {
                            $meta = $record->meta ?? [];
                            $meta['group_hotel_ids'] = $data['group_hotel_ids'];
                            $record->update(compact('meta'));
                        });
                        $this->getRecord()->logEvent('updated_roomlist');
                    })
                    ->modalWidth('sm')
                    ->modalSubmitActionLabel('Save')
                    ->deselectRecordsAfterCompletion(),
            ]);
    }
}
