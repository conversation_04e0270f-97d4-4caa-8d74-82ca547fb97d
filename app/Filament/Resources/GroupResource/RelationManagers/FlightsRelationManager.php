<?php

namespace App\Filament\Resources\GroupResource\RelationManagers;

use App\Models\Airport;
use App\Settings\AirportHandlingSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class FlightsRelationManager extends RelationManager
{
    protected static string $relationship = 'flights';

    protected static ?string $recordTitleAttribute = 'flight_number';

    protected static bool $isLazy = false;

    public function getRelationship(): Relation | Builder
    {
        return $this->getOwnerRecord()->{static::getRelationshipName()}()->with(['airline']);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\ToggleButtons::make('type')
                    ->options([
                        'arrival' => 'Arrival',
                        'departure' => 'Departure',
                    ])
                    ->grouped()
                    ->live()
                    ->required(),
                Forms\Components\Select::make('airline_id')
                    ->label('Airline')
                    ->relationship('airline', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\TextInput::make('pax')
                    ->label('Pax')
                    ->numeric()
                    ->required(),
                Forms\Components\Select::make('handler_id')
                    ->label(fn ($get) => str($get('type'))->headline() . ' Handler')
                    ->relationship('handler', 'company_name')
                    ->searchable()
                    ->preload(),
                Forms\Components\ToggleButtons::make('flight_type')
                    ->hiddenLabel()
                    ->options([
                        'direct' => 'Direct Flight',
                        'transit' => 'Transit',
                    ])
                    ->default('direct')
                    ->inline()
                    ->live()
                    ->columnSpanFull(),
                Forms\Components\Group::make()
                    ->visible(fn ($get) => $get('flight_type') === 'direct')
                    ->columnSpanFull()
                    ->columns()
                    ->schema([
                        Forms\Components\TextInput::make('flight_number')
                            ->label('Flight Number')
                            ->columnSpanFull()
                            ->required(),
                        Forms\Components\Select::make('from')
                            ->options(Airport::getCodeOptions())
                            ->required(),
                        Forms\Components\Select::make('to')
                            ->options(Airport::getCodeOptions())
                            ->required(),
                        Forms\Components\DateTimePicker::make('date_etd')
                            ->label('ETD')
                            ->required(),
                        Forms\Components\DateTimePicker::make('date_eta')
                            ->label('ETA')
                            ->required(),
                    ]),
                Forms\Components\Group::make()
                    ->visible(fn ($get) => $get('flight_type') === 'transit')
                    ->columnSpanFull()
                    ->columns()
                    ->schema([
                        Forms\Components\Fieldset::make('Flight 1')
                            ->schema([
                                Forms\Components\TextInput::make('flight_number')
                                    ->label('Flight Number')
                                    ->columnSpanFull()
                                    ->required(),
                                Forms\Components\Select::make('from')
                                    ->options(Airport::getCodeOptions())
                                    ->required(),
                                Forms\Components\Select::make('via')
                                    ->options(Airport::getCodeOptions())
                                    ->label('To'),
                                Forms\Components\DateTimePicker::make('date_etd')
                                    ->label('ETD')
                                    ->required(),
                                Forms\Components\DateTimePicker::make('via_eta')
                                    ->label('ETA'),
                            ]),
                        Forms\Components\Fieldset::make('Flight 2')
                            ->schema([
                                Forms\Components\TextInput::make('via_number')
                                    ->columnSpanFull()
                                    ->label('Flight Number'),
                                Forms\Components\TextInput::make('via')
                                    ->disabled()
                                    ->label('From'),
                                Forms\Components\Select::make('to')
                                    ->options(Airport::getCodeOptions())
                                    ->required(),
                                Forms\Components\DateTimePicker::make('via_etd')
                                    ->label('ETD'),
                                Forms\Components\DateTimePicker::make('date_eta')
                                    ->label('ETA')
                                    ->required(),
                            ]),
                    ]),
                Forms\Components\Select::make('meta.mealbox')
                    ->label('Mealbox')
                    ->options(fn () => collect(app(AirportHandlingSettings::class)->meal_prices)
                        ->mapWithKeys(fn ($v, $k) => [$k => $k])
                        ->toArray())
                    ->multiple()
                    ->createOptionForm([
                        Forms\Components\TextInput::make('meal')
                            ->required(),
                    ])
                    ->createOptionUsing(function ($data) {
                        $settings = app(AirportHandlingSettings::class);
                        $settings->meal_prices += [
                            $data['meal'] => 0,
                        ];
                        $settings->save();

                        return $data['meal'];
                    })
                    ->createOptionAction(fn ($action) => $action->modalWidth('sm'))
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('flight')
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(fn ($state) => $state == 'arrival' ? 'Arrival' : 'Departure')
                    ->badge()
                    ->colors([
                        'success',
                        'primary' => 'arrival',
                    ]),
                Tables\Columns\TextColumn::make('flight_number')
                    ->label('Flight')
                    ->description(fn ($record) => $record->airline->name),
                Tables\Columns\TextColumn::make('pax'),
                Tables\Columns\TextColumn::make('route')
                    ->getStateUsing(fn ($record) => $record->from . ($record->via ? ' ✈ ' . $record->via : '') . ' ✈ ' . $record->to),
                Tables\Columns\TextColumn::make('date_etd')
                    ->label('Date')
                    ->date(),
                Tables\Columns\TextColumn::make('etd')
                    ->label('ETD'),
                Tables\Columns\TextColumn::make('eta')
                    ->label('ETA'),
                Tables\Columns\TextColumn::make('handler.company_name')
                    ->label('Handler'),
                Tables\Columns\TextColumn::make('mealbox'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->modalWidth('xl')
                    ->mutateFormDataUsing(function ($data) {
                        unset($data['flight_type']);

                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(fn ($data, $record) => [
                        ...$data,
                        'flight_type' => $record->via ? 'transit' : 'direct',
                        'via_2' => $record->via,
                    ])
                    ->mutateFormDataUsing(function ($data) {
                        unset($data['flight_type']);
                        unset($data['via_2']);

                        return $data;
                    })
                    ->modalWidth('xl'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('date_etd', 'asc');
    }
}
