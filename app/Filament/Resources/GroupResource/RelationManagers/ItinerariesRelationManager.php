<?php

namespace App\Filament\Resources\GroupResource\RelationManagers;

use App\Models\Itinerary;
use App\Settings\SnackSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ItinerariesRelationManager extends RelationManager
{
    protected static string $relationship = 'itineraries';

    protected static ?string $title = 'Itineraries';

    protected static ?string $modelLabel = 'item';

    protected static ?string $recordTitleAttribute = 'location';

    protected static bool $isLazy = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\DateTimePicker::make('date')
                    ->required(),
                Forms\Components\TextInput::make('location')
                    ->label('Perjalanan')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('description')
                    ->label('Keterangan Perjalanan')
                    ->columnSpanFull()
                    ->required()
                    ->maxLength(255),
                Forms\Components\Checkbox::make('is_arrival'),
                Forms\Components\Section::make('Snack')
                    ->compact()
                    ->columns(2)
                    ->schema([
                        Forms\Components\Checkbox::make('has_snack')
                            ->live()
                            ->columnSpanFull(),
                        Forms\Components\Select::make('city')
                            ->options([
                                'Makkah' => 'Makkah',
                                'Madinah' => 'Madinah',
                            ])
                            ->required()
                            ->visible(fn ($get) => $get('has_snack')),
                        Forms\Components\ToggleButtons::make('snack_details')
                            ->visible(fn ($get) => $get('has_snack'))
                            ->grouped()
                            ->options(function () {
                                $settings = app(SnackSettings::class);

                                return array_combine(array_keys($settings->prices), array_keys($settings->prices));
                            }),
                        Forms\Components\Select::make('snack_handler_id')
                            ->visible(fn ($get) => $get('has_snack'))
                            ->label('Snack Handler')
                            ->relationship('snack_handler', 'company_name')
                            ->searchable()
                            ->preload(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\CheckboxColumn::make('is_arrival')
                    ->label('Arrival'),
                Tables\Columns\CheckboxColumn::make('has_snack')
                    ->label('Snack'),
                Tables\Columns\TextColumn::make('date')
                    ->dateTime(),
                Tables\Columns\TextColumn::make('details')
                    ->getStateUsing(fn ($record) => $record->location)
                    ->description(fn ($record) => $record->description),
                Tables\Columns\TextColumn::make('snack_details')
                    ->label('Snack Details')
                    ->default('')
                    ->description(fn ($record) => $record->city, 'above'),
                Tables\Columns\TextColumn::make('snack_handler.company_name')
                    ->label('Snack Handler'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edit item'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([])
            ->reorderable('sort')
            ->defaultSort('sort');
    }

    public function reorderTable(array $order): void
    {
        Itinerary::setNewOrder($order);
    }
}
