<?php

namespace App\Filament\Resources\GroupResource\RelationManagers;

use App\Enums\HotelRoomType;
use App\Models\Group;
use App\Models\GroupHotel;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Blade;

/**
 * @property Group $ownerRecord
 */
class GroupHotelsRelationManager extends RelationManager
{
    protected static string $relationship = 'group_hotels';

    protected static ?string $title = 'Hotels';

    protected static bool $isLazy = false;

    public function getRelationship(): Relation | Builder
    {
        return parent::getRelationship()->with(['hotel']);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(GroupHotel::getFormSchema(false))
            ->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modelLabel('hotel')
            ->recordTitle(fn ($record) => $record->hotel?->fullname ?? 'Hotel')
            ->reorderable('sort')
            ->defaultSort('sort')
            ->columns([
                BadgeableColumn::make('hotel.name')
                    ->description(fn ($record) => $record->hotel?->city ?? null, 'above')
                    ->suffixBadges([
                        Badge::make('type')
                            ->label(fn ($record) => HotelRoomType::tryFrom($record->meta['room_type'] ?? '')?->getShortLabel())
                            ->visible(fn ($record) => $record->meta['room_type'] ?? null),
                        Badge::make('meal')
                            ->label(fn ($record) => $record->meta['meal'] ?? null)
                            ->visible(fn ($record) => $record->meta['meal'] ?? null),
                    ]),
                Tables\Columns\TextColumn::make('check_in')
                    ->date(),
                Tables\Columns\TextColumn::make('check_out')
                    ->date(),
                Tables\Columns\TextColumn::make('composition')
                    ->badge()
                    ->getStateUsing(fn ($record) => collect([
                        $record->room_single_count ? $record->room_single_count . ' single' : null,
                        $record->room_double_count ? $record->room_double_count . ' double' : null,
                        $record->room_triple_count ? $record->room_triple_count . ' triple' : null,
                        $record->room_quad_count ? $record->room_quad_count . ' quad' : null,
                        $record->room_quint_count ? $record->room_quint_count . ' quint' : null,
                    ])->filter()->toArray()),
                Tables\Columns\IconColumn::make('is_confirmed')
                    ->boolean()
                    ->label('Confirmation')
                    ->view('filament.tables.columns.hotel-booking-hotel-confirmation')
                    ->disabledClick(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('attach')
                    ->modalHeading('Attach hotel')
                    ->modalSubmitActionLabel('Attach')
                    ->modalWidth('lg')
                    ->successNotificationTitle('Attached')
                    ->color('gray')
                    ->form([
                        Forms\Components\Select::make('group_hotel_id')
                            ->hiddenLabel()
                            ->options(
                                GroupHotel::query()
                                    ->with(['hotel'])
                                    ->whereNull('group_id')
                                    ->get()
                                    ->mapWithKeys(function ($item) {
                                        return [$item->id => Blade::render('components.group-hotel-item', ['record' => $item])];
                                    })
                            )
                            ->searchable()
                            ->required()
                            ->allowHtml(),
                    ])
                    ->action(function ($data, $action) {
                        GroupHotel::query()
                            ->where('id', $data['group_hotel_id'])
                            ->update(['group_id' => $this->ownerRecord->id]);

                        $action->success();
                    }),
                Tables\Actions\CreateAction::make()
                    ->modalWidth('lg'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('lg'),
                Tables\Actions\DetachAction::make()
                    ->using(fn ($record) => $record->update(['group_id' => null])),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }
}
