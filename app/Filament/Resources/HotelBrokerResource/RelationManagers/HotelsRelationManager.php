<?php

namespace App\Filament\Resources\HotelBrokerResource\RelationManagers;

use App\Exports\GroupHotelExport;
use App\Filament\Resources\GroupResource;
use App\Models\Group;
use App\Models\GroupHotel;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class HotelsRelationManager extends RelationManager
{
    protected static string $relationship = 'group_hotels';

    protected static ?string $title = 'Hotels';

    protected static ?string $recordTitleAttribute = 'group_id';

    protected static bool $isLazy = false;

    protected function canCreate(): bool
    {
        return false;
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public function getRelationship(): Relation | Builder
    {
        return parent::getRelationship()
            ->with(['hotel', 'group.customer', 'broker'])
            ->currentPeriod();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(GroupHotel::getFormSchema())
            ->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                BadgeableColumn::make('hotel.name')
                    ->description(fn ($record) => $record->hotel->city ?? null)
                    ->suffixBadges([
                        Badge::make('single')
                            ->label(fn ($record) => $record->room_single_count . ' single')
                            ->visible(fn ($record) => $record->room_single_count),
                        Badge::make('double')
                            ->label(fn ($record) => $record->room_double_count . ' double')
                            ->visible(fn ($record) => $record->room_double_count),
                        Badge::make('triple')
                            ->label(fn ($record) => $record->room_triple_count . ' triple')
                            ->visible(fn ($record) => $record->room_triple_count),
                        Badge::make('quad')
                            ->label(fn ($record) => $record->room_quad_count . ' quad')
                            ->visible(fn ($record) => $record->room_quad_count),
                        Badge::make('quint')
                            ->label(fn ($record) => $record->room_quint_count . ' quint')
                            ->visible(fn ($record) => $record->room_quint_count),
                    ]),
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn (GroupHotel $record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer?->name ?? null)
                    ->description(fn ($record) => $record->group?->name ?? null),
                Tables\Columns\TextColumn::make('check_in')
                    ->label('Check In')
                    ->date()
                    ->sortable()
                    ->color(fn ($record) => $record->check_in->isPast() ? 'success' : null)
                    ->iconPosition(IconPosition::After)
                    ->icon(fn ($record) => $record->check_in->isPast() ? 'heroicon-o-check-circle' : null),
                Tables\Columns\TextColumn::make('check_out')
                    ->label('Check Out')
                    ->date()
                    ->sortable()
                    ->color(fn ($record) => $record->check_out->isPast() ? 'success' : null)
                    ->iconPosition(IconPosition::After)
                    ->icon(fn ($record) => $record->check_out->isPast() ? 'heroicon-o-check-circle' : null),
                Tables\Columns\IconColumn::make('is_confirmed')
                    ->boolean()
                    ->label('Confirmation')
                    ->view('filament.tables.columns.hotel-booking-hotel-confirmation')
                    ->disabledClick(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('xlsx')
                        ->label('XLSX')
                        ->icon('phosphor-microsoft-excel-logo')
                        ->action(function () {
                            return (new GroupHotelExport($this->getFilteredTableQuery(), ['broker_id' => ['value' => $this->ownerRecord->id]]))->download();
                        }),
                    Tables\Actions\Action::make('csv')
                        ->label('CSV')
                        ->icon('phosphor-file-csv')
                        ->action(function () {
                            return (new GroupHotelExport($this->getFilteredTableQuery(), ['broker_id' => ['value' => $this->ownerRecord->id]], 'csv'))->download();
                        }),
                ])
                    ->label('Export')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->button(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('lg')
                    ->visible(fn () => auth()->user()->can('update', new Group)),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('check_in', 'desc');
    }
}
