<?php

namespace App\Filament\Resources\HotelBrokerResource\Pages;

use App\Filament\Resources\HotelBrokerResource;
use App\Filament\Resources\HotelBrokerResource\Widgets;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\ViewRecord;

class ViewHotelBroker extends ViewRecord
{
    protected static string $resource = HotelBrokerResource::class;

    protected function getFormSchema(): array
    {
        return [
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\TextInput::make('company_name')
                        ->required()
                        ->columnSpan(2),
                    Forms\Components\TextInput::make('contact_name'),
                    Forms\Components\TextInput::make('contact_phone'),
                ])
                ->columns(2),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
