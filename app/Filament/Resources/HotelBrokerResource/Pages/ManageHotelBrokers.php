<?php

namespace App\Filament\Resources\HotelBrokerResource\Pages;

use App\Filament\Resources\HotelBrokerResource;
use App\Models\Vendors\HotelBroker;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ManageHotelBrokers extends ManageRecords
{
    protected static string $resource = HotelBrokerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('lg'),
        ];
    }

    protected function getTableQuery(): Builder
    {
        return HotelBroker::query()
            ->leftJoin('group_cashes', 'group_cashes.hotel_broker_id', 'vendors.id')
            ->select([
                'vendors.id',
                'vendors.company_name',
                'vendors.contact_name',
                'vendors.contact_phone',
                DB::raw('MAX(group_cashes.cashed_at) AS last_payment_at'),
            ])
            ->groupBy([
                'vendors.id',
                'vendors.company_name',
                'vendors.contact_name',
                'vendors.contact_phone',
            ])
            ->orderByDesc('last_payment_at')
            ->orderBy('company_name');
    }
}
