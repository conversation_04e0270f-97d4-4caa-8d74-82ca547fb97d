<?php

namespace App\Filament\Resources\AirlineResource\RelationManagers;

use App\Models\Airline;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables;

class FlightsRelationManager extends RelationManager
{
    protected static string $relationship = 'flights';

    protected static ?string $recordTitleAttribute = 'number';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('airline_id')
                            ->label('Airline')
                            ->relationship('airline', 'name')
                            ->reactive(),
                        Forms\Components\TextInput::make('number')
                            ->label('Flight Number')
                            ->datalist(function ($get) {
                                $airline_id = $get('airline_id');
                                if ($airline_id) {
                                    return Airline::find($airline_id)->flights()->pluck('number')->unique();
                                }

                                return null;
                            })
                            ->required(),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('from')
                            ->label('From'),
                        Forms\Components\TextInput::make('to')
                            ->label('To'),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DateTimePicker::make('date_etd')
                            ->label('ETD')
                            ->withoutSeconds(),
                        Forms\Components\DateTimePicker::make('date_eta')
                            ->label('ETA')
                            ->withoutSeconds(),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('number')
                    ->label('Flight Number'),
                Tables\Columns\TextColumn::make('from'),
                Tables\Columns\TextColumn::make('to'),
                Tables\Columns\TextColumn::make('etd')
                    ->label('ETD'),
                Tables\Columns\TextColumn::make('eta')
                    ->label('ETA'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('lg')
                    ->mutateFormDataUsing(function ($data) {
                        $data['date'] = $data['date_etd'];
                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->modalWidth('lg')
                    ->mutateFormDataUsing(function ($data) {
                        $data['date'] = $data['date_etd'];
                        return $data;
                    }),
            ])
            ->defaultSort('date', 'desc');
    }
}
