<?php

namespace App\Filament\Resources\HotelResource\Pages;

use App\Filament\Resources\HotelResource;
use Filament\Pages;
use Filament\Resources\Pages\ManageRecords;

class ManageHotels extends ManageRecords
{
    protected static string $resource = HotelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Pages\Actions\CreateAction::make()
                ->mutateFormDataUsing(function ($data) {
                    $data['stars'] = $data['stars'] ?? 0;
                    $data['distance'] = $data['distance'] ?? 0;
                    $data['price_quad'] = $data['price_quad'] ?? 0;
                    $data['price_triple'] = $data['price_triple'] ?? 0;
                    $data['price_double'] = $data['price_double'] ?? 0;
                    return $data;
                }),
        ];
    }
}
