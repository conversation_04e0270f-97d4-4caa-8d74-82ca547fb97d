<?php

namespace App\Filament\Resources;

use App\Enums\UserRole;
use APp\Filament\Actions\WhatsAppTableAction;
use App\Filament\Resources\SnackResource\Pages;
use App\Models\Itinerary;
use App\Models\User;
use App\Settings\SnackSettings;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class SnackResource extends Resource
{
    protected static ?string $model = Itinerary::class;

    protected static ?string $modelLabel = 'Snack';

    protected static ?string $pluralModelLabel = 'Snacks';

    protected static ?string $navigationGroup = 'Schedules';

    protected static ?int $navigationSort = 10;

    protected static ?string $slug = 'snacks';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Finance', 'Admin Operator', 'Operator', 'Snack Handler']);
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole(['Admin', 'Finance', 'Admin Operator']);
    }

    public static function getEloquentQuery(): Builder
    {
        /** @var User */
        $user = auth()->user();

        return Itinerary::query()
            ->with(['snack_handler', 'group.customer', 'group.hotels', 'group.mutawif', 'group.mutawif_2', 'group.mutawif_3'])
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->where('has_snack', true)
            ->when($user->hasExactRoles(UserRole::SnackHandler->value), function ($query) use ($user) {
                $snackVendorIds = $user->vendors()->pluck('id');
                $query->whereIn('snack_handler_id', $snackVendorIds);
            })
            ->orderBy('date');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('city')
                    ->options([
                        'Makkah' => 'Makkah',
                        'Madinah' => 'Madinah',
                    ])
                    ->required(),
                Forms\Components\ToggleButtons::make('snack_details')
                    ->label('Snack Type')
                    ->grouped()
                    ->options(function () {
                        $settings = app(SnackSettings::class);

                        return array_combine(array_keys($settings->prices), array_keys($settings->prices));
                    }),
                Forms\Components\Select::make('snack_handler_id')
                    ->label('Snack Handler')
                    ->relationship('snack_handler', 'company_name')
                    ->searchable()
                    ->preload(),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date')
                    ->badge()
                    ->color('primary')
                    ->date(),
                Tables\Columns\TextColumn::make('group.total_pax')
                    ->wrapHeader()
                    ->alignCenter()
                    ->label('Total Pax'),
                Tables\Columns\TextColumn::make('details')
                    ->getStateUsing(fn ($record) => $record->location)
                    ->description(fn ($record) => $record->description)
                    ->wrap(),
                Tables\Columns\TextColumn::make('snack_details')
                    ->label('Snack Details')
                    ->wrapHeader()
                    ->default('')
                    ->description(fn ($record) => $record->city, 'above'),
                Tables\Columns\TextColumn::make('hotels')
                    ->badge()
                    ->label('Hotels')
                    ->listWithLineBreaks()
                    ->getStateUsing(
                        fn ($record) => $record->group->hotels->map(fn ($h) => $h->fullname)->toArray()
                    ),
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->getStateUsing(fn ($record) => $record->group->customer?->name ?? '')
                    ->description(fn ($record) => $record->group->name)
                    ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group]))
                    ->openUrlInNewTab(),
                Tables\Columns\TextColumn::make('snack_handler.company_name')
                    ->label('Handler'),
            ])
            ->filters([
                Tables\Filters\Filter::make('month')
                    ->form([
                        Forms\Components\TextInput::make('month')
                            ->type('month')
                            ->default(Carbon::now()->format('Y-m')),
                    ])
                    ->query(function (Builder $query, $data) {
                        return $query
                            ->when(
                                $data['month'],
                                fn (Builder $query, $month) => $query->where('date', 'like', "{$month}%")
                            );
                    })
                    ->indicateUsing(function ($data) {
                        if (! $data['month']) {
                            return null;
                        }

                        return 'Month: ' . Carbon::parse($data['month'] . '-01')->format('F Y');
                    }),
                Tables\Filters\SelectFilter::make('city')
                    ->options(Itinerary::query()
                        ->whereNotNull('city')
                        ->distinct()
                        ->pluck('city', 'city')),
                Tables\Filters\SelectFilter::make('snack_handler_id')
                    ->label('Handler')
                    ->relationship('snack_handler', 'company_name')
                    ->searchable()
                    ->preload()
                    ->visible(fn () => auth()->user()->hasRole(['Admin', 'Operator'])),
            ])
            ->actions([
                WhatsAppTableAction::make('mutawif')
                    ->phone(fn ($record) => $record->group->mutawif?->phone ?? '')
                    ->tooltip(fn ($record) => $record->group->mutawif?->name ?? null)
                    ->visible(fn ($record) => (bool) $record->group->mutawif),
                WhatsAppTableAction::make('mutawif_2')
                    ->phone(fn ($record) => $record->group->mutawif_2?->phone ?? '')
                    ->tooltip(fn ($record) => $record->group->mutawif_2?->name ?? null)
                    ->visible(fn ($record) => (bool) $record->group->mutawif_2),
                WhatsAppTableAction::make('mutawif_3')
                    ->phone(fn ($record) => $record->group->mutawif_3?->phone ?? '')
                    ->tooltip(fn ($record) => $record->group->mutawif_3?->name ?? null)
                    ->visible(fn ($record) => (bool) $record->group->mutawif_3),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->modalWidth('sm'),
                ]),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSnacks::route('/'),
        ];
    }
}
