<?php

namespace App\Filament\Resources\CustomerResource\RelationManagers;

use App\Filament\Resources\GroupResource;
use App\Models\Group;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Umrahservice\Groups\Enums\GroupProgress;
use Umrahservice\Groups\Enums\GroupStatus;

class GroupsRelationManager extends RelationManager
{
    protected static string $relationship = 'groups';

    protected static ?string $recordTitleAttribute = 'name';

    public function getRelationship(): Relation|Builder
    {
        return $this->getOwnerRecord()
            ->{static::getRelationshipName()}()
            ->currentPeriod();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordUrl(fn ($record) => GroupResource::getUrl('view', ['record' => $record]))
            ->columns([
                // Tables\Columns\TextColumn::make('invoice')
                //     ->label('Invoice')
                //     ->money('SAR')
                //     ->getStateUsing(fn ($record) => $record->invoice ? $record->invoice->total * $record->invoice->exchange_rate : null)
                //     ->description(fn ($record) => $record->invoice?->invoice_number ?? null, 'above'),
                BadgeableColumn::make('name')
                    ->label('Group')
                    ->formatStateUsing(fn ($state, $record) => $state ?? "Group #{$record->id}")
                    ->searchable()
                    ->suffixBadges([
                        Badge::make('pax')
                            ->label(fn ($record) => $record->total_pax . ' pax'),
                        Badge::make('hotel_request')
                            ->color('warning')
                            ->visible(fn ($record) => $record->status == GroupStatus::Pending),
                        Badge::make('cancelled')
                            ->color('danger')
                            ->visible(fn ($record) => $record->status == GroupStatus::Cancelled),
                        Badge::make('ongoing')
                            ->color('warning')
                            ->visible(fn ($record) => $record->progress == GroupProgress::Ongoing),
                        Badge::make('finished')
                            ->color('success')
                            ->visible(fn ($record) => $record->progress == GroupProgress::Finished),
                    ]),
                Tables\Columns\TextColumn::make('services')
                    ->badge()
                    ->label('Services')
                    ->getStateUsing(fn ($record) => $record->getServices())
                    ->color('primary')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('arrival_date')
                    ->label('Arrival Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('departure_date')
                    ->label('Departure Date')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('services')
                    ->options(Group::GROUP_SERVICES)
                    ->multiple()
                    ->query(function ($query, $data) {
                        if (filled($data['values'])) {
                            $query->where(function ($query) use ($data) {
                                collect($data['values'])
                                    ->each(function ($value) use ($query) {
                                        $query
                                            ->where('services', 'like', '%' . $value . '%');
                                    });
                            });
                        }

                        return $query;
                    }),
                Tables\Filters\SelectFilter::make('status')
                    ->options(GroupStatus::class),
                Tables\Filters\SelectFilter::make('progress')
                    ->options(GroupProgress::class),
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
                Tables\Actions\Action::make('new_group')
                    ->button()
                    ->url(fn ($livewire) => GroupResource::getUrl('create', ['customer_id' => $livewire->ownerRecord->id]))
                    ->visible(fn () => auth()->user()->can('create', Group::class)),
            ])
            ->actions([
            ])
            ->bulkActions([
            ])
            ->defaultSort('arrival_date', 'desc');
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
