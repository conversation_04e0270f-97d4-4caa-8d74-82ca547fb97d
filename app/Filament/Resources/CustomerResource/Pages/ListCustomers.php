<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Exports\CustomerExport;
use App\Filament\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListCustomers extends ListRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new CustomerExport)->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
            Actions\Action::make('finance')
                ->color('gray')
                ->icon('heroicon-o-banknotes')
                ->url(\App\Filament\Finance\Resources\CustomerResource::getUrl('index'))
                ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
            Actions\CreateAction::make(),
        ];
    }

    protected function getTableQuery(): Builder
    {
        return static::getResource()::getEloquentQuery()
            ->leftJoin('groups', 'customers.id', '=', 'groups.customer_id')
            ->select([
                'customers.id',
                'customers.name',
                'customers.owner_name',
                'customers.email',
                'customers.phone',
                'customers.logo',
                'customers.agent_id',
                'customers.user_id',
                DB::raw('MAX(groups.arrival_date) AS latest_arrival'),
            ])
            ->groupBy([
                'customers.id',
                'customers.name',
                'customers.owner_name',
                'customers.email',
                'customers.phone',
                'customers.logo',
                'customers.agent_id',
                'customers.user_id',
            ])
            ->withCasts([
                'latest_arrival' => 'datetime',
            ])
            ->orderByDesc(DB::raw('MAX(groups.arrival_date)'))
            ->orderBy('customers.name');
    }
}
