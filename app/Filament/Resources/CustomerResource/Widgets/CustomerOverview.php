<?php

namespace App\Filament\Resources\CustomerResource\Widgets;

use App\Models\Customer;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CustomerOverview extends BaseWidget
{
    public ?Customer $record = null;

    protected function getCards(): array
    {
        return [
            Stat::make('Total groups', $this->getTotalGroups()),
            Stat::make('Total pax', $this->getTotalPax()),
        ];
    }

    private function getTotalGroups()
    {
        return $this->record->groups()
            ->currentPeriod()
            ->confirmed()
            ->count();
    }

    private function getTotalPax()
    {
        return $this->record->groups()
            ->currentPeriod()
            ->confirmed()
            ->sum('total_pax');
    }
}
