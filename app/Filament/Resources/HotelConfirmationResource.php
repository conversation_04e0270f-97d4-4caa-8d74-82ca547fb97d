<?php

namespace App\Filament\Resources;

use App\Actions\Group\BulkDownloadHotelConfirmations;
use App\Filament\Resources\HotelConfirmationResource\Pages;
use App\Models\Group;
use App\Models\GroupHotel;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Carbon\Carbon;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Umrahservice\Groups\Enums\GroupStatus;

class HotelConfirmationResource extends Resource
{
    protected static ?string $modelLabel = 'hotel confirmation';

    protected static ?string $model = GroupHotel::class;

    protected static ?string $navigationGroup = 'Hotels';

    protected static ?int $navigationSort = 2;

    protected static ?string $slug = 'hotel-confirmations';

    protected static ?string $recordTitleAttribute = 'id';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->can('update', new Group);
    }

    public static function getEloquentQuery(): Builder
    {
        return static::getModel()::query()
            ->with(['group', 'hotel'])
            ->currentPeriod();
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['confirmation_number'];
    }

    public static function getGlobalSearchResultTitle(Model $record): string | Htmlable
    {
        return $record->hotel->fullname;
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        /** @var GroupHotel $record */
        return [
            'Confirmation number' => $record->confirmation_number,
        ];
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema(GroupHotel::getFormSchema())
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->with(['group.customer'])
                ->select(['*', DB::raw('IF(check_in <= NOW(), 1, 0) AS is_past')])
                ->orderBy('is_past')
                ->orderBy('check_in')
            )
            ->columns([
                BadgeableColumn::make('group')
                    ->label('Group')
                    ->url(fn ($record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer->name ?? null)
                    ->description(fn ($record) => $record->group?->name ?? null)
                    ->suffixBadges([
                        Badge::make('pending')
                            ->color('warning')
                            ->visible(fn ($record) => $record->group?->status == GroupStatus::Pending),
                        Badge::make('cancelled')
                            ->color('danger')
                            ->visible(fn ($record) => $record->group?->status == GroupStatus::Cancelled),
                    ]),
                Tables\Columns\TextColumn::make('hotel.name')
                    ->description(fn ($record) => $record->hotel->city ?? null, 'above'),
                Tables\Columns\TextColumn::make('composition')
                    ->badge()
                    ->getStateUsing(fn ($record) => collect([
                        $record->room_single_count ? $record->room_single_count . ' single' : null,
                        $record->room_double_count ? $record->room_double_count . ' double' : null,
                        $record->room_triple_count ? $record->room_triple_count . ' triple' : null,
                        $record->room_quad_count ? $record->room_quad_count . ' quad' : null,
                        $record->room_quint_count ? $record->room_quint_count . ' quint' : null,
                    ])->filter()->toArray()),
                Tables\Columns\TextColumn::make('check_in')
                    ->date(),
                Tables\Columns\TextColumn::make('check_out')
                    ->date(),
                Tables\Columns\TextColumn::make('broker.company_name')
                    ->default('-')
                    ->description(fn ($record) => $record->broker?->contact_details ?? null),
                Tables\Columns\IconColumn::make('is_confirmed')
                    ->boolean()
                    ->label('Confirmation')
                    ->view('filament.tables.columns.hotel-booking-hotel-confirmation')
                    ->disabledClick(),
            ])
            ->filters([
                Tables\Filters\Filter::make('check_in')
                    ->form([
                        DateRangePicker::make('date_range')
                            ->label('Check In'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['date_range'],
                                function (Builder $query, $date_range): Builder {
                                    $dates = explode(' - ', $date_range);

                                    if (count($dates) == 2) {
                                        return $query
                                            ->whereBetween('check_in', [
                                                Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                                                Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                                            ]);
                                    }

                                    return $query;
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['date_range'] ?? null) {
                            return 'Check In: ' . $data['date_range'];
                        }

                        return null;
                    }),
                Tables\Filters\Filter::make('check_out')
                    ->form([
                        DateRangePicker::make('date_range')
                            ->label('Check Out'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['date_range'],
                                function (Builder $query, $date_range): Builder {
                                    $dates = explode(' - ', $date_range);

                                    if (count($dates) == 2) {
                                        return $query
                                            ->whereBetween('check_out', [
                                                Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                                                Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                                            ]);
                                    }

                                    return $query;
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['date_range'] ?? null) {
                            return 'Check Out: ' . $data['date_range'];
                        }

                        return null;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('lg'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('download_confirmations')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkDownloadHotelConfirmations::run($records));
                    })
                    ->deselectRecordsAfterCompletion(),
            ])
            ->emptyStateActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->filtersLayout(\Filament\Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(['lg' => 2]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageHotelConfirmations::route('/'),
        ];
    }
}
