<?php

namespace App\Filament\Resources\AirportHandlerResource\Pages;

use App\Filament\Resources\AirportHandlerResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageAirportHandlers extends ManageRecords
{
    protected static string $resource = AirportHandlerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
