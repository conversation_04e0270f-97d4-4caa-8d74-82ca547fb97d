<?php

namespace App\Filament\Resources\GroupHotelResource\Pages;

use App\Exports\GroupHotelExport;
use App\Filament\Resources\GroupHotelResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageGroupHotels extends ManageRecords
{
    protected static string $resource = GroupHotelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new GroupHotelExport($this->getFilteredTableQuery(), $this->tableFilters))->download();
                    }),
                Actions\Action::make('csv')
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->action(function () {
                        return (new GroupHotelExport($this->getFilteredTableQuery(), $this->tableFilters, 'csv'))->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
        ];
    }
}
