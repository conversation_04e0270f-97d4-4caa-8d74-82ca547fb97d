<?php

namespace App\Filament\Resources\TransportResource\Pages;

use App\Filament\Resources\TransportResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ManageTransports extends ManageRecords
{
    protected static string $resource = TransportResource::class;

    protected function getTableQuery(): Builder
    {
        return static::getResource()::getEloquentQuery()
            ->withCount(['groups' => fn ($query) => $query->currentPeriod(true)->confirmed()]);
        // ->leftJoin('groups', 'groups.transport_id', '=', 'transports.id')
        // ->select([
        //     'transports.id', 'transports.company_name',
        //     DB::raw('SUM(`groups`.total_pax) AS total_pax'),
        // ])
        // ->groupBy([
        //     'transports.id', 'transports.company_name',
        // ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
