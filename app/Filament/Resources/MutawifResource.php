<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MutawifResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

class MutawifResource extends Resource
{
    protected static ?string $modelLabel = 'mutawif';

    protected static ?string $model = User::class;

    protected static ?string $navigationGroup = 'Users';

    protected static ?int $navigationSort = 5;

    protected static ?string $slug = 'mutawifs';

    public static function can(string $action, ?Model $record = null): bool
    {
        return auth()->user()->hasRole(['Admin']) || auth()->user()->can("mutawifs.{$action}");
    }

    public static function getEloquentQuery(): Builder
    {
        return User::query()->role(['Mutawif']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\TextInput::make('email')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('phone')
                    ->required(),
                Forms\Components\TextInput::make('password')
                    ->required(fn ($context) => $context == 'create')
                    ->password()
                    ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                    ->rule(Password::default()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->getStateUsing(fn ($record) => $record->name)
                    ->description(fn ($record) => $record->email)
                    ->searchable(['name', 'email'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Impersonate::make(),
                Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('name');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageMutawifs::route('/'),
        ];
    }
}
