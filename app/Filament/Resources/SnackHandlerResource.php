<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SnackHandlerResource\Pages;
use App\Models\Vendors\SnackHandler;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class SnackHandlerResource extends Resource
{
    protected static ?string $model = SnackHandler::class;

    protected static ?string $navigationGroup = 'Vendors';

    protected static ?int $navigationSort = 9;

    public static function can(string $action, ?Model $record = null): bool
    {
        return auth()->user()->can("vendors.{$action}");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('company_name')
                    ->required(),
                Forms\Components\TextInput::make('contact_name'),
                Forms\Components\TextInput::make('contact_email')
                    ->email(),
                PhoneInput::make('contact_phone'),
                Forms\Components\Select::make('meta.city')
                    ->options([
                        'Makkah' => 'Makkah',
                        'Madinah' => 'Madinah',
                    ]),
                Forms\Components\Select::make('users')
                    ->relationship('users', 'name')
                    ->searchable()
                    ->preload()
                    ->multiple(),
            ])
            ->columns();
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('company_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('contact_name')
                    ->searchable()
                    ->sortable(),
                PhoneColumn::make('contact_phone')
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->searchable(),
                Tables\Columns\TextColumn::make('meta.city')
                    ->label('City')
                    ->searchable(),
                Tables\Columns\TextColumn::make('users.name')
                    ->badge()
                    ->listWithLineBreaks(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('city')
                    ->options([
                        'Makkah' => 'Makkah',
                        'Madinah' => 'Madinah',
                    ]),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn () => auth('web')->user()->isSuperAdmin()),
                    // Tables\Actions\ActionGroup::make([
                    //     // TODO: edit user
                    // ])
                    //     ->dropdown(false),
                ]),
            ])
            ->defaultSort('company_name');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSnackHandlers::route('/'),
        ];
    }
}
