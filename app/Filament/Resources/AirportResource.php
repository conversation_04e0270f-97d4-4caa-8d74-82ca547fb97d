<?php

namespace App\Filament\Resources;

use App\Enums\VendorType;
use App\Filament\Resources\AirportResource\Pages;
use App\Models\Airport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AirportResource extends Resource
{
    protected static ?string $model = Airport::class;

    protected static ?string $navigationGroup = 'Master Data';

    protected static ?int $navigationSort = 0;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('code')
                    ->label('Code')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('name')
                    ->label('Airport Name')
                    ->required(),
                Forms\Components\TextInput::make('city')
                    ->label('City')
                    ->required(),
                Forms\Components\TextInput::make('country')
                    ->label('Country')
                    ->required(),
                Forms\Components\Select::make('handlers')
                    ->relationship('handlers', 'company_name', fn ($query) => $query->where('vendor_type', VendorType::AirportHandler))
                    ->searchable()
                    ->preload()
                    ->multiple()
                    ->columnSpanFull(),
                Forms\Components\Section::make('Additional info')
                    ->compact()
                    ->collapsed()
                    ->columns()
                    ->schema([
                        Forms\Components\TextInput::make('iata')
                            ->label('IATA'),
                        Forms\Components\TextInput::make('icao')
                            ->label('ICAO'),
                        Forms\Components\TextInput::make('latitude')
                            ->label('Latitude'),
                        Forms\Components\TextInput::make('longitude')
                            ->label('Longitude'),
                        Forms\Components\TextInput::make('timezone')
                            ->label('Timezone'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Airport')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('city')
                    ->label('City')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('country')
                    ->label('Country')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('iata')
                    ->label('IATA')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),
                Tables\Columns\TextColumn::make('icao')
                    ->label('ICAO')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),
                Tables\Columns\TextColumn::make('handlers.company_name')
                    ->badge(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ReplicateAction::make()
                        ->form([
                            Forms\Components\TextInput::make('code')
                                ->required()
                                ->unique(),
                        ])
                        ->modalWidth('sm'),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAirports::route('/'),
        ];
    }
}
