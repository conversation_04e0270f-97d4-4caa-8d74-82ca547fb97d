<?php

namespace App\Filament\Resources\GroupFlightResource\Pages;

use App\Exports\GroupFlightExport;
use App\Filament\Resources\GroupFlightResource;
use App\Models\Airport;
use App\Settings\AirportHandlingSettings;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;

class ManageGroupFlights extends ManageRecords
{
    protected static string $resource = GroupFlightResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('settings')
                ->color('gray')
                ->icon('heroicon-o-cog-6-tooth')
                ->form([
                    TableRepeater::make('handling_rates')
                        ->reorderable(false)
                        ->streamlined()
                        ->headers([
                            Header::make('Airport Code'),
                            Header::make('Arrival'),
                            Header::make('Departure'),
                        ])
                        ->schema([
                            Forms\Components\Select::make('airport_code')
                                ->options(fn () => Airport::query()->orderBy('code')->pluck('code', 'code'))
                                ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                ->searchable()
                                ->required(),
                            Forms\Components\TextInput::make('arrival')
                                ->numeric()
                                ->required(),
                            Forms\Components\TextInput::make('departure')
                                ->numeric()
                                ->required(),
                        ]),
                    Forms\Components\KeyValue::make('meal_prices')
                        ->keyLabel('Meal')
                        ->valueLabel('Price'),
                ])
                ->fillForm(function () {
                    $settings = app(AirportHandlingSettings::class);

                    return $settings->toArray();
                })
                ->action(function ($data) {
                    $settings = app(AirportHandlingSettings::class);
                    $settings->fill($data);
                    $settings->save();

                    Notification::make()
                        ->success()
                        ->title('Settings saved.')
                        ->send();
                })
                ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new GroupFlightExport($this->getFilteredTableQuery(), $this->tableFilters))->download();
                    }),
                Actions\Action::make('csv')
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->action(function () {
                        return (new GroupFlightExport($this->getFilteredTableQuery(), $this->tableFilters, 'csv'))->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
        ];
    }
}
