<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Filament\Resources\CustomerResource\Widgets;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationGroup = 'Groups';

    protected static ?int $navigationSort = 50;

    protected static ?string $recordTitleAttribute = 'name';

    public static function getRouteBaseName(?string $panel = null): string
    {
        return parent::getRouteBaseName('admin');
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'owner_name', 'email', 'phone'];
    }

    public static function getGlobalSearchResultUrl(Model $record): ?string
    {
        return static::getUrl('view', ['record' => $record]);
    }

    public static function getEloquentQuery(): Builder
    {
        return Customer::query()
            ->with(['agent']);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                InfoLists\Components\Section::make()
                    ->schema([
                        InfoLists\Components\TextEntry::make('name')
                            ->label('Company name'),
                        InfoLists\Components\TextEntry::make('owner_name'),
                        InfoLists\Components\TextEntry::make('email'),
                        InfoLists\Components\TextEntry::make('phone')
                            ->hintAction(
                                fn ($state) => InfoLists\Components\Actions\Action::make('chat')
                                    ->icon('tabler-brand-whatsapp')
                                    ->color('success')
                                    ->url(wa_chat_url($state))
                                    ->openUrlInNewTab()
                                    ->visible((bool) $state)
                            ),
                        InfoLists\Components\TextEntry::make('permit_no')
                            ->label('No. Izin PPIU'),
                        InfoLists\Components\TextEntry::make('region')
                            ->label('Domisili'),
                        InfoLists\Components\TextEntry::make('address'),
                        InfoLists\Components\TextEntry::make('agent.name'),
                    ])
                    ->columns(['lg' => 2])
                    ->columnSpan(['lg' => 2]),
                InfoLists\Components\Section::make()
                    ->schema([
                        InfoLists\Components\ImageEntry::make('logoUrl')
                            ->label('Logo')
                            ->width('100%')
                            ->height('auto'),
                        InfoLists\Components\ImageEntry::make('logoSquareUrl')
                            ->label('Square logo'),
                    ])
                    ->columnSpan(1),
            ])
            ->columns(['lg' => 3]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Company name')
                            ->required(),
                        Forms\Components\TextInput::make('owner_name'),
                        Forms\Components\TextInput::make('email')
                            ->email(),
                        PhoneInput::make('phone'),
                        Forms\Components\TextInput::make('permit_no')
                            ->label('No. Izin PPIU'),
                        Forms\Components\TextInput::make('region')
                            ->label('Domisili'),
                        Forms\Components\Textarea::make('address')
                            ->rows(2),
                        Forms\Components\Select::make('agent')
                            ->relationship('agent', 'name'),
                        Forms\Components\FileUpload::make('logo')
                            ->image()
                            ->disk('s3')
                            ->directory('customers')
                            ->visibility('public'),
                        Forms\Components\FileUpload::make('logo_square')
                            ->label('Square logo')
                            ->image()
                            ->disk('s3')
                            ->directory('customers')
                            ->visibility('public'),
                    ])
                    ->columns(['md' => 2])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->withCount('invoices'))
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID'),
                Tables\Columns\ImageColumn::make('logo')
                    ->label('')
                    ->getStateUsing(fn ($record) => $record->logoSquareUrl ?? $record->logoUrl),
                Tables\Columns\TextColumn::make('name')
                    ->label('Company')
                    ->sortable()
                    ->description(fn ($record) => $record->email)
                    ->searchable(['customers.name', 'customers.email']),
                Tables\Columns\TextColumn::make('owner_name')
                    ->label('Owner')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('agent.name'),
                Tables\Columns\TextColumn::make('latest_arrival')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('agent')
                    ->relationship('agent', 'name'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn ($record) => ! $record->latest_arrival && $record->invoices_count == 0),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\GroupsRelationManager::class,
            RelationManagers\UsersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'view' => Pages\ViewCustomer::route('/{record}'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\CustomerOverview::class,
        ];
    }
}
