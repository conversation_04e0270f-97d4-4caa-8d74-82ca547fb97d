<?php

namespace App\Filament\Resources;

use App\Enums\HotelRoomType;
use App\Filament\Resources\GroupHotelResource\Pages;
use App\Models\GroupHotel;
use App\Models\Hotel;
use App\Models\Vendors\HotelBroker;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class GroupHotelResource extends Resource
{
    protected static ?string $model = GroupHotel::class;

    protected static ?string $modelLabel = 'hotel';

    protected static ?string $navigationGroup = 'Schedules';

    protected static ?int $navigationSort = 30;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Finance', 'Admin Operator', 'Operator']);
    }

    public static function getEloquentQuery(): Builder
    {
        return GroupHotel::query()
            ->with(['group.customer', 'hotel'])
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->orderBy('check_in');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('group.name')
                    ->label('Group')
                    ->getStateUsing(fn ($record) => "#{$record->group_id} - {$record->group->name}")
                    ->description(fn ($record) => $record->group->customer->name, 'above')
                    ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group]))
                    ->openUrlInNewTab(),
                Tables\Columns\TextColumn::make('hotel.name')
                    ->label('Hotel')
                    ->description(fn ($record) => $record->hotel->city, 'above'),
                Tables\Columns\TextColumn::make('check_in')
                    ->date(),
                Tables\Columns\TextColumn::make('check_out')
                    ->date(),
                Tables\Columns\TextColumn::make('broker.contact_name')
                    ->label('Broker')
                    ->description(function ($record) {
                        if ($record->broker) {
                            return $record->broker->company_name !== $record->broker->contact_name
                                ? $record->broker->company_name
                                : null;
                        }
                        return null;
                    }, 'above'),
                Tables\Columns\TextColumn::make('rooms')
                    ->badge()
                    ->listWithLineBreaks()
                    ->getStateUsing(fn (GroupHotel $record) => collect([
                        $record->room_single_count ? $record->room_single_count . ' × Single' : null,
                        $record->room_double_count ? $record->room_double_count . ' × Double' : null,
                        $record->room_triple_count ? $record->room_triple_count . ' × Triple' : null,
                        $record->room_quad_count ? $record->room_quad_count . ' × Quad' : null,
                        $record->room_quint_count ? $record->room_quint_count . ' × Quint' : null,
                    ])
                        ->filter()
                        ->toArray()
                    ),
                Tables\Columns\TextColumn::make('meta.room_type')
                    ->label('Room type')
                    ->formatStateUsing(fn ($state) => HotelRoomType::tryFrom($state)?->getLabel() ?? $state)
                    ->badge(),
                Tables\Columns\TextColumn::make('meta.meal')
                    ->label('Meal plan'),
            ])
            ->filters([
                Tables\Filters\Filter::make('month')
                    ->form([
                        Forms\Components\TextInput::make('month')
                            ->type('month')
                            ->default(Carbon::now()->format('Y-m')),
                    ])
                    ->query(function (Builder $query, $data) {
                        return $query
                            ->when(
                                $data['month'],
                                fn (Builder $query, $month) => $query->where('check_in', 'like', "{$month}%")
                            );
                    })
                    ->indicateUsing(function ($data) {
                        if (! $data['month']) {
                            return null;
                        }

                        return 'Month: ' . Carbon::parse($data['month'] . '-01')->format('F Y');
                    }),
                Tables\Filters\SelectFilter::make('city')
                    ->options(fn () => Hotel::getCities())
                    ->searchable()
                    ->modifyQueryUsing(function (Builder $query, $data) {
                        $city = $data['value'] ?? null;
                        $query
                            ->when($city, fn ($query) => $query
                                ->whereHas('hotel', fn ($query) => $query
                                    ->where('city', $city)));
                    }),
                Tables\Filters\SelectFilter::make('hotel_id')
                    ->label('Hotel')
                    ->options(fn () => Hotel::query()->orderBy('fullname')->pluck('fullname', 'id'))
                    ->searchable(),
                Tables\Filters\SelectFilter::make('broker_id')
                    ->label('Broker')
                    ->options(fn () => HotelBroker::query()->orderBy('company_name')->pluck('company_name', 'id'))
                    ->searchable(),
            ])
            ->filtersLayout(FiltersLayout::AboveContent);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageGroupHotels::route('/'),
        ];
    }
}
