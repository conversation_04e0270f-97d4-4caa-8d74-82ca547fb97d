<?php

namespace App\Filament\Resources\MutawifResource\Pages;

use App\Filament\Resources\MutawifResource;
use App\Models\User;
use Carbon\Carbon;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageMutawifs extends ManageRecords
{
    protected static string $resource = MutawifResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->using(function ($data) {
                    /** @var User */
                    $mutawif = User::create($data);

                    $mutawif->syncRoles(['Mutawif']);
                    $mutawif->email_verified_at = Carbon::now();
                    $mutawif->save();

                    return $mutawif;
                }),
        ];
    }
}
