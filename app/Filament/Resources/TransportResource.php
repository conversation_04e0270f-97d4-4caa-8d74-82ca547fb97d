<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransportResource\Pages;
use App\Filament\Resources\TransportResource\RelationManagers;
use App\Models\Transport;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;

/**
 * @deprecated 2025.02.17
 */
class TransportResource extends Resource
{
    protected static ?string $model = Transport::class;

    protected static ?string $modelLabel = 'transport company';

    protected static ?string $navigationGroup = 'Master Data';

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static ?int $navigationSort = 3;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['company_name'];
    }

    public static function getGlobalSearchResultTitle(Model $record): string
    {
        return $record->company_name;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('company_name')
                    ->label('Company Name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('company_name')
                    ->label('Company Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('groups_count')
                    ->alignCenter()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->modalSubheading(function ($record) {
                        $text = '<p>Are you sure you would like to do this?</p>';
                        $groups = $record->groups()->with(['customer'])->get();
                        if (count($groups) > 0) {
                            $text = '
                            <p>This data cannot be removed because it is used in these groups:</p>
                            <table class="w-full divide-y table-auto text-start">
                                <thead>
                                    <tr class="bg-gray-500/5">
                                        <th class="p-0">
                                            <div class="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-600 cursor-default gap-x-1 whitespace-nowrap ">Group</div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y whitespace-nowrap">
                            ';
                            foreach ($groups as $g) {
                                $text .= '<tr><td class="px-4 py-2">'
                                    . '<div class="text-sm text-gray-500">'
                                    . $g->customer->name
                                    . '</div> '
                                    . $g->name
                                    . '</td></tr>';
                            }
                            $text .= '
                                </tbody>
                            </table>
                            ';
                        }

                        return new HtmlString($text);
                    }),
            ])
            ->bulkActions([])
            ->defaultSort('company_name');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTransports::route('/'),
            'view' => Pages\ViewTransport::route('/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\GroupsRelationManager::class,
        ];
    }
}
