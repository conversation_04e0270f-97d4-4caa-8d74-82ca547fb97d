<?php

namespace App\Filament\Resources\SnackHandlerResource\Pages;

use App\Filament\Resources\SnackHandlerResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageSnackHandlers extends ManageRecords
{
    protected static string $resource = SnackHandlerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
