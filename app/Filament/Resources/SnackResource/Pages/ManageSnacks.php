<?php

namespace App\Filament\Resources\SnackResource\Pages;

use App\Exports\SnackExport;
use App\Filament\Resources\SnackResource;
use App\Settings\SnackSettings;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;

class ManageSnacks extends ManageRecords
{
    protected static string $resource = SnackResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('settings')
                ->color('gray')
                ->icon('heroicon-o-cog-6-tooth')
                ->form([
                    Forms\Components\KeyValue::make('prices')
                        ->keyLabel('Snack Type')
                        ->valueLabel('Price (SAR)'),
                ])
                ->fillForm(function () {
                    $settings = app(SnackSettings::class);

                    return $settings->toArray();
                })
                ->action(function ($data) {
                    $settings = app(SnackSettings::class);
                    $settings->fill($data);
                    $settings->save();

                    Notification::make()
                        ->success()
                        ->title('Settings saved.')
                        ->send();
                })
                ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new SnackExport($this->getFilteredTableQuery(), $this->tableFilters))->download();
                    }),
                Actions\Action::make('csv')
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->action(function () {
                        return (new SnackExport($this->getFilteredTableQuery(), $this->tableFilters, 'csv'))->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
        ];
    }
}
