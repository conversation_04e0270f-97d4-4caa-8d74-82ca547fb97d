<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GroupFlightResource\Pages;
use App\Models\Airport;
use App\Models\GroupFlight;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class GroupFlightResource extends Resource
{
    protected static ?string $model = GroupFlight::class;

    protected static ?string $modelLabel = 'flight';

    protected static ?string $navigationGroup = 'Schedules';

    protected static ?int $navigationSort = 20;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Finance', 'Admin Operator', 'Operator']);
    }

    public static function getEloquentQuery(): Builder
    {
        return GroupFlight::query()
            ->with(['group.customer', 'airline', 'handler'])
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->orderBy('date_etd');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(fn ($state) => str($state)->headline())
                    ->badge()
                    ->colors([
                        'success',
                        'primary' => 'arrival',
                    ]),
                Tables\Columns\TextColumn::make('group.name')
                    ->label('Group')
                    ->getStateUsing(fn ($record) => "#{$record->group_id} - {$record->group->name}")
                    ->description(fn ($record) => $record->group->customer->name, 'above')
                    ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group]))
                    ->openUrlInNewTab(),
                Tables\Columns\TextColumn::make('flight_number')
                    ->label('Flight No.')
                    ->getStateUsing(fn ($record) => collect([$record->flight_number, $record->via_number])->filter()->join(' & ')),
                Tables\Columns\TextColumn::make('route')
                    ->getStateUsing(fn ($record) => $record->from . ($record->via ? ' ✈ ' . $record->via : '') . ' ✈ ' . $record->to),
                Tables\Columns\TextColumn::make('date_etd')
                    ->label('Date')
                    ->date(),
                Tables\Columns\TextColumn::make('etd')
                    ->label('ETD'),
                Tables\Columns\TextColumn::make('eta')
                    ->label('ETA'),
                Tables\Columns\TextColumn::make('pax')
                    ->alignCenter()
                    ->getStateUsing(fn ($record) => $record->pax ?? $record->group->total_pax),
                Tables\Columns\TextColumn::make('handler.company_name')
                    ->label('Handler'),
                Tables\Columns\TextColumn::make('mealbox'),
            ])
            ->filters([
                Tables\Filters\Filter::make('month')
                    ->form([
                        Forms\Components\TextInput::make('month')
                            ->type('month')
                            ->default(Carbon::now()->format('Y-m')),
                    ])
                    ->query(function (Builder $query, $data) {
                        return $query
                            ->when(
                                $data['month'],
                                fn (Builder $query, $month) => $query->where('date_etd', 'like', "{$month}%")
                            );
                    })
                    ->indicateUsing(function ($data) {
                        if (! $data['month']) {
                            return null;
                        }

                        return 'Month: ' . Carbon::parse($data['month'] . '-01')->format('F Y');
                    }),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'arrival' => 'Arrival',
                        'departure' => 'Departure',
                    ]),
                Tables\Filters\SelectFilter::make('from')
                    ->label('Departing from')
                    ->options(fn () => Airport::query()->orderBy('code')->pluck('code', 'code'))
                    ->multiple()
                    ->searchable(),
                Tables\Filters\SelectFilter::make('to')
                    ->label('Arriving at')
                    ->options(fn () => Airport::query()->orderBy('code')->pluck('code', 'code'))
                    ->multiple()
                    ->searchable(),
                Tables\Filters\SelectFilter::make('handler')
                    ->label('Handler')
                    ->attribute('handler_id')
                    ->relationship('handler', 'company_name')
                    ->preload()
                    ->searchable(),
            ])
            ->filtersLayout(FiltersLayout::AboveContent);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageGroupFlights::route('/'),
        ];
    }
}
