<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationLabel = 'All Users';

    protected static ?string $navigationGroup = 'Users';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\TextInput::make('email')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('phone'),
                Forms\Components\Select::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),
                Forms\Components\TextInput::make('password')
                    ->required()
                    ->password()
                    ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                    ->visible(fn ($record) => $record == null)
                    ->rule(Password::default()),
                Forms\Components\Group::make([
                    Forms\Components\TextInput::make('new_password')
                        ->password()
                        ->label('New Password')
                        ->nullable()
                        ->rule(Password::default()),
                    Forms\Components\TextInput::make('new_password_confirmation')
                        ->password()
                        ->label('Confirm New Password')
                        ->rule('required', fn ($get) => (bool) $get('new_password'))
                        ->same('new_password'),
                ])->visible(fn ($record) => $record != null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID'),
                Tables\Columns\TextColumn::make('user')
                    ->getStateUsing(fn ($record) => $record->name)
                    ->description(fn ($record) => $record->email)
                    ->searchable(['name', 'email']),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Phone')
                    ->searchable(),
                Tables\Columns\TextColumn::make('roles.name')
                    ->badge(),
                Tables\Columns\IconColumn::make('verified')
                    ->boolean()
                    ->getStateUsing(fn (User $record) => $record->email_verified_at != null)
                    ->action(function (User $record) {
                        $record->email_verified_at = $record->email_verified_at ? null : Carbon::now();
                        $record->save();
                    })
                    ->visible(fn () => auth()->user()->hasRole('Admin')),
                Tables\Columns\TextColumn::make('last_login_at')
                    ->dateTime()
                    ->description(fn ($record) => $record->last_login_ip)
                    ->sortable()
                    ->visible(fn () => auth()->user()->hasRole('Admin')),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->visible(fn () => auth()->user()->hasRole('Admin')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')
                    ->relationship('roles', 'name'),
                Tables\Filters\TernaryFilter::make('email_verified_at')
                    ->label('Verified')
                    ->nullable()
                    ->visible(fn () => auth()->user()->hasRole('Admin')),
            ])
            ->actions([
                Impersonate::make(),
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        if (isset($data['new_password'])) {
                            $data['password'] = Hash::make($data['new_password']);
                            unset($data['new_password']);
                            unset($data['new_password_confirmation']);
                        }

                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageUsers::route('/'),
        ];
    }
}
