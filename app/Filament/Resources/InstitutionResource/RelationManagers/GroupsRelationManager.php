<?php

namespace App\Filament\Resources\InstitutionResource\RelationManagers;

use App\Filament\Resources\GroupResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class GroupsRelationManager extends RelationManager
{
    protected static string $relationship = 'groups';

    protected static ?string $recordTitleAttribute = 'name';

    public function getRelationship(): Relation|Builder
    {
        return $this->getOwnerRecord()->{static::getRelationshipName()}()
            ->currentPeriod()
            ->confirmed();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Forms\Components\TextInput::make('name')
                //     ->required()
                //     ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordUrl(fn ($record) => GroupResource::getUrl('view', ['record' => $record]))
            ->columns([
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->getStateUsing(fn ($record) => $record->customer->name)
                    ->description(fn ($record) => $record->name)
                    ->searchable(['name']),
                Tables\Columns\TextColumn::make('number')
                    ->label('رقم')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('arrival_date')
                    ->label('Arrival Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_pax')
                    ->label('Total Pax')
                    ->alignCenter(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('arrival_date', 'desc');
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
