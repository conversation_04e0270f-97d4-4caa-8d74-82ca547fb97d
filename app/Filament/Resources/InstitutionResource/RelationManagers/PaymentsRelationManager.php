<?php

namespace App\Filament\Resources\InstitutionResource\RelationManagers;

use App\Filament\Resources\GroupResource;
use App\Models\Finance\CashCategory;
use App\Models\Group;
use App\Models\GroupCash;
use App\Models\User;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class PaymentsRelationManager extends RelationManager
{
    protected static string $relationship = 'payments';

    protected static ?string $recordTitleAttribute = 'description';

    protected static ?string $modelLabel = 'payment';

    protected static ?string $pluralModelLabel = 'payments';

    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
    {
        return auth()->user()->hasRole(['Admin', 'Finance']);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Operator')
                    ->options(User::query()->role(['Operator'])->pluck('name', 'id'))
                    ->required(),
                Forms\Components\DateTimePicker::make('cashed_at')
                    ->label('Date')
                    ->required(),
                Group::formFieldSelectGroup(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\Checkbox::make('is_excluded')
                    ->label('Exclude Package'),
                Forms\Components\TextInput::make('cash_out')
                    ->label('Amount')
                    ->numeric()
                    ->prefix('SAR')
                    ->required(),
                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('cover')
                    ->disk('s3')
                    ->directory('finance/attachments')
                    ->visibility('public'),
            ])
            ->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Operator'),
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn (GroupCash $record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer->name ?? null)
                    ->description(fn ($record) => $record->group?->name ?? null),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                BadgeableColumn::make('description')
                    ->wrap()
                    ->description(fn ($record) => $record->cashed_at, 'above')
                    ->description(fn ($record) => $record->category?->name ?? null)
                    ->suffixBadges([
                        Badge::make('is_excluded')
                            ->label('Exclude')
                            ->color('warning')
                            ->visible(fn ($record) => $record->is_excluded),
                    ]),
                Tables\Columns\TextColumn::make('cash_out')
                    ->label('Amount')
                    ->money('SAR'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mountUsing(fn ($form) => $form->fill(['cashed_at' => Carbon::now()]))
                    ->mutateFormDataUsing(function ($livewire, $data) {
                        $data['division'] = 'operator';
                        $data['category_id'] = CashCategory::query()->where('name', 'Pembayaran Visa')->value('id');
                        $data['institution_id'] = $livewire->ownerRecord->id;

                        return $data;
                    })
                    ->disableCreateAnother()
                    ->modalWidth('sm'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('cashed_at', 'desc');
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
