<?php

namespace App\Filament\Resources\InstitutionResource\Pages;

use App\Filament\Resources\InstitutionResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Umrahservice\Groups\Enums\GroupStatus;

class ManageInstitutions extends ManageRecords
{
    protected static string $resource = InstitutionResource::class;

    protected function getTableQuery(): Builder
    {
        return static::getResource()::getEloquentQuery()
            ->leftJoin('groups', function (JoinClause $join) {
                $join->on('groups.institution_id', 'institutions.id')
                    ->where('groups.status', GroupStatus::Confirmed);
            })
            ->select([
                'institutions.id', 'institutions.name', 'institutions.email', 'institutions.phone',
                DB::raw('SUM(`groups`.total_pax) AS total_pax'),
                DB::raw('MAX(`groups`.arrival_date) AS latest_arrival'),
            ])
            ->groupBy([
                'institutions.id', 'institutions.name', 'institutions.email', 'institutions.phone',
            ])
            ->orderByDesc(DB::raw('MAX(groups.arrival_date)'))
            ->orderBy('institutions.name');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
