<?php

namespace App\Filament\Resources\InstitutionResource\Widgets;

use App\Models\GroupCash;
use App\Models\Institution;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Card;

class InstitutionOverview extends BaseWidget
{
    public ?Institution $record;

    public static function canView(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Finance']);
    }

    protected function getCards(): array
    {
        $total_payments = GroupCash::query()
            ->where('institution_id', $this->record->id)
            ->sum('cash_out');

        return [
            Card::make('Total Payments', money($total_payments, 'sar', true)),
        ];
    }
}
