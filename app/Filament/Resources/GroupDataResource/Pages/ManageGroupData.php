<?php

namespace App\Filament\Resources\GroupDataResource\Pages;

use App\Filament\Resources\GroupDataResource;
use App\Models\GroupData;
use Closure;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageGroupData extends ManageRecords
{
    protected static string $resource = GroupDataResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->disableCreateAnother()
                ->mountUsing(fn ($form) => $form->fill([
                    'files' => collect(GroupData::FILES)
                        ->map(fn ($f) => ['name' => $f, 'file' => null])
                        ->toArray(),
                ]))
            // ->mutateFormDataUsing(function ($data) {
            //     $data['files'] = collect($data['files'])
            //         ->filter(fn ($f) => !!$f['file'])
            //         ->toArray();

            //     return $data;
            // }),
        ];
    }

    protected function getTableRecordActionUsing(): ?Closure
    {
        return null;
    }
}
