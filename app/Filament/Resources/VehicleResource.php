<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VehicleResource\Pages;
use App\Models\Vehicle;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

class VehicleResource extends Resource
{
    protected static ?string $model = Vehicle::class;

    protected static ?string $modelLabel = 'transport vehicle';

    protected static ?string $navigationGroup = 'Master Data';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->rows(2),
                Forms\Components\TextInput::make('capacity')
                    ->numeric()
                    ->suffix('pax')
                    ->required(),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->description(fn ($record) => $record->description),
                Tables\Columns\BadgeColumn::make('capacity')
                    ->formatStateUsing(fn ($state) => "{$state} pax"),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make()
                    ->modalSubheading(function ($record) {
                        $text = '<p>Are you sure you would like to do this?</p>';
                        $groups = $record->groups()->with(['customer'])->get();
                        if (count($groups) > 0) {
                            $text = '
                            <p>This data cannot be removed because it is used in these groups:</p>
                            <table class="w-full divide-y table-auto text-start">
                                <thead>
                                    <tr class="bg-gray-500/5">
                                        <th class="p-0">
                                            <div class="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-600 cursor-default gap-x-1 whitespace-nowrap ">Group</div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y whitespace-nowrap">
                            ';
                            foreach ($groups as $g) {
                                $text .= '<tr><td class="px-4 py-2">'
                                    . '<div class="text-sm text-gray-500">'
                                    . $g->customer->name
                                    . '</div> '
                                    . $g->name
                                    . '</td></tr>';
                            }
                            $text .= '
                                </tbody>
                            </table>
                            ';
                        }

                        return new HtmlString($text);
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageVehicles::route('/'),
        ];
    }
}
