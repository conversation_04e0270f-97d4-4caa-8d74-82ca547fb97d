<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GroupHotelConfirmationResource\Pages;
use App\Filament\Resources\GroupHotelConfirmationResource\RelationManagers;
use App\Models\Group;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Umrahservice\Groups\Enums\GroupStatus;

class GroupHotelConfirmationResource extends Resource
{
    protected static ?string $modelLabel = 'group hotel confirmation';

    protected static ?string $model = Group::class;

    protected static ?string $navigationGroup = 'Hotels';

    protected static ?int $navigationSort = 3;

    protected static ?string $slug = 'group-hotel-confirmations';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->can('update', new Group);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('customer')
                            ->relationship('customer', 'name'),
                        Forms\Components\TextInput::make('name')
                            ->label('Group Name'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->select(['*', DB::raw('IF(arrival_date <= NOW(), 1, 0) AS is_past')])
                ->orderBy('is_past')
                ->orderBy('arrival_date')
            )
            ->columns([
                BadgeableColumn::make('group')
                    ->label('Group')
                    ->url(fn (Group $record) => $record->is_confirmed
                    ? GroupResource::getUrl('view', ['record' => $record])
                    : null)
                    ->getStateUsing(fn ($record) => $record->customer->name)
                    ->description(fn ($record) => $record->name)
                    ->searchable(true, function (Builder $query, string $search) {
                        if ($search) {
                            return $query
                                ->orWhere('name', 'like', "%{$search}%")
                                ->orWhereHas('customer', fn ($q) => $q->where('name', 'like', "%{$search}%"));
                        }

                        return $query;
                    })
                    ->suffixBadges([
                        Badge::make('pending')
                            ->color('warning')
                            ->visible(fn ($record) => $record->status == GroupStatus::Pending),
                        Badge::make('cancelled')
                            ->color('danger')
                            ->visible(fn ($record) => $record->status == GroupStatus::Cancelled),
                    ]),
                Tables\Columns\TextColumn::make('arrival_date')
                    ->label('Arrival')
                    ->date(),
                Tables\Columns\ViewColumn::make('hotels')
                    ->view('filament.tables.columns.group-hotel-confirmations-details')
                    ->searchable(true, function (Builder $query, string $search) {
                        if ($search) {
                            return $query
                                ->orWhereHas('group_hotels', fn ($q) => $q->where('confirmation_number', 'like', "%{$search}%"));
                        }

                        return $query;
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('customer')
                    ->relationship('customer', 'name')
                    ->searchable(),
                Tables\Filters\Filter::make('arrival_date')
                    ->columnSpan(['md' => 2])
                    ->form([
                        DateRangePicker::make('date_range')
                            ->label('Arrival Date'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['date_range'] ?? null,
                                function (Builder $query, $date_range): Builder {
                                    $dates = explode(' - ', $date_range);

                                    if (count($dates) == 2) {
                                        return $query
                                            ->whereHas('flights', fn (Builder $query) => $query->where('type', 'arrival')->whereBetween('date_eta', [
                                                Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                                                Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                                            ]));
                                    }

                                    return $query;
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['date_range'] ?? null) {
                            // $dates = explode(' - ', $data['date_range']);

                            // if (count($dates) == 2) {
                            //     return 'Arrival: '.Carbon::createFromFormat('d/m/Y', $dates[0])->format('d-M-y').' - '.Carbon::createFromFormat('d/m/Y', $dates[1])->format('d-M-y');
                            // }
                            return 'Arrival: ' . $data['date_range'];
                        }

                        return null;
                    }),
                Tables\Filters\Filter::make('check_in')
                    ->columnSpan(['md' => 2])
                    ->form([
                        DateRangePicker::make('date_range')
                            ->label('Check In'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['date_range'],
                                function (Builder $query, $date_range): Builder {
                                    $dates = explode(' - ', $date_range);

                                    if (count($dates) == 2) {
                                        return $query
                                            ->whereHas('group_hotels', fn (Builder $query) => $query->whereBetween('check_in', [
                                                Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                                                Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                                            ]));
                                    }

                                    return $query;
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['date_range'] ?? null) {
                            return 'Check In: ' . $data['date_range'];
                        }

                        return null;
                    }),
                Tables\Filters\Filter::make('check_out')
                    ->columnSpan(['md' => 2])
                    ->form([
                        DateRangePicker::make('date_range')
                            ->label('Check Out'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['date_range'],
                                function (Builder $query, $date_range): Builder {
                                    $dates = explode(' - ', $date_range);

                                    if (count($dates) == 2) {
                                        return $query
                                            ->whereHas('group_hotels', fn (Builder $query) => $query->whereBetween('check_out', [
                                                Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                                                Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                                            ]));
                                    }

                                    return $query;
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['date_range'] ?? null) {
                            return 'Check Out: ' . $data['date_range'];
                        }

                        return null;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->filtersLayout(\Filament\Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(['lg' => 4]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\GroupHotelsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGroupHotelConfirmations::route('/'),
            'view' => Pages\ViewGroupHotelConfirmation::route('/{record}'),
        ];
    }
}
