<?php

namespace App\Filament\Resources\Finance;

use App\Filament\Resources\Finance\OperatorFinanceResource\Pages;
use App\Filament\Resources\Finance\OperatorFinanceResource\Widgets;
use App\Filament\Resources\GroupResource;
use App\Models\Finance\CashCategory;
use App\Models\Group;
use App\Models\GroupCash;
use App\Models\User;
use App\Models\Vendors\HotelBroker;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Carbon\Carbon;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class OperatorFinanceResource extends Resource
{
    protected static ?string $model = GroupCash::class;

    protected static ?string $modelLabel = 'Operator';

    protected static ?string $pluralModelLabel = 'Operator';

    protected static ?string $navigationGroup = 'Finance';

    protected static ?string $navigationLabel = 'Operator';

    protected static ?int $navigationSort = 2;

    protected static ?string $slug = 'finance/operator';

    public static $cat_hotel_id = 0;

    public static $cat_visa_id = 0;

    public static $cat_options = null;

    public static function getCategoryOptions(): array
    {
        if (! is_array(static::$cat_options)) {
            $options = [];
            CashCategory::with(['children'])->whereNull('parent_id')->orWhere('parent_id', 0)->get()
                ->each(function ($cat) use (&$options) {
                    if ($cat->name == 'Pembayaran Hotel') {
                        static::$cat_hotel_id = $cat->id;
                    }
                    if ($cat->name == 'Pembayaran Visa') {
                        static::$cat_visa_id = $cat->id;
                    }
                    $options[$cat->id] = $cat->name;
                    $cat->children->each(function ($child) use (&$options) {
                        $options[$child->id] = '- ' . $child->name;
                    });
                });
            static::$cat_options = $options;
        }

        return static::$cat_options;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Operator', 'Finance']);
    }

    public static function getEloquentQuery(): Builder
    {
        $user = auth()->user();

        return GroupCash::query()
            ->with(['group.customer', 'user', 'category'])
            ->when($user->hasRole(['Admin', 'Finance']), function ($query) {
                $query
                    ->where('division', 'operator');
            })
            ->when(! $user->hasRole(['Admin', 'Finance']), function ($query) use ($user) {
                $query
                    ->where('division', 'operator')
                    ->where('user_id', $user->id);
            })
            ->orderByDesc('cashed_at');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Operator')
                    ->options(User::query()->role(['Operator'])->pluck('name', 'id'))
                    ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
                Forms\Components\DateTimePicker::make('cashed_at')
                    ->label('Date')
                    ->default(Carbon::now())
                    ->required(),
                Group::formFieldSelectGroup(),
                SelectTree::make('category_id')
                    ->label('Category')
                    ->relationship(
                        'category',
                        'name',
                        'parent_id',
                    )
                    ->searchable()
                    ->required()
                    ->live(),
                Forms\Components\Select::make('hotel_broker_id')
                    ->label('Hotel Broker')
                    ->options(HotelBroker::withFullname()->get()->pluck('fullname', 'id'))
                    ->searchable()
                    ->visible(function ($get) {
                        $category_id = $get('category_id');
                        if ($category_id) {
                            return CashCategory::find($category_id)?->parent_id == static::$cat_hotel_id;
                        }

                        return false;
                    })
                    ->required(),
                Forms\Components\Select::make('institution_id')
                    ->label('Muassasah')
                    ->relationship('institution', 'name')
                    ->visible(function ($get) {
                        return $get('category_id') == static::$cat_visa_id;
                    })
                    ->required(),
                Forms\Components\ToggleButtons::make('type')
                    ->options([
                        'in' => 'Masuk',
                        'out' => 'Keluar',
                    ])
                    ->grouped()
                    ->default('out')
                    ->reactive(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255)
                    ->required(),
                Forms\Components\Checkbox::make('is_excluded')
                    ->label('Exclude Package'),
                ...GroupCash::getAmountInputFormSchema(),
                Forms\Components\FileUpload::make('attachment')
                    ->label('Bukti Transaksi')
                    ->required(fn ($get) => $get('type') == 'out')
                    ->validationMessages(['required' => 'Pengeluaran wajib menyertakan bukti transaksi'])
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('cover')
                    ->disk('s3')
                    ->directory('finance/attachments')
                    ->visibility('public'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Operator')
                    ->url(fn ($record) => OperatorFinanceResource::getUrl('index', ['tableFilters[user_id][value]' => $record->user_id]))
                    ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn (GroupCash $record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer->name ?? null)
                    ->description(fn ($record) => $record->group?->name ?? null),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                BadgeableColumn::make('description')
                    ->wrap()
                    ->description(fn ($record) => $record->cashed_at, 'above')
                    ->description(fn ($record) => $record->category?->name ?? null)
                    ->suffixBadges([
                        Badge::make('is_excluded')
                            ->label('Exclude')
                            ->color('warning')
                            ->visible(fn ($record) => $record->is_excluded),
                    ])
                    ->searchable(),
                ...GroupCash::getAmountTableColumns(),
            ])
            ->filters([
                DateRangeFilter::make('cashed_at')
                    ->columnSpan(['md' => 2])
                    ->label('Date range')
                    ->withIndicator(),
                Tables\Filters\Filter::make('group')
                    ->form([
                        Group::formFieldSelectGroup()
                            ->placeholder('All groups'),
                    ])
                    ->query(function ($query, $data) {
                        return $query
                            ->when(
                                $data['group_id'],
                                fn ($query, $group_id) => $query->where('group_id', $group_id)
                            );
                    }),
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Operator')
                    ->relationship('user', 'name', fn ($query) => $query->role(['Operator']))
                    ->searchable()
                    ->preload()
                    ->visible(fn () => auth()->user()->hasRole(['Admin', 'Finance'])),
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Category')
                    ->options(static::getCategoryOptions())
                    ->searchable(),
                Tables\Filters\Filter::make('arrival_date')
                    ->columnSpan(['md' => 2])
                    ->form([
                        DateRangePicker::make('date_range')
                            ->label('Group arrival date'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when(
                                $data['date_range'] ?? null,
                                function (Builder $query, $date_range): Builder {
                                    $dates = explode(' - ', $date_range);

                                    if (count($dates) == 2) {
                                        return $query
                                            ->whereHas('group', fn (Builder $query) => $query->whereBetween('arrival_date', [
                                                Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay(),
                                                Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                                            ]));
                                    }

                                    return $query;
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['date_range'] ?? null) {
                            return 'Arrival: ' . $data['date_range'];
                        }

                        return null;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function ($data) {
                        $data['type'] = $data['cash_in'] > 0 ? 'in' : 'out';
                        $data['amount'] = $data['type'] == 'in' ? $data['cash_in'] : $data['cash_out'];

                        return $data;
                    })
                    ->mutateFormDataUsing(function ($data) {
                        if ($data['type'] == 'in') {
                            $data['cash_in'] = $data['amount'];
                            $data['cash_out'] = null;
                        } else {
                            $data['cash_out'] = $data['amount'];
                            $data['cash_in'] = null;
                        }

                        return $data;
                    })
                    ->modalHeading('Edit'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->filtersLayout(\Filament\Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(['lg' => 4]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOperatorFinances::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\OperatorFinanceOverview::class,
        ];
    }
}
