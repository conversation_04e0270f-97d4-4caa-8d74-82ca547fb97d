<?php

namespace App\Filament\Resources\Finance\OperatorFinanceResource\Widgets;

use App\Models\GroupCash;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class OperatorFinanceOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected static bool $isLazy = false;

    protected $listeners = ['tableFiltersUpdated'];

    public $tableFilters = null;

    public function mount()
    {
        $this->tableFilters = request('tableFilters');
    }

    public function tableFiltersUpdated($value)
    {
        $this->tableFilters = $value;
    }

    protected function getCards(): array
    {
        if (auth()->user()->hasRole('Operator') || isset($this->tableFilters['user_id']['value'])) {
            $data = $this->getData();

            return [
                Stat::make('Income', money($data->income ?? 0, 'SAR', true)),
                Stat::make('Expense', money($data->expense ?? 0, 'SAR', true)),
                Stat::make('Balance', money($data->balance ?? 0, 'SAR', true)),
            ];
        }

        return [];
    }

    protected function getData()
    {
        $auth_user = auth()->user();

        $user_id = $auth_user->hasRole(['Operator']) ? $auth_user->id : $this->tableFilters['user_id']['value'];

        return GroupCash::query()
            ->where('user_id', $user_id)
            ->where('division', 'operator')
            ->select([
                DB::raw('SUM(IFNULL(`cash_in`, 0) / `exchange_rate`) AS income'),
                DB::raw('SUM(IFNULL(`cash_out`, 0) / `exchange_rate`) AS expense'),
                DB::raw('SUM(IFNULL(`cash_in`, 0) / `exchange_rate`) - SUM(IFNULL(`cash_out`, 0) / `exchange_rate`) AS balance'),
            ])
            ->first();
    }
}
