<?php

namespace App\Filament\Resources\Finance\OperatorFinanceResource\Pages;

use App\Actions\ExportOperatorCashAttachments;
use App\Exports\OperatorCashExport;
use App\Filament\Resources\Finance\OperatorFinanceResource;
use App\Filament\Resources\Finance\OperatorFinanceResource\Widgets;
use App\Imports\OperatorCashImport;
use EightyNine\ExcelImport\ExcelImportAction;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageOperatorFinances extends ManageRecords
{
    protected static string $resource = OperatorFinanceResource::class;

    protected static ?string $title = 'Operator';

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('download_template')
                    ->icon('phosphor-file')
                    ->action(function () {
                        return response()->download(storage_path('templates/template kas operator.xlsx'));
                    }),
                ExcelImportAction::make()
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->color('gray')
                    ->uploadField(fn ($upload) => $upload->label('Excel file'))
                    ->use(OperatorCashImport::class),
            ])
                ->label('Import')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('gray')
                ->button()
                ->visible(auth('web')->user()->hasRole(['Admin', 'Finance'])),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new OperatorCashExport($this->tableFilters))->download();
                    }),
                Actions\Action::make('attachments')
                    ->icon('phosphor-file-archive')
                    ->action(function () {
                        return response()->download(ExportOperatorCashAttachments::run($this->tableFilters));
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button()
                ->visible(auth('web')->user()->hasRole(['Admin', 'Finance'])),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\OperatorFinanceOverview::class,
        ];
    }

    public function updatedTableFilters(): void
    {
        parent::updatedTableFilters();

        $this->dispatch('tableFiltersUpdated', $this->tableFilters);
    }
}
