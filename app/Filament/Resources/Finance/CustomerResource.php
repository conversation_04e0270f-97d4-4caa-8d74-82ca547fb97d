<?php

namespace App\Filament\Resources\Finance;

use App\Filament\Resources\Finance\CustomerResource\Pages;
use App\Models\Customer;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationGroup = 'Finance';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 0;

    protected static ?string $slug = 'finance/customers';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function getEloquentQuery(): Builder
    {
        return Customer::query()
            ->leftJoin('customer_cashes AS cashes_in', function (JoinClause $join) {
                $join->on('customers.id', '=', 'cashes_in.customer_id')
                    ->where('cashes_in.cash_type', 'in');
            })
            ->leftJoin('customer_cashes AS cashes_out', function (JoinClause $join) {
                $join->on('customers.id', '=', 'cashes_out.customer_id')
                    ->where('cashes_out.cash_type', 'out');
            })
            ->select([
                'customers.id',
                'customers.name',
                'customers.email',
                DB::raw('SUM(IFNULL(cashes_in.amount, 0) - IFNULL(cashes_out.amount, 0)) AS balance'),
            ])
            ->groupBy([
                'customers.id',
                'customers.name',
                'customers.email',
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Company')
                    ->sortable()
                    ->description(fn ($record) => $record->email)
                    ->searchable(['name', 'email']),
                Tables\Columns\TextColumn::make('balance')
                    ->money('IDR')
                    ->sortable()
                    ->alignEnd(),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCustomers::route('/'),
        ];
    }
}
