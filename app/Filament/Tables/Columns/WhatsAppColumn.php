<?php

namespace App\Filament\Tables\Columns;

use Closure;
use Filament\Tables\Columns\TextColumn;

class WhatsAppColumn extends TextColumn
{
    protected string|Closure|null $contactName = null;

    protected string|Closure|null $contactPhone = null;

    protected function setUp(): void
    {
        parent::setUp();

        $this->badge();
        $this->openUrlInNewTab();
        $this->icon('tabler-brand-whatsapp');
        $this->color('whatsapp');
    }

    public function contactName(string|Closure|null $contactName): static
    {
        $this->contactName = $contactName;

        return $this;
    }

    public function contactPhone(string|Closure|null $contactPhone): static
    {
        $this->contactPhone = $contactPhone;

        return $this;
    }

    public function getContactName(): ?string
    {
        return $this->evaluate($this->contactName);
    }

    public function getContactPhone(): ?string
    {
        return $this->evaluate($this->contactPhone);
    }

    public function getState(): mixed
    {
        return $this->getContactName();
    }

    public function getUrl(): ?string
    {
        if (! $this->getContactName()) {
            return null;
        }

        return wa_chat_url($this->getContactPhone() ?? '');
    }
}
