<?php

namespace App\Filament\Clusters\Settings\Resources\PeriodResource\Pages;

use App\Filament\Clusters\Settings\Resources\PeriodResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManagePeriods extends ManageRecords
{
    protected static string $resource = PeriodResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('sm')
                ->disableCreateAnother(),
        ];
    }
}
