<?php

namespace App\Filament\Clusters\Settings\Resources;

use App\Filament\Clusters\Settings;
use App\Filament\Clusters\Settings\Resources\PeriodResource\Pages;
use App\Models\Period;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use GeniusTS\HijriDate\Hijri;

class PeriodResource extends Resource
{
    protected static ?string $model = Period::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?int $navigationSort = 4;

    protected static ?string $cluster = Settings::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Period')
                    ->placeholder('1445 H')
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, $set) {
                        $year = filter_var($state, FILTER_SANITIZE_NUMBER_INT);
                        if (strlen($year) >= 4) {
                            $year = (int) substr($year, 0, 4);

                            $set('date_start', Hijri::convertToGregorian(1, 1, $year)->format('Y-m-d'));
                            $set('date_end', Hijri::convertToGregorian(30, 12, $year)->format('Y-m-d'));
                        }
                    }),
                Forms\Components\DatePicker::make('date_start')
                    ->required(),
                Forms\Components\DatePicker::make('date_end')
                    ->required(),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Period')
                    ->sortable(),
                Tables\Columns\TextColumn::make('date_start')
                    ->date(),
                Tables\Columns\TextColumn::make('date_end')
                    ->date(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('name', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePeriods::route('/'),
        ];
    }
}
