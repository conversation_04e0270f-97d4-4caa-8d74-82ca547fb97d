<?php

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\WhatsAppSettings;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\SettingsPage;

class WhatsApp extends SettingsPage
{
    protected static ?string $navigationIcon = 'tabler-brand-whatsapp';

    protected static ?string $navigationLabel = 'WhatsApp';

    protected static ?int $navigationSort = 3;

    protected static ?string $title = 'WhatsApp';

    protected static ?string $slug = 'whatsapp';

    protected static ?string $cluster = Settings::class;

    protected static string $settings = WhatsAppSettings::class;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin']);
    }

    public function mount(): void
    {
        abort_unless(self::shouldRegisterNavigation(), 403);

        parent::mount();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('driver')
                            ->label('WhatsApp Driver')
                            ->disabled(),
                        Forms\Components\TextInput::make('api_url')
                            ->label('API URL')
                            ->url(),
                        Forms\Components\TextInput::make('user_token')
                            ->live(),
                        Forms\Components\TextInput::make('device_id')
                            ->label('Device ID')
                            ->visible(config('services.whatsapp.driver') == 'wanotif'),
                        // TODO: Add status check and login actions for wuzapi
                        Forms\Components\Actions\ActionContainer::make(
                            Forms\Components\Actions\Action::make('login')
                                ->url(fn ($get) => $get('api_url').'/login?token='.$get('user_token'), true)
                                ->visible(config('services.whatsapp.driver') == 'wuzapi')
                        ),
                    ]),
            ]);
    }

    public function getFormActions(): array
    {
        return [
            $this->getSaveFormAction(),
            Actions\Action::make('send_test_message')
                ->color('gray')
                ->action(function ($data) {
                    if (whatsapp()->sendText($data['phone'], $data['message'])) {
                        Notification::make()
                            ->title('Message sent successfully')
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Failed to send message')
                            ->danger()
                            ->send();
                    }
                })
                ->form([
                    Forms\Components\TextInput::make('phone')
                        ->required(),
                    Forms\Components\Textarea::make('message')
                        ->required(),
                ])
                ->modalWidth('sm')
                ->disabled(fn ($livewire) => count(array_filter($livewire->data)) < 3),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['driver'] = config('services.whatsapp.driver');

        return $data;
    }
}
