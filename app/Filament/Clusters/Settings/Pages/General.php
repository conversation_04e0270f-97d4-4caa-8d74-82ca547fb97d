<?php

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Models\Period;
use App\Settings\GeneralSettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class General extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?int $navigationSort = 1;

    protected static ?string $cluster = Settings::class;

    protected static string $settings = GeneralSettings::class;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin']);
    }

    public function mount(): void
    {
        abort_unless(self::shouldRegisterNavigation(), 403);

        parent::mount();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Active Period')
                    ->schema([
                        Forms\Components\Select::make('period_id')
                            ->hiddenLabel()
                            ->options(
                                Period::query()
                                    ->pluck('name', 'id')
                            ),
                    ]),
                Forms\Components\Section::make('Hotel Cities')
                    ->schema([
                        Forms\Components\Repeater::make('cities')
                            ->hiddenLabel()
                            ->addActionLabel('Add city')
                            ->simple(
                                Forms\Components\TextInput::make('city')
                                    ->required()
                            ),
                    ]),
            ]);
    }
}
