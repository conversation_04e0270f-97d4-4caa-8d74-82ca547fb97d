<?php

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\CompanySettings;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class CompanyProfile extends SettingsPage
{
    protected static ?string $navigationIcon = 'heroicon-o-identification';

    protected static ?int $navigationSort = 2;

    protected static ?string $cluster = Settings::class;

    protected static string $settings = CompanySettings::class;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin']);
    }

    public function mount(): void
    {
        abort_unless(self::shouldRegisterNavigation(), 403);

        parent::mount();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Company Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Company Name')
                            ->required(),
                        Forms\Components\Textarea::make('address')
                            ->label('Address')
                            ->required(),
                        Forms\Components\TextInput::make('phone')
                            ->label('Phone')
                            ->tel()
                            ->required(),
                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required(),
                        Forms\Components\TextInput::make('website')
                            ->label('Website')
                            ->required(),
                    ]),

            ]);
    }
}
