<?php

namespace App\Filament\Clusters;

use Filament\Clusters\Cluster;

class Settings extends Cluster
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'System';

    protected static ?int $navigationSort = -10;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin']);
    }
}
