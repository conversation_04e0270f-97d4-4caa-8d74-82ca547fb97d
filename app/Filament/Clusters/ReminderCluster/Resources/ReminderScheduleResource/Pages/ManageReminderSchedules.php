<?php

namespace App\Filament\Clusters\ReminderCluster\Resources\ReminderScheduleResource\Pages;

use App\Filament\Clusters\ReminderCluster\Resources\ReminderScheduleResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Support\Facades\Artisan;

class ManageReminderSchedules extends ManageRecords
{
    protected static string $resource = ReminderScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh')
                ->color('gray')
                ->label('Refresh Schedules')
                ->action(function () {
                    Artisan::call('reminders:schedule');
                    Notification::make()
                        ->success()
                        ->title('Reminder schedules refreshed successfully')
                        ->send();
                })
                ->icon('heroicon-o-arrow-path'),
        ];
    }
}
