<?php

namespace App\Filament\Clusters\ReminderCluster\Resources;

use App\Enums\NotificationType;
use App\Enums\ReminderRecipientType;
use App\Filament\Clusters\ReminderCluster;
use App\Filament\Clusters\ReminderCluster\Resources\ReminderResource\Pages;
use App\Models\Reminder;
use App\Services\ReminderService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;

class ReminderResource extends Resource
{
    protected static ?string $model = Reminder::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell';

    protected static ?string $cluster = ReminderCluster::class;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        $reminderService = app(ReminderService::class);

        return $form
            ->schema([
                Forms\Components\Select::make('reminder_type')
                    ->options($reminderService->getReminderTypesOptions())
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, callable $set, ReminderService $reminderService) {
                        $set('template', $reminderService->getDefaultTemplate($state));
                        $set('additional_data', []);
                    })
                    ->columnSpan(1),
                Forms\Components\ToggleButtons::make('notification_type')
                    ->options(NotificationType::class)
                    ->grouped()
                    ->required()
                    ->columnSpan(1),
                Forms\Components\TextInput::make('subject')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('template')
                    ->required()
                    ->columnSpanFull()
                    ->autosize()
                    ->helperText(fn ($get) => $get('reminder_type') ? 'Available tags: ' . implode(', ', array_keys($reminderService->getTemplateTags($get('reminder_type')))) : null),
                Forms\Components\Select::make('interval')
                    ->options([
                        -7 => '1 week before',
                        -3 => '3 days before',
                        -2 => '2 days before',
                        -1 => '1 day before',
                        0 => 'On the day',
                        1 => '1 day after',
                        2 => '2 days after',
                        3 => '3 days after',
                        7 => '1 week after',
                    ])
                    ->required()
                    ->columnSpan(1),
                Forms\Components\TimePicker::make('time')
                    ->required()
                    ->columnSpan(1),
                Forms\Components\Toggle::make('active')
                    ->default(true)
                    ->columnSpan(1),
                Forms\Components\Repeater::make('recipients')
                    ->columnSpanFull()
                    ->relationship('recipients')
                    ->columns(2)
                    ->schema([
                        Forms\Components\Select::make('recipient_type')
                            ->options(ReminderRecipientType::class)
                            ->required()
                            ->live(),
                        Forms\Components\Select::make('recipient_id')
                            ->label('Recipient')
                            ->options(function (callable $get) {
                                $type = $get('recipient_type');
                                switch ($type) {
                                    case ReminderRecipientType::User->value:
                                        return \App\Models\User::pluck('name', 'id');
                                    case ReminderRecipientType::Role->value:
                                        return \Spatie\Permission\Models\Role::pluck('name', 'id');
                                    default:
                                        return [];
                                }
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->visible(fn ($get) => in_array($get('recipient_type'), ['user', 'role'])),
                    ]),
                Forms\Components\Section::make('Additional Settings')
                    ->statePath('additional_data')
                    ->schema(fn ($get) => $get('reminder_type') ? $reminderService->getAdditionalFormSchema($get('reminder_type')) : [])
                    ->columns(2)
                    ->visible(fn ($get) => $get('reminder_type') && ! empty($reminderService->getAdditionalFormSchema($get('reminder_type'))))
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        $reminderService = app(ReminderService::class);

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('subject')
                    ->label('Reminder')
                    ->searchable(['subject', 'reminder_type'])
                    ->getStateUsing(fn ($record) => $record->subject ?? $reminderService->getLabel($record->reminder_type))
                    ->description(fn ($record) => $record->subject ? $reminderService->getLabel($record->reminder_type) : null),
                Tables\Columns\TextColumn::make('notification_type')
                    ->label('Via')
                    ->badge()
                    ->sortable(),
                Tables\Columns\TextColumn::make('interval')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => match ($state) {
                        -7 => '1 week before',
                        -3 => '3 days before',
                        -2 => '2 days before',
                        -1 => '1 day before',
                        0 => 'On the day',
                        1 => '1 day after',
                        2 => '2 days after',
                        3 => '3 days after',
                        7 => '1 week after',
                        default => $state . ' days',
                    }),
                Tables\Columns\TextColumn::make('time')
                    ->time()
                    ->sortable(),
                Tables\Columns\IconColumn::make('active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('deactivate')
                        ->icon('heroicon-o-x-circle')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->update(['active' => false]);
                            });
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Deactivate Reminders')
                        ->modalDescription('Are you sure you want to deactivate these reminders?')
                        ->modalSubmitActionLabel('Deactivate')
                        ->deselectRecordsAfterCompletion()
                        ->color('gray'),
                    Tables\Actions\BulkAction::make('activate')
                        ->icon('heroicon-o-check-circle')
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                $record->update(['active' => true]);
                            });
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Activate Reminders')
                        ->modalDescription('Are you sure you want to activate these reminders?')
                        ->modalSubmitActionLabel('Activate')
                        ->deselectRecordsAfterCompletion()
                        ->color('gray'),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageReminders::route('/'),
        ];
    }
}
