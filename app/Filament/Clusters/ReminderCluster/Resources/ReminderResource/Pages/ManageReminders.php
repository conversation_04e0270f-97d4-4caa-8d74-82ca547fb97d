<?php

namespace App\Filament\Clusters\ReminderCluster\Resources\ReminderResource\Pages;

use App\Filament\Clusters\ReminderCluster\Resources\ReminderResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageReminders extends ManageRecords
{
    protected static string $resource = ReminderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
