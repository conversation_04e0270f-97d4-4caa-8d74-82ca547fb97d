<?php

namespace App\Filament\Imports;

use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\Finance\Product;
use App\Models\Group;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Umrahservice\Groups\Enums\GroupStatus;

class InvoiceImporter extends Importer
{
    const CUSTOMER_NAMES = [
        'Al Hamra Rihlah Tour & Travel' => 'Al Hamra Rihlah',
        'ER Tour' => 'ER Tour & Travel',
        'Hannas Fantastic Tour' => 'Hannas Tour',
        '<PERSON><PERSON> (Umrah Perubahan)' => 'Umrah Perubahan',
        'Pangeran Tour' => 'Pangeran Tour',
        'PT. Allia Tour & Travel' => 'Allia Tour & Travel',
        'PT. Kiblat Kortasindo Perdana' => 'Kiblat Wisata',
        'PT. Mahabbah Raja Wisata' => 'Mahabbah Raja Wisata',
        'PT. Sjava Bar<PERSON>h Makmu<PERSON> Tour & Travel' => 'Sjava Tour & Travel',
        'Umrahype' => 'Umrah Hype',
    ];

    protected static ?string $model = Invoice::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('invoice_number')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Invoice Number']),

            ImportColumn::make('invoice_date')
                ->requiredMapping()
                ->rules(['date'])
                ->guess(['Invoice Date']),

            ImportColumn::make('due_date')
                ->requiredMapping()
                ->rules(['date'])
                ->guess(['Due Date']),

            ImportColumn::make('customer_id')
                ->label('Customer ID')
                ->numeric()
                ->guess(['Customer ID']),

            ImportColumn::make('customer_name')
                ->label('Customer Name')
                ->guess(['Customer Name']),

            ImportColumn::make('group_id')
                ->label('Group ID')
                ->numeric()
                ->guess(['Group ID']),

            ImportColumn::make('group_name')
                ->label('Group Name')
                ->guess(['Group Name']),

            ImportColumn::make('package_id')
                ->label('Package ID')
                ->numeric()
                ->guess(['Package ID']),

            ImportColumn::make('currency_code')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Currency Code']),

            ImportColumn::make('exchange_rate')
                ->requiredMapping()
                ->rules(['required'])
                ->numeric()
                ->guess(['Exchange Rate']),

            ImportColumn::make('subject')
                ->guess(['Subject']),

            ImportColumn::make('notes')
                ->guess(['Notes']),

            ImportColumn::make('terms')
                ->label('Terms & Conditions')
                ->guess(['Terms & Conditions']),

            // Item related columns
            ImportColumn::make('item_name')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Item Name']),

            ImportColumn::make('item_desc')
                ->label('Item Description')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Item Desc']),

            ImportColumn::make('item_qty')
                ->label('Quantity')
                ->requiredMapping()
                ->rules(['required'])
                ->numeric()
                ->guess(['Quantity']),

            ImportColumn::make('item_price')
                ->label('Item Price')
                ->requiredMapping()
                ->rules(['required'])
                ->numeric()
                ->guess(['Item Price']),
        ];
    }

    public function resolveRecord(): ?Invoice
    {
        // Determine customer_id - prioritize direct ID if provided
        $customerId = null;
        if (! empty($this->data['customer_id'])) {
            $customer = Customer::query()->find($this->data['customer_id']);
            if ($customer) {
                $customerId = $customer->id;
            }
        }

        // If no customer_id provided or found, try to find or create by name
        if (! $customerId && ! empty($this->data['customer_name'])) {
            $customerName = $this->data['customer_name'];

            if (blank($customerName)) {
                $customerId = 1;
            } else {
                $customer = Customer::query()
                    ->whereIn('name', [static::CUSTOMER_NAMES[$customerName] ?? $customerName, $customerName])
                    ->first();

                if ($customer) {
                    $customer->update(['name' => $customerName]);
                    $customerId = $customer->id;
                } else {
                    $customer = Customer::query()->create(['name' => $customerName]);
                    $customerId = $customer->id;
                }
            }
        }

        // Default to customer ID 1 if nothing found
        $customerId = $customerId ?? 1;

        // Determine group_id - prioritize direct ID if provided
        $groupId = null;
        if (! empty($this->data['group_id'])) {
            $group = Group::query()->find($this->data['group_id']);
            if ($group) {
                $groupId = $group->id;
            }
        }

        // If no group_id provided or found, try to find or create by name
        if (! $groupId && ! empty($this->data['group_name'])) {
            $groupName = $this->data['group_name'];

            if (! blank($groupName)) {
                $group = Group::query()
                    ->where('customer_id', $customerId)
                    ->where('name', $groupName)
                    ->first();

                if ($group) {
                    $groupId = $group->id;
                } else {
                    $group = Group::query()->create([
                        'name' => $groupName,
                        'customer_id' => $customerId,
                        'status' => GroupStatus::Draft,
                    ]);
                    $groupId = $group->id;
                }
            }
        }

        /** @var Invoice */
        $invoice = Invoice::query()->firstOrCreate([
            'invoice_number' => $this->data['invoice_number'],
        ], [
            'invoice_date' => $this->data['invoice_date'],
            'customer_id' => $customerId,
            'due_date' => $this->data['due_date'],
            'currency_code' => $this->data['currency_code'],
            'exchange_rate' => $this->data['exchange_rate'],
            'subject' => $this->data['subject'] ?? null,
            'notes' => $this->data['notes'] ?? null,
            'terms' => $this->data['terms'] ?? null,
            'group_id' => $groupId,
            'package_id' => $this->data['package_id'] ?? null,
        ]);

        // Create or update the product if item name is provided
        $productId = null;
        $product = Product::query()->updateOrCreate(
            [
                'name' => $this->data['item_name'],
            ],
            [
                'description' => $this->data['item_desc'] ?? '',
                'unit_price' => round(($this->data['item_price'] ?? 0) * ($this->data['exchange_rate'] ?? 1), 2),
            ]
        );
        $productId = $product->id;

        // Create or update the invoice item
        $invoice->items()->updateOrCreate(
            [
                'name' => $this->data['item_name'] ?? '',
                'description' => $this->data['item_desc'] ?? '',
            ],
            [
                'product_id' => $productId,
                'quantity' => $this->data['item_qty'] ?? 1,
                'unit_price' => $this->data['item_price'] ?? 0,
            ]
        );

        return $invoice;
    }

    public function fillRecord(): void
    {
        // do nothing - we handle everything in resolveRecord
    }

    protected function afterSave(): void
    {
        // Recalculate the invoice total after saving
        $this->record->refresh();
        $this->record->save();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your invoice import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
