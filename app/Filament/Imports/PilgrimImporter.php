<?php

namespace App\Filament\Imports;

use App\Models\Pilgrim;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class PilgrimImporter extends Importer
{
    protected static ?string $model = Pilgrim::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('fullname')
                ->requiredMapping()
                ->guess(['FullName'])
                ->rules(['required', 'max:255']),
            ImportColumn::make('title')
                ->requiredMapping()
                ->guess(['Title'])
                ->castStateUsing(fn ($state) => match ($state) {
                    'Mr', 'mr' => 'mr',
                    'Mrs', 'mrs' => 'mrs',
                    'Miss', 'miss', 'ms' => 'ms',
                    default => 'mr'
                })
                ->rules(['required', 'max:3']),
            ImportColumn::make('passport_number')
                ->guess(['PPNo'])
                ->rules(['max:255']),
            ImportColumn::make('national_id')
                ->guess(['National ID'])
                ->rules(['max:255']),
            ImportColumn::make('gender')
                ->guess(['Gender'])
                ->requiredMapping()
                ->castStateUsing(fn ($state) => match ($state) {
                    'Male', 'male', 'm' => 'm',
                    'Female', 'female', 'f' => 'f',
                    default => 'm'
                })
                ->rules(['required', 'max:1']),
            ImportColumn::make('birthplace')
                ->guess(['BirthCity'])
                ->rules(['max:255']),
            ImportColumn::make('birthdate')
                ->guess(['BirthDate'])
                ->rules(['date']),
        ];
    }

    public function resolveRecord(): ?Pilgrim
    {
        $pilgrim = Pilgrim::where('national_id', $this->data['national_id'])->first();

        if (! $pilgrim) {
            $pilgrim = Pilgrim::where('passport_number', $this->data['passport_number'])->first();
        }

        return $pilgrim ?? new Pilgrim();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your manifest import has completed and '.number_format($import->successful_rows).' '.str('row')->plural($import->successful_rows).' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' '.number_format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to import.';
        }

        return $body;
    }
}
