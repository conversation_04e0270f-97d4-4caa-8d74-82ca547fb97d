<?php

namespace App\Filament\Imports;

use App\Models\Bill;
use App\Models\Finance\Product;
use App\Models\Group;
use App\Models\Vendor;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class BillImporter extends Importer
{
    const VENDOR_NAMES = [
        // Add any vendor name mappings here if needed
    ];

    protected static ?string $model = Bill::class;

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('bill_number')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Bill Number']),

            ImportColumn::make('bill_date')
                ->requiredMapping()
                ->rules(['date'])
                ->guess(['Bill Date']),

            ImportColumn::make('due_date')
                ->requiredMapping()
                ->rules(['date'])
                ->guess(['Due Date']),

            ImportColumn::make('vendor_id')
                ->label('Vendor ID')
                ->numeric()
                ->guess(['Vendor ID']),

            ImportColumn::make('vendor_name')
                ->label('Vendor Name')
                ->guess(['Vendor Name']),

            ImportColumn::make('order_id')
                ->label('Order ID')
                ->numeric()
                ->guess(['Order ID']),

            ImportColumn::make('order_number')
                ->label('Order Number')
                ->guess(['Order Number']),

            ImportColumn::make('currency_code')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Currency Code']),

            ImportColumn::make('exchange_rate')
                ->requiredMapping()
                ->rules(['required'])
                ->numeric()
                ->guess(['Exchange Rate']),

            ImportColumn::make('subject')
                ->guess(['Subject']),

            ImportColumn::make('notes')
                ->guess(['Notes']),

            // Item related columns
            ImportColumn::make('group_id')
                ->label('Group ID')
                ->numeric()
                ->guess(['Group ID']),

            ImportColumn::make('group_name')
                ->label('Group Name')
                ->guess(['Group Name']),

            ImportColumn::make('item_name')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Item Name']),

            ImportColumn::make('item_desc')
                ->label('Item Description')
                ->requiredMapping()
                ->rules(['required'])
                ->guess(['Item Desc']),

            ImportColumn::make('item_qty')
                ->label('Quantity')
                ->requiredMapping()
                ->rules(['required'])
                ->numeric()
                ->guess(['Quantity']),

            ImportColumn::make('item_price')
                ->label('Item Price')
                ->requiredMapping()
                ->rules(['required'])
                ->numeric()
                ->guess(['Item Price']),

            ImportColumn::make('item_vat')
                ->label('VAT %')
                ->numeric()
                ->guess(['VAT %']),
        ];
    }

    public function resolveRecord(): ?Bill
    {
        // Determine vendor_id - prioritize direct ID if provided
        $vendorId = null;
        if (! empty($this->data['vendor_id'])) {
            $vendor = Vendor::query()->find($this->data['vendor_id']);
            if ($vendor) {
                $vendorId = $vendor->id;
            }
        }

        // If no vendor_id provided or found, try to find or create by name
        if (! $vendorId && ! empty($this->data['vendor_name'])) {
            $vendorName = $this->data['vendor_name'];

            if (blank($vendorName)) {
                $vendorId = 1;
            } else {
                $vendor = Vendor::query()
                    ->whereIn('company_name', [static::VENDOR_NAMES[$vendorName] ?? $vendorName, $vendorName])
                    ->first();

                if ($vendor) {
                    $vendor->update(['company_name' => $vendorName]);
                    $vendorId = $vendor->id;
                } else {
                    $vendor = Vendor::query()->create(['company_name' => $vendorName]);
                    $vendorId = $vendor->id;
                }
            }
        }

        // Default to vendor ID 1 if nothing found
        $vendorId = $vendorId ?? 1;

        // Determine group_id - prioritize direct ID if provided
        $groupId = null;
        if (! empty($this->data['group_id'])) {
            $group = Group::query()->find($this->data['group_id']);
            if ($group) {
                $groupId = $group->id;
            }
        }

        // If no group_id provided or found, try to find by name
        if (! $groupId && ! empty($this->data['group_name'])) {
            $groupName = $this->data['group_name'];

            if (! blank($groupName)) {
                $group = Group::query()
                    ->where('name', $groupName)
                    ->first();

                if ($group) {
                    $groupId = $group->id;
                }
            }
        }

        /** @var Bill */
        $bill = Bill::query()->firstOrCreate([
            'bill_number' => $this->data['bill_number'],
        ], [
            'bill_date' => $this->data['bill_date'],
            'vendor_id' => $vendorId,
            'due_date' => $this->data['due_date'],
            'currency_code' => $this->data['currency_code'],
            'exchange_rate' => $this->data['exchange_rate'],
            'subject' => $this->data['subject'] ?? null,
            'notes' => $this->data['notes'] ?? null,
            'order_id' => $this->data['order_id'] ?? null,
        ]);

        // Create or update the product if item name is provided
        $productId = null;
        if (! empty($this->data['item_name'])) {
            $product = Product::query()->updateOrCreate(
                [
                    'name' => $this->data['item_name'],
                ],
                [
                    'description' => $this->data['item_desc'] ?? '',
                    'unit_price' => round(($this->data['item_price'] ?? 0) * ($this->data['exchange_rate'] ?? 1), 2),
                ]
            );
            $productId = $product->id;
        }

        // Create or update the bill item
        if (! empty($this->data['item_name'])) {
            $bill->items()->updateOrCreate(
                [
                    'name' => $this->data['item_name'] ?? '',
                    'description' => $this->data['item_desc'] ?? '',
                ],
                [
                    'product_id' => $productId,
                    'group_id' => $groupId,
                    'quantity' => $this->data['item_qty'] ?? 1,
                    'unit_price' => $this->data['item_price'] ?? 0,
                    'vat' => $this->data['item_vat'] ?? 0,
                ]
            );
        }

        return $bill;
    }

    public function fillRecord(): void
    {
        // do nothing - we handle everything in resolveRecord
    }

    protected function afterSave(): void
    {
        // Recalculate the bill total after saving
        $this->record->refresh();
        $this->record->save();
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your bill import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
