<?php

namespace App\Filament\Imports;

use App\Enums\Finance\AccountCategory;
use App\Models\Finance\CashAccount;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class InvoicePaymentImporter extends Importer
{
    protected static ?string $model = InvoicePayment::class;

    const ACCOUNT_CODES = [
        'BSI IDR' => '11201',
        'MANDIRI' => '11202',
        'BCA' => '11203',
    ];

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('ref_id')
                ->label('Reference ID')
                ->requiredMapping()
                ->guess(['Payment Number']),
            ImportColumn::make('invoice_number')
                ->requiredMapping()
                ->fillRecordUsing(function ($record, $state) {
                    $record->invoice_id = Invoice::query()
                        ->where('invoice_number', $state)
                        ->value('id') ?? 0;
                })
                ->guess(['Invoice Number']),
            ImportColumn::make('paid_at')
                ->label('Date')
                ->requiredMapping()
                ->guess(['Date']),
            ImportColumn::make('account_name')
                ->requiredMapping()
                ->fillRecordUsing(function () {})
                ->guess(['Deposit To']),
            ImportColumn::make('account_code')
                ->requiredMapping()
                ->castStateUsing(fn ($state) => static::ACCOUNT_CODES[$state] ?? $state)
                ->fillRecordUsing(function ($record, $state) {
                    $record->cash_account_id = CashAccount::query()
                        ->where('code', $state)
                        ->value('id') ?? null;
                })
                ->guess(['Deposit To Account Code']),
            ImportColumn::make('amount')
                ->requiredMapping()
                ->numeric()
                ->guess(['Amount']),
            ImportColumn::make('currency_code')
                ->requiredMapping()
                ->guess(['Currency Code']),
            ImportColumn::make('exchange_rate')
                ->requiredMapping()
                ->guess(['Exchange Rate']),
            ImportColumn::make('description')
                ->requiredMapping()
                ->guess(['Description']),
        ];
    }

    public function resolveRecord(): ?InvoicePayment
    {
        CashAccount::query()->firstOrCreate([
            'code' => $this->data['account_code'],
        ], [
            'name' => $this->data['account_name'],
            'category' => AccountCategory::Bank,
        ]);

        return InvoicePayment::query()->firstOrNew([
            'ref_id' => $this->data['ref_id'],
        ]);
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your invoice payment import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
