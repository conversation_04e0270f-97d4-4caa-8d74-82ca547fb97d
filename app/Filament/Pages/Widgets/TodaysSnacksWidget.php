<?php

namespace App\Filament\Pages\Widgets;

use App\Enums\UserRole;
use APp\Filament\Actions\WhatsAppTableAction;
use App\Filament\Resources\GroupResource;
use App\Filament\Tables\Columns\WhatsAppColumn;
use App\Models\Itinerary;
use App\Models\User;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class TodaysSnacksWidget extends BaseWidget
{
    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $heading = 'Snacks';

    public $date;

    public static function canView(): bool
    {
        return auth()->user()->can('groups.view') || auth()->user()->hasRole(['Admin', 'Operator', 'Snack Handler']);
    }

    protected function getTableQuery(): Builder
    {
        /** @var User */
        $user = auth()->user();

        return Itinerary::query()
            ->with(['snack_handler', 'group.customer', 'group.hotels', 'group.mutawif', 'group.mutawif_2', 'group.mutawif_3'])
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->where('has_snack', true)
            ->when($user->hasExactRoles(UserRole::SnackHandler->value), function ($query) use ($user) {
                $snackVendorIds = $user->vendors()->pluck('id');
                $query->whereIn('snack_handler_id', $snackVendorIds);
            })
            ->whereDate('date', $this->date)
            ->orderBy('date');
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No schedules';
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('date')
                ->badge()
                ->label('Time')
                ->color('primary')
                ->time('H:i'),
            Tables\Columns\TextColumn::make('group.total_pax')
                ->wrapHeader()
                ->alignCenter()
                ->label('Total Pax'),
            Tables\Columns\TextColumn::make('details')
                ->getStateUsing(fn ($record) => $record->location)
                ->description(fn ($record) => $record->description)
                ->wrap(),
            Tables\Columns\TextColumn::make('snack_details')
                ->label('Snack Details')
                ->wrapHeader()
                ->default('')
                ->description(fn ($record) => $record->city, 'above'),
            Tables\Columns\TextColumn::make('hotels')
                ->badge()
                ->label('Hotels')
                ->getStateUsing(
                    fn ($record) => $record->group->hotels->map(fn ($h) => $h->fullname)->toArray()
                )
                ->listWithLineBreaks(),
            Tables\Columns\TextColumn::make('group')
                ->label('Group')
                ->getStateUsing(fn ($record) => $record->group->customer?->name ?? '')
                ->description(fn ($record) => $record->group->name)
                ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group])),
            Tables\Columns\TextColumn::make('snack_handler.company_name')
                ->label('Handler'),
            // WhatsAppColumn::make('mutawif')
            //     ->contactName(fn ($record) => $record->group->mutawif?->name)
            //     ->contactPhone(fn ($record) => $record->group->mutawif?->phone),
        ];
    }

    protected function getTableActions(): array
    {
        return [
            WhatsAppTableAction::make('mutawif')
                ->phone(fn ($record) => $record->group->mutawif?->phone ?? '')
                ->tooltip(fn ($record) => $record->group->mutawif?->name ?? null)
                ->visible(fn ($record) => (bool) $record->group->mutawif),
            WhatsAppTableAction::make('mutawif_2')
                ->phone(fn ($record) => $record->group->mutawif_2?->phone ?? '')
                ->tooltip(fn ($record) => $record->group->mutawif_2?->name ?? null)
                ->visible(fn ($record) => (bool) $record->group->mutawif_2),
            WhatsAppTableAction::make('mutawif_3')
                ->phone(fn ($record) => $record->group->mutawif_3?->phone ?? '')
                ->tooltip(fn ($record) => $record->group->mutawif_3?->name ?? null)
                ->visible(fn ($record) => (bool) $record->group->mutawif_3),
        ];
    }
}
