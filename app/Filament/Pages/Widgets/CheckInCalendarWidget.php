<?php

namespace App\Filament\Pages\Widgets;

use App\Filament\Resources\GroupResource;
use App\Models\Group;
use App\Models\GroupHotel;
use App\Models\Itinerary;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Infolists;
use Saade\FilamentFullCalendar\Actions\ViewAction;
use Saade\FilamentFullCalendar\Data\EventData;
use Saade\FilamentFullCalendar\Widgets\FullCalendarWidget;

class CheckInCalendarWidget extends FullCalendarWidget
{
    protected string $key = 'schedule';

    protected function headerActions(): array
    {
        return [
        ];
    }

    protected function modalActions(): array
    {
        return [
            Actions\Action::make('view_pif')
                ->label('View PIF')
                ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group_id]))
                ->openUrlInNewTab(),
        ];
    }

    protected function viewAction(): Action
    {
        return ViewAction::make()
            ->infolist(function ($record) {
                if ($record instanceof Itinerary) {
                    return [
                        Infolists\Components\TextEntry::make('event')
                            ->getStateUsing(fn ($record) => $record->is_arrival ? '[Arrival] '.$record->location : '['.$record->location.'] '.$record->description),
                        Infolists\Components\TextEntry::make('group')
                            ->getStateUsing(fn ($record) => $record->group->full_name),
                    ];
                }

                return [
                    Infolists\Components\TextEntry::make('event')
                        ->getStateUsing(fn ($record) => '[Check In] '.$record->hotel->fullname),
                    Infolists\Components\TextEntry::make('group')
                        ->getStateUsing(fn ($record) => $record->group->full_name),
                ];
            })
            ->modalHeading('View Event')
            ->modalWidth('sm');
    }

    public function fetchEvents(array $fetchInfo): array
    {
        $start = Carbon::parse($fetchInfo['start'])->setTime(0, 0);
        $end = Carbon::parse($fetchInfo['end'])->setTime(23, 59, 59);

        $data = Itinerary::query()
            ->with(['group.customer'])
            ->whereHas('group', function ($q) {
                $q->confirmed();
            })
            ->whereBetween('date', [$start, $end])
            ->where('is_arrival', true)->get()
            ->map(function (Itinerary $itinerary) {
                return EventData::make()
                    ->id('arrival.'.$itinerary->id)
                    ->title('[Arrival] '.$itinerary->location.' | '.$itinerary->group->full_name)
                    ->start($itinerary->date->toDateTimeString())
                    // ->url(GroupResource::getUrl('view', ['record' => $itinerary->group]), true)
                    ->extraProperties([
                        'model' => Itinerary::class,
                        'model_id' => $itinerary->id,
                    ]);
            })->toArray();

        $data = array_merge(
            $data,
            Itinerary::query()
                ->with(['group.customer'])
                ->whereHas('group', function ($q) {
                    $q->confirmed();
                })
                ->whereBetween('date', [$start, $end])
                ->where('is_arrival', false)->get()
                ->map(function (Itinerary $itinerary) {
                    return EventData::make()
                        ->id('itinerary.'.$itinerary->id)
                        ->title('['.$itinerary->location.'] '.$itinerary->description.' | '.$itinerary->group->full_name)
                        ->start($itinerary->date->toDateTimeString())
                        // ->url(GroupResource::getUrl('view', ['record' => $itinerary->group]), true)
                        ->extraProperties([
                            'model' => Itinerary::class,
                            'model_id' => $itinerary->id,
                        ]);
                })->toArray()
        );

        $data = array_merge(
            $data,
            GroupHotel::query()
                ->with(['group.customer', 'hotel'])
                ->whereHas('group', function ($q) {
                    $q->confirmed();
                })
                ->whereBetween('check_in', [$start, $end])->get()
                ->map(function (GroupHotel $hotel) {
                    return EventData::make()
                        ->id('check-in.'.$hotel->id)
                        ->title('[Check In] '.$hotel->hotel->fullname.' | '.$hotel->group->full_name)
                        ->start($hotel->check_in->toDateTimeString())
                        // ->url(GroupResource::getUrl('view', ['record' => $hotel->group]), true)
                        ->extraProperties([
                            'model' => GroupHotel::class,
                            'model_id' => $hotel->id,
                        ]);
                })->toArray()
        );

        return $data;
    }

    public function onEventClick(array $event): void
    {
        $this->model = $event['extendedProps']['model'];
        $this->record = $this->model::with(['group'])->find($event['extendedProps']['model_id'] ?? 0);

        $this->mountAction('view', [
            'type' => 'click',
            'event' => $event,
        ]);
    }
}
