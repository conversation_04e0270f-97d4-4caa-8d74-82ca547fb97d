<?php

namespace App\Filament\Pages\Widgets;

use App\Models\Group;
use Filament\Widgets\Widget;

class TopServicesTable extends Widget
{
    protected static ?string $heading = 'Top Services';

    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    protected static string $view = 'filament.widgets.statistics.top-services-table';

    protected function getViewData(): array
    {
        $services = [];
        Group::query()
            ->currentPeriod(true)
            ->get()
            ->each(function ($group) use (&$services) {
                if (count(array_intersect(['handling', 'visa', 'hotel'], $group->services)) == 3) {
                    $services['Full LA'] = [
                        ...$services['Full LA'] ?? [],
                        'name' => 'Full LA',
                        'key' => 'full_la',
                        'groups_count' => ($services['Full LA']['groups_count'] ?? 0) + 1,
                        'total_pax' => ($services['Full LA']['total_pax'] ?? 0) + $group->total_pax,
                    ];
                }
                foreach ($group->services as $service) {
                    $services[$service] = [
                        ...$services[$service] ?? [],
                        'name' => Group::GROUP_SERVICES[$service] ?? $service,
                        'key' => $service,
                        'groups_count' => ($services[$service]['groups_count'] ?? 0) + 1,
                        'total_pax' => ($services[$service]['total_pax'] ?? 0) + $group->total_pax,
                    ];
                }
            });

        return [
            'services' => collect($services)->sortByDesc('groups_count'),
        ];
    }
}
