<?php

namespace App\Filament\Pages\Widgets;

use App\Models\Institution;
use Filament\Support\Colors\Color;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class TopInstitutionsChart extends ChartWidget
{
    protected static ?string $heading = 'Top Muassasahs';

    protected int|string|array $columnSpan = 'full';

    protected static ?string $pollingInterval = null;

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array|RawJs|null
    {
        return [
            'indexAxis' => 'y',
        ];
    }

    protected function getData(): array
    {
        $period = current_period();
        $institutions = Institution::query()
            ->leftJoin('groups', 'institutions.id', '=', 'groups.institution_id')
            ->whereBetween('groups.arrival_date', [
                $period->date_start->format('Y-m-d').' 00:00:00',
                $period->date_end->format('Y-m-d').' 23:59:59',
            ])
            ->select([
                'institutions.id',
                'institutions.name',
                DB::raw('count(`groups`.`id`) AS groups_count'),
                DB::raw('sum(`groups`.`total_pax`) AS pax_count'),
            ])
            ->groupBy(['institutions.id', 'institutions.name'])
            ->orderByDesc('pax_count')
            ->limit(10)
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Groups',
                    'data' => $institutions->pluck('groups_count'),
                    'backgroundColor' => 'rgb('.Color::Blue[500].')',
                    'borderColor' => 'transparent',
                ],
                [
                    'label' => 'Total Pax',
                    'data' => $institutions->pluck('pax_count'),
                    'backgroundColor' => 'rgb('.Color::Amber[500].')',
                    'borderColor' => 'transparent',
                ],
            ],
            'labels' => $institutions->pluck('name'),
        ];
    }
}
