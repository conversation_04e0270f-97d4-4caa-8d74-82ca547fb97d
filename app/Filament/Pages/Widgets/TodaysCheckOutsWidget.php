<?php

namespace App\Filament\Pages\Widgets;

use App\Casts\SerializedArray;
use App\Filament\Resources\GroupResource;
use App\Filament\Resources\HotelBrokerResource;
use App\Filament\Tables\Columns\WhatsAppColumn;
use App\Models\GroupHotel;
use App\Models\Hotel;
use App\Models\Vendors\HotelBroker;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class TodaysCheckOutsWidget extends BaseWidget
{
    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $heading = 'Check Outs';

    public $date;

    public static function canView(): bool
    {
        return auth()->user()->can('groups.view') || auth()->user()->hasRole(['Admin', 'Operator']);
    }

    protected function getTableQuery(): Builder
    {
        return GroupHotel::query()
            ->with(['group.customer', 'hotel', 'broker'])
            ->leftJoin('metas', function (JoinClause $join) {
                $join->on('metas.metable_id', '=', 'group_hotel.id')
                    ->where('metas.metable_type', 'group_hotel')
                    ->where('metas.key', 'contacts');
            })
            ->select([
                'group_hotel.*',
                'metas.value AS contacts',
            ])
            ->withCasts([
                'contacts' => SerializedArray::class,
            ])
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->whereDate('check_out', '=', $this->date);
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'No check-outs';
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('hotel.name')
                ->label('Hotel')
                ->description(fn ($record) => $record->hotel?->city, 'above'),
            Tables\Columns\TextColumn::make('group.customer.name')
                ->description(fn ($record) => $record->group?->name)
                ->label('Group')
                ->url(fn ($record) => GroupResource::getUrl('view', ['record' => $record->group_id])),
            Tables\Columns\TextColumn::make('services')
                ->label('Services')
                ->badge()
                ->getStateUsing(fn ($record) => $record->group->getServices())
                ->color('primary')
                ->toggleable(),
            Tables\Columns\TextColumn::make('broker.contact_name')
                ->label('Broker')
                ->description(fn ($record) => $record->broker?->company_name ?? '', 'above')
                ->url(fn ($record) => $record->broker_id ? HotelBrokerResource::getUrl('view', ['record' => $record->broker_id]) : null),
            WhatsAppColumn::make('contact')
                ->contactName(fn ($record) => $record->contacts[0]['name'] ?? null)
                ->contactPhone(fn ($record) => $record->contacts[0]['phone'] ?? null)
                ->toggleable()
                ->toggledHiddenByDefault(),
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            Tables\Filters\SelectFilter::make('hotel_city')
                ->label('City')
                ->options(Hotel::getCities())
                ->query(fn ($query, $data) => $query->when($data['value'], fn ($query, $city) => $query->whereHas('hotel', fn ($query) => $query->where('city', $city)))
                ),
        ];
    }

    protected function getTableActions(): array
    {
        $contacts = DB::table('metas')
            ->where('metable_type', 'group_hotel')
            ->where('key', 'contacts')
            ->pluck('value')
            ->transform(fn ($v) => maybe_unserialize($v))
            ->transform(fn ($c) => $c[0])
            ->mapWithKeys(fn ($c) => [$c['name'] => $c['phone']])
            ->toArray();

        return [
            Tables\Actions\ActionGroup::make([
                Tables\Actions\EditAction::make()
                    ->form([
                        Forms\Components\Select::make('broker_id')
                            ->label('Broker')
                            ->searchable()
                            ->getSearchResultsUsing(fn ($search) => HotelBroker::query()
                                ->where('company_name', 'like', "%{$search}%")
                                ->orWhere('contact_name', 'like', "%{$search}%")
                                ->get()
                                ->pluck('full_name', 'id'))
                            ->getOptionLabelUsing(fn ($value) => HotelBroker::find($value)?->full_name)
                            ->preload(),
                    ])
                    ->modalWidth('lg'),
                Tables\Actions\Action::make('set_contact')
                    ->color('gray')
                    ->action(function ($record, $data) {
                        collect($data)->each(fn ($v, $k) => $record->setMeta($k, $v));
                    })
                    ->form([
                        TableRepeater::make('contacts')
                            ->hiddenLabel()
                            ->reorderable(false)
                            ->headers([
                                Header::make('Name'),
                                Header::make('Phone'),
                            ])
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->datalist(array_keys($contacts))
                                    ->reactive()
                                    ->afterStateUpdated(function ($set, $state) use ($contacts) {
                                        if (isset($contacts[$state])) {
                                            $set('phone', $contacts[$state]);
                                        }
                                    }),
                                Forms\Components\TextInput::make('phone'),
                            ])
                            ->minItems(1)
                            ->defaultItems(1)
                            ->maxItems(1)
                            ->addActionLabel('Add contact'),
                    ])
                    ->mountUsing(fn ($form, $record) => $form->fill([
                        'contacts' => $record->contacts,
                    ]))
                    ->modalWidth('md'),
            ]),
        ];
    }
}
