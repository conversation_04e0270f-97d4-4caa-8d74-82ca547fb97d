<?php

namespace App\Filament\Pages\Widgets;

use App\Models\Customer;
use Filament\Support\Colors\Color;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class TopCustomersChart extends ChartWidget
{
    protected static ?string $heading = 'Top Customers';

    protected int|string|array $columnSpan = 'full';

    protected static ?string $pollingInterval = null;

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array|RawJs|null
    {
        return [
            'indexAxis' => 'y',
        ];
    }

    protected function getData(): array
    {
        $period = current_period();
        $customers = Customer::query()
            ->leftJoin('groups', 'customers.id', '=', 'groups.customer_id')
            ->whereBetween('groups.arrival_date', [
                $period->date_start->format('Y-m-d').' 00:00:00',
                $period->date_end->format('Y-m-d').' 23:59:59',
            ])
            ->select([
                'customers.id',
                'customers.name',
                DB::raw('count(`groups`.`id`) AS groups_count'),
                DB::raw('sum(`groups`.`total_pax`) AS pax_count'),
            ])
            ->groupBy(['customers.id', 'customers.name'])
            ->orderByDesc('pax_count')
            ->limit(10)
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Groups',
                    'data' => $customers->pluck('groups_count'),
                    'backgroundColor' => 'rgb('.Color::Blue[500].')',
                    'borderColor' => 'transparent',
                ],
                [
                    'label' => 'Total Pax',
                    'data' => $customers->pluck('pax_count'),
                    'backgroundColor' => 'rgb('.Color::Amber[500].')',
                    'borderColor' => 'transparent',
                ],
            ],
            'labels' => $customers->pluck('name'),
        ];
    }
}
