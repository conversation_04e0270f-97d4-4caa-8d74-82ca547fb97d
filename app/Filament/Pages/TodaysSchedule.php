<?php

namespace App\Filament\Pages;

use App\Filament\Pages\Widgets\TodaysArrivalsWidget;
use App\Filament\Pages\Widgets\TodaysCheckInsWidget;
use App\Filament\Pages\Widgets\TodaysCheckOutsWidget;
use App\Filament\Pages\Widgets\TodaysDeparturesWidget;
use App\Filament\Pages\Widgets\TodaysItinerariesWidget;
use App\Filament\Pages\Widgets\TodaysSnacksWidget;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms;
use Filament\Pages\Page;
use Livewire\Attributes\Url;

class TodaysSchedule extends Page
{
    protected static ?string $navigationGroup = 'Schedules';

    protected static string $view = 'filament.pages.todays-schedule';

    protected static ?string $title = "Today's Schedule";

    protected static ?int $navigationSort = 2;

    #[Url(history: true, keep: true)]
    public $date;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->can('groups.view') || auth()->user()->hasRole(['Admin', 'Operator', 'Snack Handler', 'Mutawif']);
    }

    public function mount()
    {
        abort_unless(self::shouldRegisterNavigation(), 403);

        if (! $this->date) {
            $this->date = today()->format('Y-m-d');
        }
    }

    public function getTitle(): string
    {
        $today = Carbon::parse($this->date);

        return self::$title . ' (' . $today->toFormattedDayDateString() . ')';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('pick_date')
                ->label('Pick a date')
                ->icon('heroicon-o-calendar-days')
                ->color('gray')
                ->form([
                    Forms\Components\DatePicker::make('date')
                        ->required()
                        ->default(Carbon::now()),
                ])
                ->modalWidth('sm')
                ->modalSubmitActionLabel('Show')
                ->action(function ($data) {
                    return redirect(static::getUrl([
                        'date' => Carbon::parse($data['date'])->format('Y-m-d'),
                    ]));
                }),
            Actions\Action::make('yesterday')
                ->hiddenLabel()
                ->icon('heroicon-o-chevron-left')
                ->url(static::getUrl([
                    'date' => Carbon::parse($this->date)->subDay()->format('Y-m-d'),
                ])),
            Actions\Action::make('tomorrow')
                ->hiddenLabel()
                ->icon('heroicon-o-chevron-right')
                ->url(static::getUrl([
                    'date' => Carbon::parse($this->date)->addDay()->format('Y-m-d'),
                ])),
        ];
    }

    public function getWidgetData(): array
    {
        return ['date' => $this->date];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            TodaysArrivalsWidget::class,
            TodaysDeparturesWidget::class,
            TodaysCheckInsWidget::class,
            TodaysCheckOutsWidget::class,
            TodaysItinerariesWidget::class,
            TodaysSnacksWidget::class,
        ];
    }
}
