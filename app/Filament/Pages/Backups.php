<?php

namespace App\Filament\Pages;

use ShuvroRoy\FilamentSpatieLaravelBackup\Pages\Backups as PagesBackups;

class Backups extends PagesBackups
{
    protected static ?string $navigationIcon = 'heroicon-o-archive-box';

    public static function getNavigationGroup(): ?string
    {
        return 'System';
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole('Admin');
    }

    public function mount(): void
    {
        abort_unless(auth()->user()->hasRole('Admin'), 403);
    }
}
