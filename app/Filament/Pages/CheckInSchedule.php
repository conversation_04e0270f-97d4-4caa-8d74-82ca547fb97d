<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class CheckInSchedule extends Page
{
    protected static ?string $navigationGroup = 'Schedules';

    protected static string $view = 'filament.pages.check-in-schedule';

    protected static ?int $navigationSort = 1;

    protected static ?string $title = 'Calendar';

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole(['Admin', 'Operator']) || auth()->user()->can('widget.schedule-calendar');
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\CheckInCalendarWidget::class,
        ];
    }
}
