<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\GroupResource;
use App\Filament\Resources\HotelBrokerResource;
use App\Models\GroupHotel;
use App\Models\User;
use Closure;
use Filament\Tables;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class CheckInScheduleWidget extends BaseWidget
{
    protected static bool $isLazy = false;

    protected int | string | array $columnSpan = 'full';

    public static function canView(): bool
    {
        /** @var User * */
        $user = auth()->user();

        return $user->can('widget.check-ins') || $user->hasRole(['Admin', 'Operator', 'Customer']);
    }

    protected function getTableQuery(): Builder
    {
        /** @var User * */
        $user = auth()->user();

        return GroupHotel::query()
            ->with(['group.customer', 'hotel'])
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->when($user->hasExactRoles('Customer'), function ($query) use ($user) {
                $query->whereHas('group', function ($query) use ($user) {
                    $query->whereHas('customer', fn ($query) => $query->where('user_id', $user->id));
                });
            })
            ->whereDate('check_in', '>=', today());
    }

    protected function getTableRecordUrlUsing(): ?Closure
    {
        return function (GroupHotel $record) {
            return GroupResource::getUrl('view', ['record' => $record->group_id]);
        };
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('check_in')
                ->date()
                ->label('Check In')
                ->sortable(),
            Tables\Columns\TextColumn::make('hotel.name')
                ->label('Hotel')
                ->description(fn ($record) => $record->hotel?->city, 'above'),
            Tables\Columns\TextColumn::make('group.customer.name')
                ->description(fn ($record) => $record->group->name)
                ->label('Group'),
            Tables\Columns\TextColumn::make('group.services')
                ->label('Services')
                ->badge()
                ->getStateUsing(fn ($record) => $record->group->getServices())
                ->toggleable(),
            Tables\Columns\TextColumn::make('broker.contact_name')
                ->label('Broker')
                ->description(fn ($record) => $record->broker?->company_name ?? '', 'above')
                ->url(fn ($record) => $record->broker_id ? HotelBrokerResource::getUrl('view', ['record' => $record->broker_id]) : null)
                ->visible(fn () => ! auth()->user()->hasExactRoles('Customer')),
            Tables\Columns\IconColumn::make('is_confirmed')
                ->boolean()
                ->label('Confirmation')
                ->view('filament.tables.columns.hotel-booking-hotel-confirmation')
                ->disabledClick(),
        ];
    }

    protected function getDefaultTableSortColumn(): ?string
    {
        return 'check_in';
    }

    public function getDefaultTableRecordsPerPageSelectOption(): int
    {
        return 5;
    }

    protected function getTableRecordsPerPageSelectOptions(): ?array
    {
        return [5, 10, 25, 50];
    }
}
