<?php

namespace App\Filament\Widgets\Dashboard;

use App\Models\Customer;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CustomerStatsOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected static ?int $sort = -2;

    protected int|string|array $columnSpan = 'full';

    protected $customer;

    public function mount()
    {
        $this->customer = Customer::query()
            ->where('user_id', auth()->user()->id)->first();
    }

    public static function canView(): bool
    {
        return auth()->user()->hasRole(['Customer']) && Customer::query()->where('user_id', auth()->user()->id)->exists();
    }

    protected function getStats(): array
    {
        if (! $this->customer) {
            return [];
        }

        return [
            Stat::make('Total groups', $this->getTotalGroups()),
            Stat::make('Total pax', $this->getTotalPax()),
        ];
    }

    protected function getColumns(): int
    {
        return 2;
    }

    private function getTotalGroups()
    {
        return $this->customer?->groups()
            ->currentPeriod()
            ->confirmed()
            ->count();
    }

    private function getTotalPax()
    {
        return $this->customer?->groups()
            ->currentPeriod()
            ->confirmed()
            ->sum('total_pax');
    }
}
