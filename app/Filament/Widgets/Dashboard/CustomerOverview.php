<?php

namespace App\Filament\Widgets\Dashboard;

use App\Models\Customer;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Widgets\Widget;

class CustomerOverview extends Widget implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    protected static ?string $pollingInterval = null;

    protected static string $view = 'filament.widgets.dashboard.customer-overview';

    protected static ?int $sort = -1;

    protected int|string|array $columnSpan = 'full';

    public static function canView(): bool
    {
        return auth()->user()->hasRole(['Customer']) && Customer::query()->where('user_id', auth()->user()->id)->exists();
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record(Customer::where('user_id', auth()->user()->id)->first())
            ->schema([
                InfoLists\Components\Group::make()
                    ->schema([
                        InfoLists\Components\TextEntry::make('name')
                            ->label('Company name'),
                        InfoLists\Components\TextEntry::make('owner_name'),
                        InfoLists\Components\TextEntry::make('email'),
                        InfoLists\Components\TextEntry::make('phone'),
                        InfoLists\Components\TextEntry::make('permit_no')
                            ->label('No. Izin PPIU'),
                        InfoLists\Components\TextEntry::make('region')
                            ->label('Domisili'),
                        InfoLists\Components\TextEntry::make('address'),
                    ])
                    ->columns(['sm' => 2])
                    ->columnSpan(['md' => 2]),
                InfoLists\Components\Group::make()
                    ->schema([
                        InfoLists\Components\ImageEntry::make('logo'),
                    ])
                    ->columnSpan(1),
            ])
            ->columns(['md' => 3]);
    }
}
