<?php

namespace App\Filament\Finance\Widgets;

use App\Models\Finance\Invoice;
use Filament\Support\Colors\Color;
use Filament\Widgets\ChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Illuminate\Support\Carbon;

class MonthlySales extends ChartWidget
{
    protected static ?string $pollingInterval = null;

    protected static ?string $heading = 'Sales per Month';

    protected int|string|array $columnSpan = 'full';

    protected static ?array $options = [
        'responsive' => true,
        'stacked' => false,
        'interaction' => [
            'mode' => 'index',
            'intersect' => false,
        ],
        'scales' => [
            'x' => [
                'display' => true,
            ],
            'y' => [
                'display' => true,
                'position' => 'left',
                'title' => [
                    'display' => true,
                    'text' => 'Sales (SAR)',
                ],
            ],
            'y1' => [
                'display' => true,
                'position' => 'right',
                'title' => [
                    'display' => true,
                    'text' => 'Invoices',
                ],
                'border' => [
                    'display' => false,
                ],
                'grid' => [
                    'display' => false,
                    'drawOnChartArea' => false,
                    'drawTicks' => false,
                ],
            ],
        ],
    ];

    protected function getData(): array
    {
        $period = current_period();

        $invoices = Trend::model(Invoice::class)
            ->between(
                $period->date_start,
                $period->date_end,
            )
            ->dateColumn('invoice_date')
            ->perMonth()
            ->count();
        $sales = Trend::model(Invoice::class)
            ->between(
                $period->date_start,
                $period->date_end,
            )
            ->dateColumn('invoice_date')
            ->perMonth()
            ->sum('total * exchange_rate');

        return [
            'datasets' => [
                [
                    'label' => 'Invoices',
                    'data' => $invoices->map(fn (TrendValue $value) => $value->aggregate),
                    'pointBackgroundColor' => 'rgb(' . Color::Blue[500] . ')',
                    'borderColor' => 'rgb(' . Color::Blue[500] . ')',
                    'tension' => 0.25,
                    'yAxisID' => 'y1',
                ],
                [
                    'label' => 'Sales (SAR)',
                    'data' => $sales->map(fn (TrendValue $value) => $value->aggregate),
                    'pointBackgroundColor' => 'rgb(' . Color::Green[500] . ')',
                    'borderColor' => 'rgb(' . Color::Green[500] . ')',
                    'tension' => 0.25,
                    'yAxisID' => 'y',
                ],
            ],
            'labels' => $sales->map(fn (TrendValue $value) => Carbon::createFromFormat('Y-m-d', $value->date . '-01')->format('M Y')),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
