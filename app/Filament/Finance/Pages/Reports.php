<?php

namespace App\Filament\Finance\Pages;

use Filament\Pages\Page;

class Reports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-duplicate';

    protected static ?string $navigationLabel = 'Laporan-laporan';

    protected static ?string $title = 'Laporan-laporan';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?int $navigationSort = 20;

    protected static string $view = 'filament.finance.pages.reports';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
}
