<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Enums\InvoiceStatus;
use App\Models\Customer;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoicePayment;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class SalesPerformanceReport extends Page
{
    use InteractsWithFormActions, InteractsWithForms;

    protected static ?string $navigationLabel = 'Sales Performance';

    protected static ?string $title = 'Sales Performance Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 40;

    protected static string $view = 'filament.finance.pages.reports.sales-performance-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Select::make('customer_id')
                    ->label('Customer (Optional)')
                    ->options(Customer::pluck('name', 'id'))
                    ->searchable()
                    ->placeholder('All Customers'),
                Select::make('group_by')
                    ->label('Group By')
                    ->options([
                        'month' => 'Month',
                        'customer' => 'Customer',
                        'status' => 'Status',
                    ])
                    ->default('month'),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('downloadPdf')
                ->disabled(fn () => $this->data === null)
                ->color('success'),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Build base query
            $invoiceQuery = Invoice::query()
                ->with(['customer', 'group', 'payments'])
                ->whereBetween('invoice_date', [$start, $end])
                ->where('status', '!=', InvoiceStatus::Cancelled);

            // Apply customer filter if selected
            if (! empty($data['customer_id'])) {
                $invoiceQuery->where('customer_id', $data['customer_id']);
            }

            $invoices = $invoiceQuery->get();

            // Calculate summary metrics
            $summary = $this->calculateSummaryMetrics($invoices);

            // Group data based on selection
            $groupedData = $this->groupSalesData($invoices, $data['group_by'], $start, $end);

            // Get top performing customers
            $topCustomers = $this->getTopCustomers($invoices);

            // Get payment trends
            $paymentTrends = $this->getPaymentTrends($invoices, $start, $end);

            // Get invoice status breakdown
            $statusBreakdown = $this->getStatusBreakdown($invoices);

            $this->data = [
                'summary' => $summary,
                'grouped_data' => $groupedData,
                'top_customers' => $topCustomers,
                'payment_trends' => $paymentTrends,
                'status_breakdown' => $statusBreakdown,
                'group_by' => $data['group_by'],
                'customer_filter' => $data['customer_id'] ? Customer::find($data['customer_id'])?->name : null,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    private function calculateSummaryMetrics($invoices): array
    {
        $totalInvoices = $invoices->count();
        $totalSales = $invoices->sum('total');
        $totalPaid = $invoices->sum('paid');
        $totalOutstanding = $totalSales - $totalPaid;
        $averageInvoiceValue = $totalInvoices > 0 ? $totalSales / $totalInvoices : 0;
        $paymentRate = $totalSales > 0 ? ($totalPaid / $totalSales) * 100 : 0;

        // Count unique customers and groups
        $uniqueCustomers = $invoices->unique('customer_id')->count();
        $uniqueGroups = $invoices->whereNotNull('group_id')->unique('group_id')->count();

        return [
            'total_invoices' => $totalInvoices,
            'total_sales' => $totalSales,
            'total_paid' => $totalPaid,
            'total_outstanding' => $totalOutstanding,
            'average_invoice_value' => $averageInvoiceValue,
            'payment_rate' => $paymentRate,
            'unique_customers' => $uniqueCustomers,
            'unique_groups' => $uniqueGroups,
        ];
    }

    private function groupSalesData($invoices, $groupBy, $start, $end): array
    {
        switch ($groupBy) {
            case 'month':
                return $this->groupByMonth($invoices, $start, $end);
            case 'customer':
                return $this->groupByCustomer($invoices);
            case 'status':
                return $this->groupByStatus($invoices);
            default:
                return [];
        }
    }

    private function groupByMonth($invoices, $start, $end): array
    {
        $months = [];
        $current = $start->copy()->startOfMonth();

        while ($current <= $end) {
            $monthLabel = $current->format('M Y');

            $monthInvoices = $invoices->filter(function ($invoice) use ($current) {
                return $invoice->invoice_date->format('Y-m') === $current->format('Y-m');
            });

            $months[] = [
                'label' => $monthLabel,
                'count' => $monthInvoices->count(),
                'sales' => $monthInvoices->sum('total'),
                'paid' => $monthInvoices->sum('paid'),
                'outstanding' => $monthInvoices->sum('total') - $monthInvoices->sum('paid'),
            ];

            $current->addMonth();
        }

        return $months;
    }

    private function groupByCustomer($invoices): array
    {
        return $invoices->groupBy('customer_id')->map(function ($customerInvoices) {
            $customer = $customerInvoices->first()->customer;

            return [
                'label' => $customer->name,
                'count' => $customerInvoices->count(),
                'sales' => $customerInvoices->sum('total'),
                'paid' => $customerInvoices->sum('paid'),
                'outstanding' => $customerInvoices->sum('total') - $customerInvoices->sum('paid'),
            ];
        })->sortByDesc('sales')->values()->toArray();
    }

    private function groupByStatus($invoices): array
    {
        return $invoices->groupBy('status')->map(function ($statusInvoices, $status) {
            return [
                'label' => $status instanceof InvoiceStatus ? $status->getLabel() : InvoiceStatus::tryFrom($status)?->getLabel() ?? '-',
                'count' => $statusInvoices->count(),
                'sales' => $statusInvoices->sum('total'),
                'paid' => $statusInvoices->sum('paid'),
                'outstanding' => $statusInvoices->sum('total') - $statusInvoices->sum('paid'),
            ];
        })->values()->toArray();
    }

    private function getTopCustomers($invoices): array
    {
        return $invoices->groupBy('customer_id')
            ->map(function ($customerInvoices) {
                $customer = $customerInvoices->first()->customer;

                return [
                    'name' => $customer->name,
                    'sales' => $customerInvoices->sum('total'),
                    'invoices' => $customerInvoices->count(),
                    'payment_rate' => $customerInvoices->sum('total') > 0
                        ? ($customerInvoices->sum('paid') / $customerInvoices->sum('total')) * 100
                        : 0,
                ];
            })
            ->sortByDesc('sales')
            ->take(10)
            ->values()
            ->toArray();
    }

    private function getPaymentTrends($invoices, $start, $end): array
    {
        $payments = InvoicePayment::query()
            ->whereIn('invoice_id', $invoices->pluck('id'))
            ->whereBetween('paid_at', [$start, $end])
            ->get();

        return $payments->groupBy(function ($payment) {
            return $payment->paid_at->format('Y-m');
        })->map(function ($monthPayments, $month) {
            return [
                'month' => Carbon::createFromFormat('Y-m', $month)->format('M Y'),
                'amount' => $monthPayments->sum('amount'),
                'count' => $monthPayments->count(),
            ];
        })->sortBy('month')->values()->toArray();
    }

    private function getStatusBreakdown($invoices): array
    {
        $totalInvoices = $invoices->count();

        return $invoices->groupBy('status')->map(function ($statusInvoices, $status) use ($totalInvoices) {
            return [
                'status' => $status instanceof InvoiceStatus ? $status->getLabel() : InvoiceStatus::tryFrom($status)?->getLabel() ?? '-',
                'count' => $statusInvoices->count(),
                'percentage' => $totalInvoices > 0 ? ($statusInvoices->count() / $totalInvoices) * 100 : 0,
                'sales' => $statusInvoices->sum('total'),
            ];
        })->values()->toArray();
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'sales-performance-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.sales-performance-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }
}
