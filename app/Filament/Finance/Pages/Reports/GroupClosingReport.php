<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Models\Group;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;

class GroupClosingReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'Group Closing Report';

    protected static ?string $title = 'Group Closing Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 0;

    protected static string $view = 'filament.finance.pages.reports.group-closing-report';

    public array $filters = [];

    public ?Group $group = null;

    public ?array $data = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::formFieldSelectGroup(),
            ])
            ->statePath('filters')
            ->columns();
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('downloadPdf')
                ->disabled(fn () => $this->data === null)
                ->color('success'),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $group = Group::find($data['group_id'] ?? null);

        if ($group) {
            $this->group = $group;
            $this->data = $group->getClosingReportData();
        } else {
            $this->group = null;
            $this->data = null;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = "group-closing-report-{$this->group->id}.pdf";

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.group-closing-report', [
                'group' => $this->group,
                'data' => $this->data,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }
}
