<?php

namespace App\Filament\Finance\Pages\Reports;

use App\Contracts\PdfService;
use App\Enums\Finance\AccountCategory;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class GeneralLedgerReport extends Page
{
    use InteractsWithFormActions;

    protected static ?string $navigationLabel = 'General Ledger';

    protected static ?string $title = 'General Ledger Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 70;

    protected static string $view = 'filament.finance.pages.reports.general-ledger-report';

    public array $filters = [];

    public ?array $data = null;

    public ?array $period = null;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->label('Period')
                    ->required()
                    ->maxDate(today())
                    ->startDate(today()->startOfYear())
                    ->endDate(today())
                    ->columnSpan(['lg' => 2]),
                Select::make('currency_code')
                    ->label('Currency')
                    ->options(Currency::getOptions())
                    ->default('SAR'),
                Select::make('account_id')
                    ->label('Account (Optional)')
                    ->placeholder('All Accounts')
                    ->options(function () {
                        return CashAccount::query()
                            ->orderBy('code')
                            ->get()
                            ->mapWithKeys(function ($account) {
                                return [$account->id => $account->code . ' - ' . $account->name];
                            });
                    })
                    ->searchable(),
            ])
            ->statePath('filters')
            ->columns(3);
    }

    protected function getFormActions(): array
    {
        return [
            Actions\Action::make('filter')
                ->label('Generate Report')
                ->submit('filter'),
            Actions\Action::make('download_pdf')
                ->label('Download PDF')
                ->icon('heroicon-o-document-arrow-down')
                ->action('downloadPdf')
                ->disabled(fn () => $this->data === null)
                ->color('success'),
        ];
    }

    public function filter(): void
    {
        $data = $this->form->getState();

        $dates = explode(' - ', $data['date_range'] ?? '');
        if (count($dates) == 2) {
            $start = Carbon::createFromFormat('d/m/Y', $dates[0])->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay();

            $this->period = [
                'start' => $start->format('Y-m-d'),
                'end' => $end->format('Y-m-d'),
            ];

            // Build the query for journal entry items
            $query = JournalEntryItem::query()
                ->join('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->whereBetween('journal_entries.entry_date', [$start, $end]);

            // Filter by specific account if selected
            if (! empty($data['account_id'])) {
                $query->where('journal_entry_items.account_id', $data['account_id']);
            }

            // Get all transactions ordered by account code and entry date
            $transactions = $query
                ->select([
                    'cash_accounts.id as account_id',
                    'cash_accounts.code',
                    'cash_accounts.name as account_name',
                    'cash_accounts.category',
                    'journal_entries.id as entry_id',
                    'journal_entries.entry_date',
                    'journal_entries.details',
                    'journal_entry_items.type',
                    'journal_entry_items.amount',
                ])
                ->orderBy('cash_accounts.code')
                ->orderBy('journal_entries.entry_date')
                ->orderBy('journal_entries.id')
                ->get();

            // Group transactions by account
            $accountsData = [];
            foreach ($transactions as $transaction) {
                $accountId = $transaction->account_id;

                if (! isset($accountsData[$accountId])) {
                    $accountsData[$accountId] = [
                        'account' => [
                            'id' => $transaction->account_id,
                            'code' => $transaction->code,
                            'name' => $transaction->account_name,
                            'category' => $transaction->category,
                        ],
                        'transactions' => [],
                        'opening_balance' => 0,
                        'closing_balance' => 0,
                    ];
                }

                $accountsData[$accountId]['transactions'][] = [
                    'entry_id' => $transaction->entry_id,
                    'date' => $transaction->entry_date,
                    'details' => $transaction->details,
                    'type' => $transaction->type,
                    'debit' => $transaction->type === 'd' ? $transaction->amount : 0,
                    'credit' => $transaction->type === 'c' ? $transaction->amount : 0,
                    'amount' => $transaction->amount,
                ];
            }

            // Calculate opening balances and running balances
            $this->calculateBalances($accountsData, $start);

            $this->data = [
                'currency_code' => $data['currency_code'],
                'exchange_rate' => Currency::getExchangeRate($data['currency_code']),
                'accounts' => array_values($accountsData),
                'selected_account_id' => $data['account_id'] ?? null,
            ];
        } else {
            $this->data = null;
            $this->period = null;
        }
    }

    private function calculateBalances(array &$accountsData, Carbon $start): void
    {
        foreach ($accountsData as $accountId => &$accountData) {
            // Calculate opening balance (transactions before the start date)
            $openingBalance = JournalEntryItem::query()
                ->join('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->where('journal_entry_items.account_id', $accountId)
                ->where('journal_entries.entry_date', '<', $start)
                ->select([
                    DB::raw('SUM(CASE WHEN type = "d" THEN amount ELSE 0 END) as total_debit'),
                    DB::raw('SUM(CASE WHEN type = "c" THEN amount ELSE 0 END) as total_credit'),
                ])
                ->first();

            $category = AccountCategory::from($accountData['account']['category']);

            if ($category->isNormalDebitBalance()) {
                // For accounts with normal debit balance (assets, expenses)
                $accountData['opening_balance'] = ($openingBalance->total_debit ?? 0) - ($openingBalance->total_credit ?? 0);
            } else {
                // For accounts with normal credit balance (liabilities, equity, revenue)
                $accountData['opening_balance'] = ($openingBalance->total_credit ?? 0) - ($openingBalance->total_debit ?? 0);
            }

            // Calculate running balances for each transaction
            $runningBalance = $accountData['opening_balance'];
            foreach ($accountData['transactions'] as &$transaction) {
                if ($category->isNormalDebitBalance()) {
                    $runningBalance += $transaction['debit'] - $transaction['credit'];
                } else {
                    $runningBalance += $transaction['credit'] - $transaction['debit'];
                }
                $transaction['balance'] = $runningBalance;
            }

            $accountData['closing_balance'] = $runningBalance;
        }
    }

    public function downloadPdf(): void
    {
        if ($this->data === null) {
            return;
        }

        $pdfService = app(PdfService::class);

        $fileName = 'general-ledger-report-' . $this->period['start'] . '-to-' . $this->period['end'] . '.pdf';

        // Create the reports directory if it doesn't exist
        $reportsPath = storage_path('app/public/reports');
        if (! file_exists($reportsPath)) {
            mkdir($reportsPath, 0755, true);
        }

        $pdfPath = $pdfService
            ->view('pdf.general-ledger-report', [
                'data' => $this->data,
                'period' => $this->period,
            ])
            ->name($fileName)
            ->save($reportsPath);

        $this->dispatch('download-file', url('storage/reports/' . basename($pdfPath)));
    }
}
