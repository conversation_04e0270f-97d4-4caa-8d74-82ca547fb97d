<?php

namespace App\Filament\Finance\Resources\CashCategoryResource\Pages;

use App\Filament\Finance\Resources\CashCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageCashCategories extends ManageRecords
{
    protected static string $resource = CashCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('sm'),
        ];
    }
}
