<?php

namespace App\Filament\Finance\Resources;

use App\Enums\ExpenseGroup;
use App\Filament\Finance\Resources\CashCategoryResource\Pages;
use App\Models\Finance\CashCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;

class CashCategoryResource extends Resource
{
    protected static ?string $model = CashCategory::class;

    protected static ?string $modelLabel = 'category';

    protected static ?string $navigationGroup = 'Expenses';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('parent_id')
                    ->label('Parent')
                    ->relationship('parent', 'name', fn ($query) => $query->whereNull('parent_id')),
                Forms\Components\Select::make('group')
                    ->options(ExpenseGroup::class),
                // Forms\Components\Radio::make('type')
                //     ->options(['in' => 'In', 'out' => 'Out'])
                //     ->inline()
                //     ->default('out'),
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\Select::make('account_id')
                    ->label('Account')
                    ->relationship('account', 'name', fn ($query) => $query->orderBy('code'))
                    ->searchable()
                    ->preload(),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query
                ->with(['parent', 'account'])
                ->withCount('group_cashes'))
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID'),
                Tables\Columns\TextColumn::make('name')
                    ->description(fn ($record) => $record->parent?->name ?? null, 'above')
                    ->searchable(),
                // Tables\Columns\BadgeColumn::make('type')
                //     ->formatStateUsing(fn ($state) => $state == 'in' ? 'In' : 'Out'),
                Tables\Columns\TextColumn::make('group')
                    ->badge(),
                Tables\Columns\TextColumn::make('account.name'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('group')
                    ->options(ExpenseGroup::class),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => ! $record->group_cashes_count),
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('editGroup')
                        ->label('Edit Group')
                        ->icon('heroicon-m-pencil-square')
                        ->modalWidth('sm')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'group' => $data['group'],
                                ]);
                            }
                        })
                        ->form([
                            Forms\Components\Select::make('group')
                                ->options(ExpenseGroup::class)
                                ->required(),
                        ])
                        ->deselectRecordsAfterCompletion(),
                    Tables\Actions\BulkAction::make('editAccount')
                        ->label('Edit Account')
                        ->icon('heroicon-m-pencil-square')
                        ->modalWidth('sm')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'account_id' => $data['account_id'],
                                ]);
                            }
                        })
                        ->form([
                            Forms\Components\Select::make('account_id')
                                ->label('Account')
                                ->relationship('account', 'name', fn ($query) => $query->orderBy('code'))
                                ->searchable()
                                ->preload()
                                ->required(),
                        ])
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCashCategories::route('/'),
        ];
    }
}
