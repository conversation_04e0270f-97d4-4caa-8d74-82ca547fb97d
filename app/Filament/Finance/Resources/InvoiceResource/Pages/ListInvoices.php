<?php

namespace App\Filament\Finance\Resources\InvoiceResource\Pages;

use App\Exports\Finance\SalesInvoiceExport;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Filament\Finance\Resources\InvoiceResource\Widgets;
use App\Filament\Imports\InvoiceImporter;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ListRecords;

class ListInvoices extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                // Actions\Action::make('download_template')
                //     ->icon('phosphor-file')
                //     ->action(function () {
                //         return response()->download(storage_path('templates/template kas advance.xlsx'));
                //     }),
                // ExcelImportAction::make()
                //     ->label('XLSX')
                //     ->icon('phosphor-microsoft-excel-logo')
                //     ->color('gray')
                //     ->uploadField(fn ($upload) => $upload->label('Excel file'))
                //     ->use(UserCashImport::class),
                Actions\ImportAction::make()
                    ->importer(InvoiceImporter::class)
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->groupedIcon('phosphor-file-csv'),
            ])
                ->label('Import')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('gray')
                ->button(),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new SalesInvoiceExport($this->tableFilters))->download();
                    }),
                Actions\Action::make('csv')
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->action(function () {
                        return (new SalesInvoiceExport($this->tableFilters, 'csv'))->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\InvoiceOverview::class,
        ];
    }
}
