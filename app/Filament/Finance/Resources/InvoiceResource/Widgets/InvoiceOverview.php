<?php

namespace App\Filament\Finance\Resources\InvoiceResource\Widgets;

use App\Filament\Finance\Resources\InvoiceResource\Pages\ListInvoices;
use App\Models\Currency;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class InvoiceOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static bool $isLazy = false;

    protected function getColumns(): int
    {
        return 2;
    }

    protected function getTablePage(): string
    {
        return ListInvoices::class;
    }

    protected function getStats(): array
    {
        $data = $this->getData();
        $rate = Currency::getExchangeRate('IDR');

        return [
            Stat::make('Invoice', money($data->total_invoice ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->total_invoice * $rate, 'IDR', true)),
            Stat::make('Payment', money($data->total_payment ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->total_payment * $rate, 'IDR', true)),
            Stat::make('Receivable', money($data->balance ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->balance * $rate, 'IDR', true)),
            Stat::make('Overdue', money($data->overdue_balance ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->overdue_balance * $rate, 'IDR', true))
                ->color('danger'),
        ];
    }

    protected function getData()
    {
        return $this->getTablePageInstance()->getFilteredTableQuery()
            ->without(['group', 'customer'])
            ->select([
                DB::raw('SUM(`total` * `exchange_rate`) AS total_invoice'),
                DB::raw('SUM(`paid` * `exchange_rate`) AS total_payment'),
                DB::raw('SUM((`total` * `exchange_rate`) - (`paid` * `exchange_rate`)) AS balance'),
                DB::raw('SUM(CASE WHEN `status` = "overdue" THEN (`total` * `exchange_rate`) - (`paid` * `exchange_rate`) ELSE 0 END) AS overdue_balance'),
            ])
            ->first();
    }
}
