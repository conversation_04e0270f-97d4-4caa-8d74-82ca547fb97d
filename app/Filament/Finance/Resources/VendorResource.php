<?php

namespace App\Filament\Finance\Resources;

use App\Enums\VendorType;
use App\Filament\Finance\Resources\VendorResource\Pages;
use App\Models\Vendor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class VendorResource extends Resource
{
    protected static ?string $model = Vendor::class;

    protected static ?string $navigationGroup = 'Contacts';

    protected static ?int $navigationSort = -10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('vendor_type')
                    ->options(VendorType::class)
                    ->required(),
                Forms\Components\TextInput::make('company_name')
                    ->required(),
                Forms\Components\TextInput::make('contact_name'),
                Forms\Components\TextInput::make('contact_email')
                    ->email(),
                PhoneInput::make('contact_phone'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('company_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('contact_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('contact_email')
                    ->searchable()
                    ->sortable(),
                PhoneColumn::make('contact_phone')
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn () => auth('web')->user()->isSuperAdmin()),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\Action::make('purchases')
                            ->icon('phosphor-invoice-fill')
                            ->url(fn ($record) => BillResource::getUrl('index', ['tableFilters[vendor_id][value]' => $record->id])),
                        Tables\Actions\Action::make('payments')
                            ->icon('heroicon-m-banknotes')
                            ->url(fn ($record) => BillPaymentResource::getUrl('index', ['tableFilters[bill][vendor_id][value]' => $record->id])),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->defaultSort('company_name');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVendors::route('/'),
            'create' => Pages\CreateVendor::route('/create'),
            'view' => Pages\ViewVendor::route('/{record}'),
            'edit' => Pages\EditVendor::route('/{record}/edit'),
        ];
    }
}
