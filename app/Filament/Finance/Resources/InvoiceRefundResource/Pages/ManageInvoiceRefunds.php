<?php

namespace App\Filament\Finance\Resources\InvoiceRefundResource\Pages;

use App\Filament\Finance\Resources\InvoiceRefundResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageInvoiceRefunds extends ManageRecords
{
    protected static string $resource = InvoiceRefundResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
