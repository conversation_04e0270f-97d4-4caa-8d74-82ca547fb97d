<?php

namespace App\Filament\Finance\Resources\InvoicePaymentResource\Pages;

use App\Exports\Finance\SalesInvoicePaymentExport;
use App\Filament\Finance\Resources\InvoicePaymentResource;
use App\Filament\Imports\InvoicePaymentImporter;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageInvoicePayments extends ManageRecords
{
    protected static string $resource = InvoicePaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->importer(InvoicePaymentImporter::class)
                ->label('Import'),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new SalesInvoicePaymentExport($this->tableFilters))->download();
                    }),
                Actions\Action::make('csv')
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->action(function () {
                        return (new SalesInvoicePaymentExport($this->tableFilters, 'csv'))->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
            Actions\CreateAction::make()
                ->mountUsing(fn ($form) => $form->fill([
                    'payment_method' => 'cash',
                    'paid_at' => Carbon::now(),
                    'currency_code' => 'SAR',
                ])),
        ];
    }
}
