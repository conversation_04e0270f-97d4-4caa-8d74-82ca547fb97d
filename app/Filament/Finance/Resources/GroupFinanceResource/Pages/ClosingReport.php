<?php

namespace App\Filament\Finance\Resources\GroupFinanceResource\Pages;

use App\Actions\Group\GenerateReport;
use App\Filament\Finance\Resources\GroupFinanceResource;
use App\Filament\Resources\GroupResource;
use App\Models\Group;
use Filament\Actions;
use Filament\Resources\Pages\Concerns;
use Filament\Resources\Pages\Page;

/**
 * @property Group $record
 */
class ClosingReport extends Page
{
    use Concerns\InteractsWithRecord;

    protected static string $resource = GroupFinanceResource::class;

    protected static string $view = 'filament.resources.group-finances.pages.closing-report';

    protected static ?string $breadcrumb = 'Closing Report';

    protected static ?string $navigationLabel = 'Closing Report';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public $data = [];

    public function mount($record): void
    {
        abort_unless(auth()->user()->hasRole(['Admin', 'Finance']), 403);

        // @phpstan-ignore-next-line
        $this->record = $this->resolveRecord($record);

        $this->data = $this->record->getClosingReportData();
    }

    protected function getViewData(): array
    {
        return array_merge(parent::getViewData(), ['data' => $this->data]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('view_pif')
                ->label('View PIF')
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->url(GroupResource::getUrl('view', ['record' => $this->getRecord()])),
            Actions\Action::make('pdf')
                ->label('PDF')
                ->action(function ($record) {
                    return response()->download(GenerateReport::run($record));
                })
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray'),
        ];
    }
}
