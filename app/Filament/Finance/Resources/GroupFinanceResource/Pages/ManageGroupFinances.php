<?php

namespace App\Filament\Finance\Resources\GroupFinanceResource\Pages;

use App\Filament\Finance\Resources\GroupFinanceResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Builder;
use Umrahservice\Groups\Enums\GroupProgress;

class ManageGroupFinances extends ManageRecords
{
    protected static string $resource = GroupFinanceResource::class;

    public function getTabs(): array
    {
        return collect(GroupProgress::cases())
            ->prepend('all')
            ->filter(fn ($progress) => auth()->user()->hasExactRoles('Mutawif')
                    ? $progress == 'all' || $progress !== GroupProgress::Finished
                    : true)
            ->mapWithKeys(function ($progress, $key) {
                $tabKey = $key === 0 ? 'all' : strtolower($progress->name);

                return [
                    $tabKey => Tab::make()->when(
                        $progress !== 'all',
                        fn (Tab $tab) => $tab->modifyQueryUsing(
                            fn (Builder $query) => $query->where('progress', $progress)
                        )
                    ),
                ];
            })
            ->toArray();
    }
}
