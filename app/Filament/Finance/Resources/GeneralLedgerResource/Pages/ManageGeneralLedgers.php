<?php

namespace App\Filament\Finance\Resources\GeneralLedgerResource\Pages;

use App\Exports\GeneralLedgerExport;
use App\Filament\Finance\Resources\GeneralLedgerResource;
use App\Filament\Finance\Resources\GeneralLedgerResource\Widgets;
use App\Models\Finance\CashAccount;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Colors\Color;
use Illuminate\Support\Carbon;

class ManageGeneralLedgers extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = GeneralLedgerResource::class;

    protected static ?string $title = 'Buku Besar';

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('download_xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-file-xls')
                    ->action(function ($livewire) {
                        $account = CashAccount::find($livewire->tableFilters['account_id']['value']);

                        $period = '';
                        if ($livewire->tableFilters['entry_date']['entry_date'] ?? null) {
                            $dates = explode(' - ', $livewire->tableFilters['entry_date']['entry_date']);
                            $period = Carbon::createFromFormat('d/m/Y', $dates[0])->format('Y-m-d') . ' - ' . Carbon::createFromFormat('d/m/Y', $dates[1])->format('Y-m-d');
                        } else {
                            $p = current_period();
                            $period = $p->date_start->format('Y-m-d') . ' - ' . $p->date_end->format('Y-m-d');
                        }

                        return (new GeneralLedgerExport($livewire->tableFilters))->download("Buku Besar - {$account->name} - {$period}.xlsx");
                    }),
            ])
                ->button()
                ->label('Download')
                ->icon('heroicon-o-arrow-down-tray')
                ->color(Color::Emerald),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\GeneralLedgerOverview::class,
        ];
    }
}
