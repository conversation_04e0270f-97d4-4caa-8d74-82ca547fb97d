<?php

namespace App\Filament\Finance\Resources\GeneralLedgerResource\Widgets;

use App\Filament\Finance\Resources\GeneralLedgerResource\Pages;
use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class GeneralLedgerOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static ?string $pollingInterval = null;

    protected static bool $isLazy = false;

    protected function getColumns(): int
    {
        return 2;
    }

    protected function getTablePage(): string
    {
        return Pages\ManageGeneralLedgers::class;
    }

    protected function getStats(): array
    {
        $debit = $this->getPageTableQuery()->sum(DB::raw("IF(type = 'd', amount, 0)"));
        $credit = $this->getPageTableQuery()->sum(DB::raw("IF(type = 'c', amount, 0)"));

        return [
            Stat::make('Debit', money($debit, 'SAR', true)),
            Stat::make('Credit', money($credit, 'SAR', true)),
            Stat::make('Cashflow', money($debit - $credit, 'SAR', true)),
            Stat::make('Balance', money($this->getBalance() ?? 0, 'SAR', true)),
        ];
    }

    protected function getBalance()
    {
        return JournalEntryItem::query()
            ->leftJoin('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
            ->when($this->tableFilters['account_id']['value'] ?? null, fn ($query, $account_id) => $query->where('account_id', $account_id))
            ->whereDate('entry_date', '>=', current_period()->date_start)
            ->when($this->tableFilters['entry_date']['entry_date'] ?? null, function ($query, $range) {
                $dates = explode(' - ', $range);
                $query->whereDate('entry_date', '<=', Carbon::createFromFormat('d/m/Y', $dates[1])->endOfDay(),
                );
            })
            ->selectRaw('SUM(IF(type = "d", amount, 0) - IF(type = "c", amount, 0)) AS balance')
            ->value('balance');
    }
}
