<?php

namespace App\Filament\Finance\Resources\CashAccountResource\Pages;

use App\Filament\Finance\Resources\CashAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageCashAccounts extends ManageRecords
{
    protected static string $resource = CashAccountResource::class;

    protected static ?string $title = 'Chart of Accounts';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('sm'),
        ];
    }
}
