<?php

namespace App\Filament\Finance\Resources\CashTransactionResource\Pages;

use App\Filament\Finance\Resources\CashTransactionResource;
use App\Filament\Finance\Resources\CashTransactionResource\Widgets;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageCashTransactions extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = CashTransactionResource::class;

    protected static ?string $title = 'Cash & Bank';

    protected function getHeaderWidgets(): array
    {
        return [
            // Widgets\CashBankOverview::make(),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\CreateAction::make('add_cash_in')
                    ->label('Income')
                    ->icon(null)
                    ->groupedIcon('heroicon-o-arrow-down-on-square')
                    ->modalHeading('New Income')
                    ->mountUsing(fn ($form) => $form->fill([
                        'type' => 'in',
                        'transaction_date' => now(),
                        'currency_code' => 'SAR',
                        'exchange_rate' => 1,
                    ])),
                Actions\CreateAction::make('add_cash_out')
                    ->label('Expense')
                    ->icon(null)
                    ->groupedIcon('heroicon-o-arrow-up-on-square')
                    ->modalHeading('New Expense')
                    ->mountUsing(fn ($form) => $form->fill([
                        'type' => 'out',
                        'transaction_date' => now(),
                        'currency_code' => 'SAR',
                        'exchange_rate' => 1,
                    ])),
                Actions\CreateAction::make('add_cash_transfer')
                    ->label('Transfer')
                    ->icon(null)
                    ->groupedIcon('heroicon-o-arrows-right-left')
                    ->modalHeading('New Transfer')
                    ->mountUsing(fn ($form) => $form->fill([
                        'type' => 'transfer',
                        'transaction_date' => now(),
                        'currency_code' => 'SAR',
                        'exchange_rate' => 1,
                    ])),
            ])
                ->label('New')
                ->icon('heroicon-o-plus')
                ->button(),
        ];
    }
}
