<?php

namespace App\Filament\Finance\Resources\CashTransactionResource\Widgets;

use App\Filament\Finance\Resources\CashTransactionResource\Pages;
use App\Models\Finance\JournalEntryItem;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class CashBankOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static bool $isLazy = false;

    protected function getTablePage(): string
    {
        return Pages\ManageCashTransactions::class;
    }

    protected function getStats(): array
    {
        $accountId = $this->tableFilters['account_id']['value'] ?? null;
        $balance = JournalEntryItem::query()
            ->when($accountId, fn ($query) => $query->where('account_id', $accountId))
            ->sum(DB::raw("CASE WHEN `type`='d' THEN `amount` ELSE -`amount` END"));

        return [
            Stat::make('Balance', money($balance, 'SAR', true)),
        ];
    }
}
