<?php

namespace App\Filament\Finance\Resources\StartingBalanceResource\Pages;

use App\Filament\Finance\Resources\StartingBalanceResource;
use App\Models\Finance\JournalEntry;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageStartingBalances extends ManageRecords
{
    protected static string $resource = StartingBalanceResource::class;

    protected static ?string $title = 'Saldo Awal';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('sm')
                ->using(function ($data) {
                    $journalEntry = JournalEntry::query()
                        ->updateOrCreate([
                            'transaction_type' => 'balance',
                            'transaction_id' => $data['owner_id'],
                        ], [
                            'entry_date' => $data['owner_id'] . '-01-01',
                            'details' => 'Saldo Awal',
                        ]);

                    return $journalEntry->items()->updateOrCreate([
                        'account_id' => $data['account_id'],
                        'type' => 'd',
                        'owner_type' => 'year',
                        'owner_id' => $data['owner_id'],
                    ], [
                        'amount' => $data['amount'],
                    ]);
                }),
        ];
    }
}
