<?php

namespace App\Filament\Finance\Resources\PurchaseOrderResource\Pages;

use App\Enums\OrderStatus;
use App\Filament\Finance\Resources\PurchaseOrderResource;
use Filament\Resources\Pages\EditRecord;

class EditPurchaseOrder extends EditRecord
{
    protected static string $resource = PurchaseOrderResource::class;

    protected static ?string $navigationLabel = 'Edit';

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return $parameters['record']->status === OrderStatus::Open;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['vendor_type'] = $this->record->vendor?->vendor_type;

        return $data;
    }
}
