<?php

namespace App\Filament\Finance\Resources\PurchaseOrderResource\Pages;

use App\Actions\PurchaseOrder\GeneratePDF;
use App\Filament\Finance\Resources\PurchaseOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewPurchaseOrder extends ViewRecord
{
    protected static string $resource = PurchaseOrderResource::class;

    protected static string $view = 'filament.finance.resources.purchase-order-resource.pages.view-purchase-order';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'View';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('pdf')
                ->label('PDF')
                ->action(function ($record) {
                    return response()->download(GeneratePDF::run($record));
                })
                ->color('gray')
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }
}
