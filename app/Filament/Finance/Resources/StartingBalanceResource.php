<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\StartingBalanceResource\Pages;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntryItem;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class StartingBalanceResource extends Resource
{
    protected static ?string $model = JournalEntryItem::class;

    protected static ?string $modelLabel = 'entry';

    protected static ?string $navigationIcon = 'heroicon-o-pencil-square';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationLabel = 'Saldo Awal';

    protected static ?int $navigationSort = 1;

    protected static ?string $slug = 'starting-balance';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function getEloquentQuery(): Builder
    {
        return JournalEntryItem::query()
            ->leftJoin('cash_accounts AS ca', 'ca.id', '=', 'journal_entry_items.account_id')
            ->select([
                DB::raw("COALESCE(CONCAT(`ca`.`code`, ' · ', `ca`.`name`), `ca`.`name`) AS account"),
                'journal_entry_items.*',
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\Select::make('owner_id')
                    ->label('Year')
                    ->options(
                        collect(range(2022, date('Y')))
                            ->mapWithKeys(fn ($y) => [$y => $y])
                    )
                    ->required()
                    ->default(date('Y'))
                    ->disabled(fn ($context) => $context === 'edit'),
                Forms\Components\Select::make('account_id')
                    ->label('Account')
                    ->searchable()
                    ->required()
                    ->options(
                        CashAccount::query()
                            ->select([
                                'id',
                                DB::raw("COALESCE(CONCAT(`code`, ' · ', `name`), `name`) AS fullname"),
                            ])
                            ->orderBy('code')
                            ->pluck('fullname', 'id')
                    )
                    ->disabled(fn ($context) => $context === 'edit'),
                Forms\Components\TextInput::make('amount')
                    ->label('Saldo Awal')
                    ->numeric()
                    ->prefix('SAR')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('owner_id')
                    ->label('Year'),
                Tables\Columns\TextColumn::make('account')
                    ->searchable(['ca.name', 'ca.code']),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyRight(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('owner_id')
                    ->label('Year')
                    ->options(
                        collect(range(2022, date('Y')))
                            ->mapWithKeys(fn ($y) => [$y => $y])
                    )
                    ->default(date('Y'))
                    ->selectablePlaceholder(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->defaultSort('account');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageStartingBalances::route('/'),
        ];
    }
}
