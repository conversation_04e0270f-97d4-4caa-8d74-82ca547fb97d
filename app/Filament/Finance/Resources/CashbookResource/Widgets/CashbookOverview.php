<?php

namespace App\Filament\Finance\Resources\CashbookResource\Widgets;

use App\Filament\Finance\Resources\CashbookResource\Pages;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class CashbookOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static ?string $pollingInterval = null;

    protected static bool $isLazy = false;

    protected function getTablePage(): string
    {
        return Pages\ManageCashbooks::class;
    }

    protected function getStats(): array
    {
        $debit = $this->getPageTableQuery()->sum(DB::raw("IF(type = 'd', amount, 0)"));
        $credit = $this->getPageTableQuery()->sum(DB::raw("IF(type = 'c', amount, 0)"));

        return [
            Stat::make('Debit', money($debit, 'SAR', true)),
            Stat::make('Credit', money($credit, 'SAR', true)),
            Stat::make('Balance', money($debit - $credit, 'SAR', true)),
        ];
    }
}
