<?php

namespace App\Filament\Finance\Resources\CashbookResource\Pages;

use App\Exports\CashbookExport;
use App\Filament\Finance\Resources\CashbookResource;
use App\Filament\Finance\Resources\CashbookResource\Widgets;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Colors\Color;

class ManageCashbooks extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = CashbookResource::class;

    protected static ?string $title = 'Buku Kas';

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\Action::make('download_xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-file-xls')
                    ->action(function ($livewire) {
                        $period = '';
                        if ($livewire->tableFilters['entry_date']['entry_date'] ?? null) {
                            $dates = explode(' - ', $livewire->tableFilters['entry_date']['entry_date']);
                            $period = Carbon::createFromFormat('d/m/Y', $dates[0])->format('Y-m-d').' - '.Carbon::createFromFormat('d/m/Y', $dates[1])->format('Y-m-d');
                        } else {
                            $p = current_period();
                            $period = $p->date_start->format('Y-m-d').' - '.$p->date_end->format('Y-m-d');
                        }

                        return (new CashbookExport($livewire->tableFilters))->download('Buku Kas '.$period.'.xlsx');
                    }),
            ])
                ->button()
                ->label('Download')
                ->icon('heroicon-o-arrow-down-tray')
                ->color(Color::Emerald),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\CashbookOverview::class,
        ];
    }
}
