<?php

namespace App\Filament\Finance\Resources;

use App\Enums\Finance\AccountCategory;
use App\Filament\Finance\Resources\ProductCategoryResource\Pages;
use App\Models\Finance\ProductCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ProductCategoryResource extends Resource
{
    protected static ?string $model = ProductCategory::class;

    protected static ?string $modelLabel = 'category';

    protected static ?string $navigationGroup = 'Products & Services';

    protected static ?int $navigationSort = 35;

    public static function form(Form $form): Form
    {
        return $form
            ->schema(static::getFormSchema())
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->with('cost_account')
                ->withCount('products'))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('cost_account.name'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalWidth('sm'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record->products_count === 0),
            ])
            ->defaultSort('name');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProductCategories::route('/'),
        ];
    }

    public static function getFormSchema(): array
    {
        return [
            Forms\Components\TextInput::make('name')
                ->required(),
            Forms\Components\Select::make('cost_account_id')
                ->relationship(
                    'cost_account',
                    'name',
                    fn ($query) => $query->where('category', AccountCategory::CostOfGoodsSold)->orderBy('code')
                )
                ->searchable()
                ->preload()
                ->required(),
        ];
    }
}
