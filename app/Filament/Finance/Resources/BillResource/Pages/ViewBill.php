<?php

namespace App\Filament\Finance\Resources\BillResource\Pages;

use App\Actions\Bill\GeneratePDF;
use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Finance\Resources\BillResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBill extends ViewRecord
{
    protected static string $resource = BillResource::class;

    protected static string $view = 'filament.finance.resources.bill-resource.pages.view-bill';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'View';

    protected function getHeaderActions(): array
    {
        return [
            ActivityLogTimelineAction::make('history'),
            Actions\Action::make('pdf')
                ->label('PDF')
                ->action(function ($record) {
                    return response()->download(GeneratePDF::run($record));
                })
                ->color('gray')
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }
}
