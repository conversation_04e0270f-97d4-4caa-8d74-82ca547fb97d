<?php

namespace App\Filament\Finance\Resources\BillResource\Pages;

use App\Exports\Finance\BillsExport;
use App\Filament\Finance\Resources\BillResource;
use App\Filament\Finance\Resources\BillResource\Widgets;
use App\Filament\Imports\BillImporter;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ListRecords;

class ListBills extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = BillResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ActionGroup::make([
                Actions\ImportAction::make()
                    ->importer(BillImporter::class)
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->groupedIcon('phosphor-file-csv'),
            ])
                ->label('Import')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('gray')
                ->button(),
            Actions\ActionGroup::make([
                Actions\Action::make('xlsx')
                    ->label('XLSX')
                    ->icon('phosphor-microsoft-excel-logo')
                    ->action(function () {
                        return (new BillsExport($this->tableFilters))->download();
                    }),
                Actions\Action::make('csv')
                    ->label('CSV')
                    ->icon('phosphor-file-csv')
                    ->action(function () {
                        return (new BillsExport($this->tableFilters, 'csv'))->download();
                    }),
            ])
                ->label('Export')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('gray')
                ->button(),
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\BillsOverview::class,
        ];
    }
}
