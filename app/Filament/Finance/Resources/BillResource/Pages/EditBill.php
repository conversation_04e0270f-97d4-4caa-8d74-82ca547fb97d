<?php

namespace App\Filament\Finance\Resources\BillResource\Pages;

use App\Filament\Finance\Resources\BillResource;
use Filament\Resources\Pages\EditRecord;

class EditBill extends EditRecord
{
    protected static string $resource = BillResource::class;

    protected static ?string $navigationLabel = 'Edit';

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['vendor_type'] = $this->record->vendor?->vendor_type;

        return $data;
    }
}
