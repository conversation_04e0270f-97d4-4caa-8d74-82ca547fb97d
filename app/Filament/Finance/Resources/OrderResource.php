<?php

namespace App\Filament\Finance\Resources;

use App\Actions\Invoice\BulkGeneratePDF;
use App\Enums\OrderStatus;
use App\Filament\Finance\Resources\OrderResource\Pages;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Finance\Order;
use App\Models\Finance\Product;
use App\Models\Group;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $recordTitleAttribute = 'order_number';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 15;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Group::make()
                    ->schema([
                        Infolists\Components\Section::make()
                            ->schema(static::getInfolistSchema())
                            ->columns(),
                    ])
                    ->columnSpan(['lg' => 2]),

                Infolists\Components\Group::make()
                    ->schema([
                        Infolists\Components\Section::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('order_number'),

                                Infolists\Components\TextEntry::make('order_date')
                                    ->date(),
                            ]),

                        Infolists\Components\Section::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('currency_code')
                                    ->label('Currency'),

                                Infolists\Components\TextEntry::make('exchange_rate'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Order items')
                    ->schema(static::getInfolistSchema('items')),
            ])
            ->columns(3);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->columns()
                            ->schema(static::getFormSchema()),

                        Forms\Components\Section::make()
                            ->columns()
                            ->schema([
                                Forms\Components\Select::make('currency_code')
                                    ->required()
                                    ->label('Currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                                        ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->default(1),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Placeholder::make('created_at')
                                    ->label('Created at')
                                    ->content(fn ($record) => $record->created_at?->diffForHumans()),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('Last modified at')
                                    ->content(fn ($record) => $record->updated_at?->diffForHumans()),
                            ])
                            ->hidden(fn ($record) => $record === null),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\ToggleButtons::make('status')
                                    ->grouped()
                                    ->options(OrderStatus::class)
                                    ->default(OrderStatus::Open)
                                    ->required(),

                                Forms\Components\TextInput::make('order_number')
                                    ->default(Order::getNextOrderNumber())
                                    ->unique(ignoreRecord: true)
                                    ->required(),

                                Forms\Components\DatePicker::make('order_date')
                                    ->label('Order date')
                                    ->default(Carbon::now()),
                            ]),
                    ]),

                Forms\Components\Section::make('Order items')
                    ->schema(static::getFormSchema('items')),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => $record->group
                        ? $record->group->name ?? 'Group #' . $record->group->id
                        : null)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('order_date')
                    ->label('Order date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Items')
                    ->badge(),
                Tables\Columns\TextColumn::make('total_price')
                    ->label('Total order')
                    ->sortable()
                    ->currencyAuto(),
                Tables\Columns\TextColumn::make('status'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('customer_id')
                    ->label('Customer')
                    ->options(Customer::query()->orderBy('name')->pluck('name', 'id'))
                    ->searchable(),
                Group::tableFilterGroup(),
                Tables\Filters\Filter::make('order_date')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->placeholder(fn ($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('date_until')
                            ->placeholder(fn ($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('order_date', '>=', $date),
                            )
                            ->when(
                                $data['date_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('order_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['date_from'] ?? null) {
                            $indicators['date_from'] = 'Order from ' . Carbon::parse($data['date_from'])->format('d-M-y');
                        }
                        if ($data['date_until'] ?? null) {
                            $indicators['date_until'] = 'Order until ' . Carbon::parse($data['date_until'])->format('d-M-y');
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('pdf')
                    ->label('Download PDF')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('gray')
                    ->action(function ($records) {
                        return response()->download(BulkGeneratePDF::run($records));
                    }),
            ])
            ->defaultSort('order_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'view' => Pages\ViewOrder::route('/{record}'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewOrder::class,
            Pages\EditOrder::class,
        ]);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['order_number', 'subject', 'customer.name', 'group.name'];
    }

    public static function getNavigationBadge(): ?string
    {
        $count = static::$model::where('status', '!=', 'paid')->count();

        return $count > 0 ? $count : null;
    }

    public static function getFormSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Forms\Components\Repeater::make('items')
                    ->relationship()
                    ->orderColumn('order_column')
                    ->reorderableWithButtons()
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\Select::make('name')
                                    ->label('Item')
                                    ->searchable()
                                    ->getSearchResultsUsing(fn ($search) => Product::query()
                                        ->where('name', 'like', "%{$search}%")
                                        ->pluck('name', 'name'))
                                    ->requiredWithout('description')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set, $get) {
                                        $item = Product::query()
                                            ->where('name', $state)
                                            ->first();
                                        $set('description', $item?->description ?? '');
                                        $set('unit_price', ($item?->unit_price ?? 0) / ($get('../../exchange_rate') ?? 1));
                                    })
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('name')
                                            ->required(),
                                        Forms\Components\Textarea::make('description')
                                            ->required(),
                                        Forms\Components\TextInput::make('unit_price')
                                            ->label('Unit Price')
                                            ->numeric()
                                            ->prefix('SAR')
                                            ->required(),
                                    ])
                                    ->createOptionUsing(fn ($data) => Product::create($data)->name),
                                Forms\Components\Textarea::make('description')
                                    ->rows(2)
                                    ->hiddenLabel()
                                    ->extraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ])
                            ->columnSpan([
                                'md' => 5,
                            ]),

                        Forms\Components\TextInput::make('quantity')
                            ->numeric()
                            ->default(1)
                            ->columnSpan([
                                'md' => 2,
                            ])
                            ->required(),

                        Forms\Components\TextInput::make('unit_price')
                            ->label('Unit Price')
                            ->numeric()
                            ->required()
                            ->prefix(fn ($get) => $get('../../currency_code') ?? 'SAR')
                            ->columnSpan([
                                'md' => 3,
                            ]),
                    ])
                    ->defaultItems(1)
                    ->hiddenLabel()
                    ->columns([
                        'md' => 10,
                    ])
                    ->required(),
            ];
        }

        return [

            Forms\Components\Select::make('customer_id')
                ->relationship('customer', 'name')
                ->searchable()
                ->required()
                ->createOptionForm([
                    Forms\Components\TextInput::make('name')
                        ->required(),

                    Forms\Components\TextInput::make('email')
                        ->required()
                        ->email()
                        ->unique(),

                    Forms\Components\TextInput::make('phone'),
                ])
                ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                    return $action
                        ->modalHeading('Create customer')
                        ->modalSubmitActionLabel('Create customer')
                        ->modalWidth('lg');
                }),

            // Forms\Components\Select::make('status')
            //     ->options([
            //         'unpaid' => 'Unpaid',
            //     ])
            //     ->default('unpaid')
            //     ->required(),

            Forms\Components\Select::make('group_id')
                ->label('Group')
                ->relationship('group', 'name', fn ($query, $get) => $query
                    ->select(['id', DB::raw("COALESCE(`name`, CONCAT('Group #', `id`)) as `name`")])
                    ->where('customer_id', $get('customer_id')))
                ->searchable()
                ->preload(),

            Forms\Components\TextInput::make('subject')
                ->columnSpanFull(),
        ];
    }

    public static function getInfolistSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Infolists\Components\RepeatableEntry::make('items')
                    ->contained(false)
                    ->schema([
                        Infolists\Components\Group::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('name')
                                    ->label('Item'),
                                Infolists\Components\TextEntry::make('description')
                                    ->hiddenLabel()
                                    ->formatStateUsing(fn ($state) => nl2br($state))
                                    ->html(),
                            ])
                            ->columnSpan([
                                'md' => 5,
                            ]),

                        Infolists\Components\TextEntry::make('quantity')
                            ->columnSpan([
                                'md' => 2,
                            ]),

                        Infolists\Components\TextEntry::make('unit_price')
                            ->label('Unit Price')
                            ->formatStateUsing(fn ($state, $record) => money($state, $record->order?->currency_code ?? 'SAR', true))
                            ->columnSpan([
                                'md' => 3,
                            ]),
                    ])
                    ->hiddenLabel()
                    ->columns([
                        'md' => 10,
                    ]),
            ];
        }

        return [
            Infolists\Components\TextEntry::make('customer.name'),

            Infolists\Components\TextEntry::make('subject'),

            Infolists\Components\TextEntry::make('notes')
                ->formatStateUsing(fn ($state) => nl2br($state))
                ->html()
                ->columnSpan('full'),

            Infolists\Components\TextEntry::make('terms')
                ->label('Terms & conditions')
                ->formatStateUsing(fn ($state) => nl2br($state))
                ->html()
                ->columnSpan('full'),
        ];
    }
}
