<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\CashbookResource\Pages;
use App\Filament\Finance\Resources\CashbookResource\Widgets;
use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class CashbookResource extends Resource
{
    protected static ?string $model = JournalEntryItem::class;

    protected static ?string $modelLabel = 'item';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationIcon = 'phosphor-book-bookmark';

    protected static ?string $navigationLabel = 'Buku Kas';

    protected static ?int $navigationSort = 5;

    protected static ?string $slug = 'cashbook';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query
                ->leftJoin('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->leftJoin('cash_accounts', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->where(fn ($query) => $query
                    ->whereIn('cash_accounts.group', ['cash', 'bank'])
                    ->where('cash_accounts.code', '!=', config('finance.coa.advance_cash')))
                ->select([
                    'journal_entries.entry_date',
                    'journal_entries.details',
                    'journal_entry_items.*',
                    'cash_accounts.name AS account_name',
                    DB::raw("IF(type = 'd', amount, null) AS debit"),
                    DB::raw("IF(type = 'c', amount, null) AS credit"),
                ]))
            ->columns([
                Tables\Columns\TextColumn::make('entry_date')
                    ->label('Date')
                    ->sortable()
                    ->date(),
                Tables\Columns\TextColumn::make('details')
                    ->label('Details')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('debit')
                    ->label('Debit')
                    ->summarize(
                        Sum::make()->currency('SAR', true)
                            ->label('Total')
                    )
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('credit')
                    ->label('Credit')
                    ->summarize(
                        Sum::make()->currency('SAR', true)
                            ->label('Total')
                    )
                    ->currencyRight(),
            ])
            ->defaultGroup(
                Tables\Grouping\Group::make('account_id')
                    ->titlePrefixedWithLabel(false)
                    ->getTitleFromRecordUsing(fn ($record) => $record->account_name),
            )
            ->filters([
                DateRangeFilter::make('entry_date')
                    ->label('Date range')
                    ->withIndicator()
                    ->disableClear()
                    ->ranges(function ($component) {
                        $period = current_period();

                        return [
                            __('filament-daterangepicker-filter::message.today') => [$component->now(), $component->now()],
                            __('filament-daterangepicker-filter::message.yesterday') => [$component->now()->subDay(), $component->now()->subDay()],
                            __('filament-daterangepicker-filter::message.last_7_days') => [$component->now()->subDays(6), $component->now()],
                            __('filament-daterangepicker-filter::message.last_30_days') => [$component->now()->subDays(29), $component->now()],
                            __('filament-daterangepicker-filter::message.this_month') => [$component->now()->startOfMonth(), $component->now()->endOfMonth()],
                            __('filament-daterangepicker-filter::message.last_month') => [$component->now()->subMonth()->startOfMonth(), $component->now()->subMonth()->endOfMonth()],
                            'This Period' => [$period->date_start, $period->date_end],
                        ];
                    })
                    ->minDate(current_period()->date_start)
                    ->maxDate(current_period()->date_end)
                    ->indicateUsing(function ($data, $filter) {
                        $datesString = data_get($data, 'entry_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Date range: {$datesString}";
                    })
                    ->columnSpan(2)
                    ->startDate(Carbon::today()->startOfMonth())
                    ->endDate(Carbon::today()->endOfMonth()),
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Account')
                    ->relationship('account', 'name', fn ($query) => $query
                        ->isCashOrBank()
                        ->where('code', '!=', config('finance.coa.advance_cash'))
                        ->orderBy('code')),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->defaultSort('entry_date');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCashbooks::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\CashbookOverview::class,
        ];
    }
}
