<?php

namespace App\Filament\Finance\Resources\CustomerDepositResource\Pages;

use App\Filament\Finance\Resources\CustomerDepositResource;
use App\Filament\Finance\Resources\CustomerDepositResource\Widgets;
use Filament\Actions;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageCustomerDeposits extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = CustomerDepositResource::class;

    protected static ?string $title = 'Deposits';

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\DepositOverview::class,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
