<?php

namespace App\Filament\Finance\Resources\CustomerDepositResource\Widgets;

use App\Filament\Finance\Resources\CustomerDepositResource\Pages\ManageCustomerDeposits;
use App\Models\Finance\CustomerCash;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class DepositOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected static bool $isLazy = false;

    protected function getTablePage(): string
    {
        return ManageCustomerDeposits::class;
    }

    protected function getStats(): array
    {
        $customer_id = $this->tableFilters['customer_id']['value'] ?? null;

        $data = CustomerCash::query()
            ->when($customer_id, fn ($query) => $query->where('customer_id', $customer_id))
            ->select([
                DB::raw("SUM(IF(`type` = 'd', `amount` * `exchange_rate`, 0)) AS debit"),
                DB::raw("SUM(IF(`type` = 'c', `amount` * `exchange_rate`, 0)) AS credit"),
                DB::raw("SUM(IF(`type` = 'd', `amount` * `exchange_rate`, -1 * `amount` * `exchange_rate`)) AS balance"),
            ])
            ->first();

        return [
            Stat::make('Debit', money($data->debit ?? 0, 'SAR', true)),
            Stat::make('Credit', money($data->credit ?? 0, 'SAR', true)),
            Stat::make('Balance', money($data->balance ?? 0, 'SAR', true)),
        ];
    }
}
