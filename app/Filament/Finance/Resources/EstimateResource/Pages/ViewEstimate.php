<?php

namespace App\Filament\Finance\Resources\EstimateResource\Pages;

use App\Actions\Estimate\GeneratePDF;
use App\Filament\Finance\Resources\EstimateResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewEstimate extends ViewRecord
{
    protected static string $resource = EstimateResource::class;

    protected static string $view = 'filament.finance.resources.estimate-resource.pages.view-estimate';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'View';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('pdf')
                ->label('PDF')
                ->action(function ($record) {
                    return response()->download(GeneratePDF::run($record));
                })
                ->color('gray')
                ->icon('heroicon-o-arrow-down-tray'),
        ];
    }
}
