<?php

namespace App\Filament\Finance\Resources\EstimateResource\Pages;

use App\Enums\EstimateStatus;
use App\Filament\Finance\Resources\EstimateResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEstimate extends EditRecord
{
    protected static string $resource = EstimateResource::class;

    protected static ?string $navigationLabel = 'Edit';

    public static function shouldRegisterNavigation(array $parameters = []): bool
    {
        return $parameters['record']->status !== EstimateStatus::Invoiced;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave()
    {
        $this->record->refresh();
        $this->record->save();
    }
}
