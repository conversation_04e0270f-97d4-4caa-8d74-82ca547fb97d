<?php

namespace App\Filament\Finance\Resources\EstimateResource\Pages;

use App\Filament\Finance\Resources\EstimateResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEstimates extends ListRecords
{
    protected static string $resource = EstimateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}