<?php

namespace App\Filament\Finance\Resources\VendorResource\Pages;

use App\Enums\VendorType;
use App\Filament\Finance\Resources\VendorResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListVendors extends ListRecords
{
    protected static string $resource = VendorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return collect(VendorType::cases())
            ->mapWithKeys(function ($type) {
                $tabKey = strtolower($type->name);

                return [
                    $tabKey => Tab::make($type->getLabel())->modifyQueryUsing(
                        fn (Builder $query) => $query->where('vendor_type', $type)
                    ),
                ];
            })
            ->toArray();
    }
}
