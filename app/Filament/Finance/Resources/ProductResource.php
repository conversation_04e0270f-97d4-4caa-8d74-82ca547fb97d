<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\ProductResource\Pages;
use App\Models\Finance\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $modelLabel = 'item';

    protected static ?string $navigationGroup = 'Products & Services';

    protected static ?int $navigationSort = 30;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('category_id')
                    ->label('Category')
                    ->relationship('category', 'name')
                    ->searchable()
                    ->preload()
                    ->createOptionForm(ProductCategoryResource::getFormSchema())
                    ->createOptionAction(fn ($action) => $action->modalWidth('sm')),
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->label('Default description'),
                Forms\Components\TextInput::make('cost')
                    ->numeric()
                    ->prefix('SAR'),
                Forms\Components\TextInput::make('unit_price')
                    ->label('Price')
                    ->numeric()
                    ->prefix('SAR')
                    ->required(),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with('category'))
            ->columns([
                Tables\Columns\TextColumn::make('category.name'),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Default description')
                    ->searchable()
                    ->html()
                    ->formatStateUsing(fn ($state) => nl2br($state))
                    ->toggleable()
                    ->toggledHiddenByDefault(),
                Tables\Columns\TextColumn::make('cost')
                    ->money('SAR')
                    ->alignRight()
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_price')
                    ->label('Price')
                    ->money('SAR')
                    ->alignRight()
                    ->toggleable()
                    ->toggledHiddenByDefault()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Category')
                    ->relationship('category', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->modalWidth('sm'),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('change_category')
                        ->icon('heroicon-m-pencil-square')
                        ->modalWidth('sm')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'category_id' => $data['category_id'],
                                ]);
                            }
                        })
                        ->form([
                            Forms\Components\Select::make('category_id')
                                ->label('Category')
                                ->relationship('category', 'name')
                                ->searchable()
                                ->preload()
                                ->required()
                                ->createOptionForm(ProductCategoryResource::getFormSchema())
                                ->createOptionAction(fn ($action) => $action->modalWidth('sm')),
                        ])
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageProducts::route('/'),
        ];
    }
}
