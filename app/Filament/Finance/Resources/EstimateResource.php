<?php

namespace App\Filament\Finance\Resources;

use App\Enums\EstimateStatus;
use App\Filament\Finance\Resources\EstimateResource\Pages;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Finance\Estimate;
use App\Models\Finance\Product;
use App\Models\Finance\ProductCategory;
use App\Models\Group;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class EstimateResource extends Resource
{
    protected static ?string $model = Estimate::class;

    // protected static ?string $modelLabel = 'proforma invoice';

    protected static ?string $recordTitleAttribute = 'estimate_number';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 10;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->with(['items.estimate']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->columns()
                            ->schema(static::getFormSchema()),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Placeholder::make('created_at')
                                    ->label('Created at')
                                    ->content(fn ($record) => $record->created_at?->diffForHumans()),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('Last modified at')
                                    ->content(fn ($record) => $record->updated_at?->diffForHumans()),
                            ])
                            ->hidden(fn ($record) => $record === null),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('estimate_number')
                                    ->default(fn () => Estimate::getNextEstimateNumber())
                                    ->unique(ignoreRecord: true)
                                    ->required(),

                                Forms\Components\DatePicker::make('estimate_date')
                                    ->default(now())
                                    ->required(),

                                Forms\Components\DatePicker::make('valid_until')
                                    ->default(now()->addDays(30))
                                    ->required(),
                            ]),

                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\Select::make('currency_code')
                                    ->required()
                                    ->label('Currency')
                                    ->options(Currency::getOptions())
                                    ->default('SAR')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set) {
                                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                                    }),

                                Forms\Components\TextInput::make('exchange_rate')
                                    ->required()
                                    ->numeric()
                                    ->live()
                                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                                    ->default(1)
                                    ->visible(fn ($get) => $get('currency_code') !== 'SAR'),
                            ]),
                    ]),

                Forms\Components\Section::make('Items')
                    ->schema(static::getFormSchema('items')),
            ])
            ->columns(3);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Group::make()
                    ->schema([
                        Infolists\Components\Section::make()
                            ->schema(static::getInfolistSchema())
                            ->columns(),
                    ])
                    ->columnSpan(['lg' => 2]),

                Infolists\Components\Group::make()
                    ->schema([
                        Infolists\Components\Section::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('estimate_number'),
                                Infolists\Components\TextEntry::make('estimate_date')
                                    ->date(),
                                Infolists\Components\TextEntry::make('valid_until')
                                    ->date(),
                            ]),

                        Infolists\Components\Section::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('currency_code')
                                    ->label('Currency'),
                                Infolists\Components\TextEntry::make('exchange_rate'),
                            ]),
                    ]),

                Infolists\Components\Section::make('Items')
                    ->schema(static::getInfolistSchema('items')),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['group']))
            ->columns([
                Tables\Columns\TextColumn::make('estimate_number')
                    ->label('Number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => $record->group
                        ? $record->group->name ?? 'Group #' . $record->group->id
                        : null)
                    ->toggleable(),
                Tables\Columns\TextColumn::make('estimate_date')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('valid_until')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('items_count')
                    ->counts('items')
                    ->label('Items')
                    ->badge(),
                Tables\Columns\TextColumn::make('total')
                    ->sortable()
                    ->currencyAuto(),
                Tables\Columns\TextColumn::make('status')
                    ->badge(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('customer_id')
                    ->label('Customer')
                    ->options(Customer::query()->orderBy('name')->pluck('name', 'id'))
                    ->searchable(),
                Group::tableFilterGroup(),
                Tables\Filters\Filter::make('estimate_date')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->placeholder(fn ($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('date_until')
                            ->placeholder(fn ($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('estimate_date', '>=', $date),
                            )
                            ->when(
                                $data['date_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('estimate_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['date_from'] ?? null) {
                            $indicators['date_from'] = 'Estimates from ' . Carbon::parse($data['date_from'])->format('d-M-y');
                        }
                        if ($data['date_until'] ?? null) {
                            $indicators['date_until'] = 'Estimates until ' . Carbon::parse($data['date_until'])->format('d-M-y');
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('mark_as_sent')
                        ->label('Mark as Sent')
                        ->icon('phosphor-checks')
                        ->color('info')
                        ->action(fn ($record) => $record->update(['status' => EstimateStatus::Sent]))
                        ->requiresConfirmation()
                        ->modalHeading('Mark Estimate as Sent')
                        ->modalDescription('Are you sure you want to mark this estimate as Sent?')
                        ->visible(fn ($record) => $record->status === EstimateStatus::Draft),
                    Tables\Actions\Action::make('mark_as_declined')
                        ->label('Mark as Declined')
                        ->icon('heroicon-m-x-circle')
                        ->color('danger')
                        ->action(fn ($record) => $record->update(['status' => EstimateStatus::Declined]))
                        ->requiresConfirmation()
                        ->modalHeading('Mark Estimate as Declined')
                        ->modalDescription('Are you sure you want to mark this estimate as Declined?')
                        ->visible(fn ($record) => $record->status === EstimateStatus::Sent),
                    Tables\Actions\Action::make('convert')
                        ->label('Convert to Invoice')
                        ->icon('heroicon-m-arrow-right-circle')
                        ->color('success')
                        ->action(function ($record) {
                            $invoice = $record->convertToInvoice();

                            return redirect(InvoiceResource::getUrl('view', ['record' => $invoice]));
                        })
                        ->requiresConfirmation()
                        ->modalHeading('Convert Estimate to Invoice')
                        ->modalDescription('Are you sure you want to convert this estimate to an invoice? This action cannot be undone.')
                        ->visible(fn ($record) => $record->status !== EstimateStatus::Invoiced),
                    Tables\Actions\Action::make('view_invoice')
                        ->label('View Invoice')
                        ->icon('phosphor-invoice-fill')
                        ->url(fn ($record) => InvoiceResource::getUrl('view', ['record' => $record->invoice_id]))
                        ->visible(fn ($record) => $record->status === EstimateStatus::Invoiced && $record->invoice_id),
                    Tables\Actions\ActionGroup::make([
                        Tables\Actions\ViewAction::make(),
                        Tables\Actions\EditAction::make()
                            ->visible(fn ($record) => $record->status !== EstimateStatus::Invoiced),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->bulkActions([
            ])
            ->defaultSort('estimate_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEstimates::route('/'),
            'create' => Pages\CreateEstimate::route('/create'),
            'view' => Pages\ViewEstimate::route('/{record}'),
            'edit' => Pages\EditEstimate::route('/{record}/edit'),
        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewEstimate::class,
            Pages\EditEstimate::class,
        ]);
    }

    public static function getFormSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Forms\Components\Repeater::make('items')
                    ->relationship()
                    ->orderColumn('order_column')
                    ->reorderableWithButtons()
                    ->schema([
                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Item')
                                    ->relationship('product', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->requiredWithout('description')
                                    ->live()
                                    ->afterStateUpdated(function ($state, $set, $get) {
                                        $item = Product::query()->find($state);

                                        if ($item) {
                                            $set('name', $item->name ?? '');
                                            $set('description', $item->description ?? '');
                                            $set('unit_price', ($item->unit_price ?? 0) / ($get('../../exchange_rate') ?? 1));
                                        }
                                    })
                                    ->createOptionForm([
                                        Forms\Components\Select::make('category_id')
                                            ->label('Category')
                                            ->options(ProductCategory::query()->pluck('name', 'id'))
                                            ->searchable(),
                                        Forms\Components\TextInput::make('name')
                                            ->required(),
                                        Forms\Components\Textarea::make('description'),
                                        Forms\Components\TextInput::make('unit_price')
                                            ->label('Price')
                                            ->numeric()
                                            ->prefix('SAR')
                                            ->required(),
                                    ])
                                    ->createOptionAction(fn ($action) => $action->modalWidth('sm')),
                                Forms\Components\Hidden::make('name'),
                                Forms\Components\Textarea::make('description')
                                    ->rows(2)
                                    ->hiddenLabel()
                                    ->extraAttributes([
                                        'class' => 'text-sm',
                                    ]),
                            ])
                            ->columnSpan(['md' => 5]),

                        Forms\Components\TextInput::make('quantity')
                            ->numeric()
                            ->default(1)
                            ->live()
                            ->columnSpan(['md' => 2])
                            ->required(),

                        Forms\Components\Group::make()
                            ->schema([
                                Forms\Components\TextInput::make('unit_price')
                                    ->label('Unit Price')
                                    ->numeric()
                                    ->required()
                                    ->live()
                                    ->prefix(fn ($get) => $get('../../currency_code') ?? 'SAR'),
                                Forms\Components\Placeholder::make('subtotal')
                                    ->extraAttributes([
                                        'class' => 'tabular-nums',
                                    ])
                                    ->content(fn ($get) => money(floatval($get('unit_price') ?? 0) * floatval($get('quantity') ?? 0), $get('../../currency_code') ?? 'SAR', true)),
                            ])
                            ->columnSpan(['md' => 3]),
                    ])
                    ->defaultItems(1)
                    ->hiddenLabel()
                    ->columns(['md' => 10])
                    ->required(),
            ];
        }

        return [
            Forms\Components\Select::make('customer_id')
                ->relationship('customer', 'name')
                ->searchable()
                ->preload()
                ->required()
                ->createOptionForm([
                    Forms\Components\TextInput::make('name')
                        ->required(),
                    Forms\Components\TextInput::make('email')
                        ->required()
                        ->email()
                        ->unique(),
                    Forms\Components\TextInput::make('phone'),
                ])
                ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                    return $action
                        ->modalHeading('Create customer')
                        ->modalSubmitActionLabel('Create customer')
                        ->modalWidth('lg');
                }),

            Forms\Components\Select::make('group_id')
                ->label('Group')
                ->relationship('group', 'name', fn ($query, $get) => $query
                    ->select(['id', DB::raw("COALESCE(`name`, CONCAT('Group #', `id`)) as `name`")])
                    ->where('customer_id', $get('customer_id')))
                ->searchable()
                ->preload(),

            Forms\Components\Select::make('package_id')
                ->label('Package')
                ->relationship('package', 'title', fn ($query, $get) => $query
                    ->select([
                        'id',
                        DB::raw("
                            COALESCE(
                                CONCAT('#', `id`, ' - ', `title`),
                                CONCAT('Package #', `id`)
                            ) as `title`
                        "),
                    ])
                    ->where('customer_id', $get('customer_id')))
                ->searchable()
                ->preload()
                ->columnSpanFull(),

            Forms\Components\TextInput::make('subject')
                ->columnSpanFull(),

            Forms\Components\Textarea::make('notes')
                ->rows(5)
                ->columnSpanFull(),

            Forms\Components\Textarea::make('terms')
                ->label('Terms & conditions')
                ->default(Estimate::DEFAULT_TERMS)
                ->rows(10)
                ->columnSpanFull(),
        ];
    }

    public static function getInfolistSchema(?string $section = null): array
    {
        if ($section === 'items') {
            return [
                Infolists\Components\RepeatableEntry::make('items')
                    ->contained(false)
                    ->schema([
                        Infolists\Components\Group::make()
                            ->schema([
                                Infolists\Components\TextEntry::make('name')
                                    ->label('Item'),
                                Infolists\Components\TextEntry::make('description')
                                    ->hiddenLabel()
                                    ->formatStateUsing(fn ($state) => nl2br($state))
                                    ->html(),
                            ])
                            ->columnSpan(['md' => 5]),

                        Infolists\Components\TextEntry::make('quantity')
                            ->columnSpan(['md' => 2]),

                        Infolists\Components\TextEntry::make('unit_price')
                            ->label('Unit Price')
                            ->formatStateUsing(fn ($state, $record) => money($state, $record->estimate?->currency_code ?? 'SAR', true))
                            ->columnSpan(['md' => 3]),
                    ])
                    ->hiddenLabel()
                    ->columns(['md' => 10]),
            ];
        }

        return [
            Infolists\Components\TextEntry::make('customer.name'),
            Infolists\Components\TextEntry::make('subject'),
            Infolists\Components\TextEntry::make('notes')
                ->formatStateUsing(fn ($state) => nl2br($state))
                ->html()
                ->columnSpan('full'),
            Infolists\Components\TextEntry::make('terms')
                ->label('Terms & conditions')
                ->formatStateUsing(fn ($state) => nl2br($state))
                ->html()
                ->columnSpan('full'),
        ];
    }
}
