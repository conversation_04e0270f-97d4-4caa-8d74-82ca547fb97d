<?php

namespace App\Filament\Finance\Resources;

use App\Enums\CashTransactionType;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\CashTransactionResource\Pages;
use App\Filament\Finance\Resources\CashTransactionResource\Widgets;
use App\Models\Currency;
use App\Models\Finance\CashAccount;
use App\Models\Finance\CashTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class CashTransactionResource extends Resource
{
    protected static ?string $model = CashTransaction::class;

    protected static ?string $navigationGroup = 'Expenses';

    protected static ?string $navigationLabel = 'Cash & Bank';

    protected static ?int $navigationSort = 0;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('type'),
                Forms\Components\DatePicker::make('transaction_date')
                    ->default(now())
                    ->required(),
                Forms\Components\TextInput::make('details'),
                Forms\Components\Select::make('debit_account_id')
                    ->label(fn ($get) => match ($get('type')) {
                        'in' => 'Cash/Bank account',
                        'out' => 'Expense category',
                        'transfer' => 'To account'
                    })
                    ->relationship('debit_account', 'name', function ($query, $get) {
                        if ($get('type') == 'out') {
                            $query->isNotCashOrBank()
                                ->orderBy('code');
                        } else {
                            $query->isCashOrBank()
                                ->orderBy('code');
                        }
                    })
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\Select::make('credit_account_id')
                    ->label(fn ($get) => match ($get('type')) {
                        'in' => 'Income category',
                        'out' => 'Cash/Bank account',
                        'transfer' => 'From account'
                    })
                    ->relationship('credit_account', 'name', function ($query, $get) {
                        if ($get('type') == 'in') {
                            $query->isNotCashOrBank()
                                ->orderBy('code');
                        } else {
                            $query->isCashOrBank()
                                ->orderBy('code');
                        }
                    })
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\ToggleButtons::make('currency_code')
                    ->label('Currency')
                    ->options(Currency::getOptions())
                    ->default('SAR')
                    ->grouped()
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        $set('exchange_rate', 1 / Currency::getExchangeRate($state));
                    }),
                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->prefix(fn ($get) => $get('currency_code'))
                    ->required(),
                Forms\Components\TextInput::make('exchange_rate')
                    ->numeric()
                    ->default(1)
                    ->required()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                    ->visible(fn ($get) => $get('currency_code') != 'SAR'),
                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('cover')
                    ->disk('s3')
                    ->directory('finance/attachments')
                    ->visibility('public'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['debit_account', 'credit_account']))
            ->columns([
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge(),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('accounts')
                    ->getStateUsing(fn ($record) => $record->credit_account?->name . ' → ' . $record->debit_account?->name),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Account')
                    ->native(false)
                    ->options(fn () => CashAccount::query()
                        ->isCashOrBank()
                        ->orderBy('code')
                        ->pluck('name', 'id'))
                    ->modifyQueryUsing(fn ($query, $data) => $query
                        ->when($data['value'] ?? null, fn ($query, $value) => $query
                            ->where('debit_account_id', $value)
                            ->orWhere('credit_account_id', $value))),
                DateRangeFilter::make('transaction_date')
                    ->label('Date range')
                    ->withIndicator()
                    ->indicateUsing(function ($data, $filter) {
                        $datesString = data_get($data, 'transaction_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Date range: {$datesString}";
                    })
                    ->columnSpan(2),
                Tables\Filters\SelectFilter::make('type')
                    ->native(false)
                    ->options(CashTransactionType::class),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->modalHeading(fn ($record) => 'Edit ' . ($record->type == 'in' ? 'Kas Masuk' : 'Kas Keluar')),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->defaultSort('transaction_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCashTransactions::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            // Widgets\CashBankOverview::make(),
        ];
    }
}
