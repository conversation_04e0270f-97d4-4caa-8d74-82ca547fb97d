<?php

namespace App\Filament\Finance\Resources\JournalEntryResource\Pages;

use App\Filament\Finance\Resources\JournalEntryResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageJournalEntries extends ManageRecords
{
    protected static string $resource = JournalEntryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->fillForm([
                    'entry_date' => today(),
                    'items' => [
                        ['type' => 'd'],
                        ['type' => 'c'],
                    ],
                ]),
        ];
    }
}
