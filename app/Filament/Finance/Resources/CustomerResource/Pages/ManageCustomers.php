<?php

namespace App\Filament\Finance\Resources\CustomerResource\Pages;

use App\Filament\Finance\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageCustomers extends ManageRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('admin')
                ->color('gray')
                ->icon('heroicon-o-pencil-square')
                ->url(\App\Filament\Resources\CustomerResource::getUrl('index')),
        ];
    }
}
