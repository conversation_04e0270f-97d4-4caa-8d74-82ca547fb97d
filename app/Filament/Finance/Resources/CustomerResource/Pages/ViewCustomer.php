<?php

namespace App\Filament\Finance\Resources\CustomerResource\Pages;

use App\Filament\Finance\Resources\CustomerResource;
use App\Filament\Finance\Resources\CustomerResource\Widgets;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCustomer extends ViewRecord
{
    protected static string $resource = CustomerResource::class;

    protected static ?string $navigationIcon = 'heroicon-o-bars-3-bottom-left';

    protected static ?string $navigationLabel = 'Details';

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('statement')
                ->label('View Statement')
                ->url(route('customers.statements', ['customerUuid' => $this->record->uuid]))
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->openUrlInNewTab(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Widgets\CustomerOverview::class,
        ];
    }
}
