<?php

namespace App\Filament\Finance\Resources\CustomerResource\RelationManagers;

use App\Filament\Resources\GroupResource;
use App\Models\GroupCash;
use Awcodes\FilamentBadgeableColumn\Components\Badge;
use Awcodes\FilamentBadgeableColumn\Components\BadgeableColumn;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;

class GroupCashesRelationManager extends RelationManager
{
    protected static string $relationship = 'group_cashes';

    protected static ?string $recordTitleAttribute = 'description';

    protected static ?string $title = 'Cashflow';

    protected static ?string $modelLabel = 'cashflow';

    protected static ?string $pluralModelLabel = 'cashflow';

    public function getRelationship(): Relation|Builder
    {
        return $this->getOwnerRecord()->{static::getRelationshipName()}()
            ->currentPeriod()
            ->where('division', 'not like', 'mutawif%');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('User')
                    ->description(fn ($record) => GroupCash::DIVISIONS[$record->division] ?? null),
                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->url(fn (GroupCash $record) => $record->group ? GroupResource::getUrl('view', ['record' => $record->group]) : null)
                    ->getStateUsing(fn ($record) => $record->group?->customer->name ?? null)
                    ->description(fn ($record) => $record->group?->name ?? null),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                BadgeableColumn::make('description')
                    ->wrap()
                    ->description(fn ($record) => $record->cashed_at, 'above')
                    ->description(fn ($record) => $record->category?->name ?? null)
                    ->suffixBadges([
                        Badge::make('is_excluded')
                            ->label('Exclude')
                            ->color('warning')
                            ->visible(fn ($record) => $record->is_excluded),
                    ]),
                ...GroupCash::getAmountTableColumns(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //
            ])
            ->actions([
                //
            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make(),
            ])
            ->defaultSort('cashed_at', 'desc')
            ->emptyStateDescription(null);
    }
}
