<?php

namespace App\Filament\Finance\Resources\CustomerResource\Widgets;

use App\Models\Customer;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class CustomerOverview extends BaseWidget
{
    public ?Customer $record = null;

    protected function getCards(): array
    {
        $data = Customer::leftJoinRelationship('groups.cashes', [
            'groups' => fn ($join) => $join->currentPeriod(true),
        ])
            ->select([
                'customers.id',
                DB::raw("SUM(IF(`group_cashes`.`division` = 'payment', IFNULL(`group_cashes`.`cash_in`, 0) / `group_cashes`.`exchange_rate`, 0)) AS payment"),
                DB::raw("SUM(IF(`group_cashes`.`division` != 'payment' AND `group_cashes`.`division` not like 'mutawif%', IFNULL(`group_cashes`.`cash_out`, 0) / `group_cashes`.`exchange_rate`, 0)) AS expense"),
                DB::raw("SUM(IF(`group_cashes`.`division` = 'payment', IFNULL(`group_cashes`.`cash_in`, 0) / `group_cashes`.`exchange_rate`, 0)) - SUM(IF(`group_cashes`.`division` != 'payment' AND `group_cashes`.`division` not like 'mutawif%', IFNULL(`group_cashes`.`cash_out`, 0) / `group_cashes`.`exchange_rate`, 0)) AS balance"),
            ])
            ->groupBy([
                'customers.id',
            ])
            ->find($this->record->id);

        return [
            Stat::make('Payment', money($data->payment, 'SAR', true)),
            Stat::make('Expense', money($data->expense, 'SAR', true)),
            Stat::make('Balance', money($data->balance, 'SAR', true)),
        ];
    }
}
