<?php

namespace App\Filament\Finance\Resources\CustomerResource\Widgets;

use App\Models\Currency;
use App\Models\Finance\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class CustomerInvoiceOverview extends BaseWidget
{
    public ?Model $record = null;

    protected static bool $isLazy = false;

    protected function getColumns(): int
    {
        return 2;
    }

    protected function getStats(): array
    {
        $data = $this->getData();
        $rate = Currency::getExchangeRate('IDR');

        return [
            Stat::make('Invoice', money($data->total_invoice ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->total_invoice * $rate, 'IDR', true)),
            Stat::make('Payment', money($data->total_payment ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->total_payment * $rate, 'IDR', true)),
            Stat::make('Receivable', money($data->balance ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->balance * $rate, 'IDR', true)),
            Stat::make('Overdue', money($data->overdue_balance ?? 0, 'SAR', true))
                ->description('≈ ' . money($data->overdue_balance * $rate, 'IDR', true))
                ->color('danger'),
        ];
    }

    protected function getData()
    {
        return Invoice::query()
            ->where('customer_id', $this->record->id)
            ->select([
                DB::raw('SUM(`total` * `exchange_rate`) AS total_invoice'),
                DB::raw('SUM(`paid` * `exchange_rate`) AS total_payment'),
                DB::raw('SUM((`total` * `exchange_rate`) - (`paid` * `exchange_rate`)) AS balance'),
                DB::raw('SUM(CASE WHEN `status` = "overdue" THEN (`total` * `exchange_rate`) - (`paid` * `exchange_rate`) ELSE 0 END) AS overdue_balance'),
            ])
            ->first();
    }
}
