<?php

namespace App\Filament\Finance\Resources\OrderResource\Pages;

use App\Filament\Finance\Resources\OrderResource;
use App\Models\Finance\Order;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getTableQuery(): Builder
    {
        return Order::query()
            ->leftJoinSub(
                DB::table('order_items')
                    ->select([
                        'order_id',
                        DB::raw('COUNT(*) as items_count'),
                        DB::raw('SUM(quantity * unit_price) as total_price'),
                    ])
                    ->groupBy('order_id'),
                'items',
                'orders.id',
                '=',
                'items.order_id',
            );
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
