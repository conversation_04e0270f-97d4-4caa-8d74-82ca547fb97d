<?php

namespace App\Filament\Finance\Resources\OrderResource\Pages;

use App\Actions\Invoice\GeneratePDF;
use App\Filament\Finance\Resources\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewOrder extends ViewRecord
{
    protected static string $resource = OrderResource::class;

    protected static string $view = 'filament.finance.resources.order-resource.pages.view-order';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'View';

    protected function getHeaderActions(): array
    {
        return [
            // Actions\Action::make('pdf')
            //     ->label('PDF')
            //     ->action(function ($record) {
            //         return response()->download(GeneratePDF::run($record));
            //     })
            //     ->color('gray')
            //     ->icon('heroicon-o-arrow-down-tray'),
        ];
    }
}
