<?php

namespace App\Filament\Finance\Resources\PaymentVoucherResource\Pages;

use App\Filament\Finance\Resources\PaymentVoucherResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManagePaymentVouchers extends ManageRecords
{
    protected static string $resource = PaymentVoucherResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
