<?php

namespace App\Filament\Finance\Resources;

use App\Enums\Finance\AccountCategory;
use App\Filament\Finance\Resources\CashAccountResource\Pages;
use App\Models\Finance\CashAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class CashAccountResource extends Resource
{
    protected static ?string $model = CashAccount::class;

    protected static ?string $modelLabel = 'account';

    protected static ?string $navigationLabel = 'Chart of Accounts';

    protected static ?string $navigationGroup = 'Settings';

    protected static ?int $navigationSort = 0;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('category')
                    ->options(AccountCategory::getOptions())
                    ->default(AccountCategory::CurrentAsset)
                    ->native(false)
                    ->required(),
                Forms\Components\TextInput::make('code')
                    ->unique(ignoreRecord: true)
                    ->label('Code'),
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\Textarea::make('description'),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        $startDate = today()->startOfYear();
        $endDate = today();

        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query
                ->select([
                    'cash_accounts.id',
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.description',
                    'cash_accounts.category',
                    'cash_accounts.is_fixed',
                    DB::raw("
                        COALESCE(
                            SUM(IF(journal_entry_items.type = 'd' AND journal_entries.entry_date < ?, journal_entry_items.amount, 0)) -
                            SUM(IF(journal_entry_items.type = 'c' AND journal_entries.entry_date < ?, journal_entry_items.amount, 0))
                        , 0) AS starting_balance
                    "),
                    DB::raw("
                        COALESCE(
                            SUM(IF(journal_entry_items.type = 'd' AND journal_entries.entry_date BETWEEN ? AND ?, journal_entry_items.amount, 0)) -
                            SUM(IF(journal_entry_items.type = 'c' AND journal_entries.entry_date BETWEEN ? AND ?, journal_entry_items.amount, 0))
                        , 0) AS net_movement
                    "),
                    DB::raw('COUNT(journal_entry_items.id) AS journal_entry_items_count'),
                ])
                ->leftJoin('journal_entry_items', 'cash_accounts.id', '=', 'journal_entry_items.account_id')
                ->leftJoin('journal_entries', function (JoinClause $join) use ($endDate) {
                    $join->on('journal_entry_items.entry_id', '=', 'journal_entries.id')
                        ->where('journal_entries.entry_date', '<=', $endDate);
                })
                ->groupBy([
                    'cash_accounts.id',
                    'cash_accounts.code',
                    'cash_accounts.name',
                    'cash_accounts.description',
                    'cash_accounts.category',
                    'cash_accounts.is_fixed',
                ])
                ->withCasts([
                    'starting_balance' => 'float',
                    'net_movement' => 'float',
                ])
                ->addBinding([$startDate, $startDate, $startDate, $endDate, $startDate, $endDate], 'select')
            )
            ->recordUrl(fn ($record) => GeneralLedgerResource::getUrl('index', [
                'tableFilters[account_id][value]' => $record->id,
                'tableFilters[entry_date][entry_date]' => today()->startOfYear()->format('d/m/Y') . ' - ' . today()->endOfYear()->format('d/m/Y'),
            ]))
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->sortable()
                    ->extraAttributes([
                        'class' => 'tabular-nums',
                    ])
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->description(fn ($record) => $record->description)
                    ->searchable(),
                Tables\Columns\TextColumn::make('category')
                    ->badge(),
                // Tables\Columns\TextColumn::make('starting_balance'),
                // Tables\Columns\TextColumn::make('net_movement'),
                Tables\Columns\TextColumn::make('balance')
                    ->label('Balance')
                    ->money('SAR', true)
                    ->alignEnd()
                    ->getStateUsing(fn ($record) => $record->category->isReal()
                        ? ($record->starting_balance + $record->net_movement) * ($record->category->isNormalDebitBalance() ? 1 : -1)
                        : $record->net_movement * ($record->category->isNormalDebitBalance() ? 1 : -1)
                    )
                    ->extraAttributes([
                        'class' => 'tabular-nums',
                    ]),
            ])
            ->defaultGroup(
                Tables\Grouping\Group::make('category')
                    ->orderQueryUsing(fn ($query) => $query
                        ->orderByRaw("FIELD(`category`, '" . collect(AccountCategory::orderedCases())->map(fn ($c) => $c->value)->join("','") . "')"))
                    ->titlePrefixedWithLabel(false)
            )
            ->groupingSettingsHidden()
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options(AccountCategory::getOptions())
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->modalWidth('sm')
                        ->visible(fn ($record) => ! $record->is_fixed),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn ($record) => $record->journal_entry_items_count === 0 && ! $record->is_fixed),
                ]),
            ])
            ->paginated(false)
            ->defaultSort('code');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCashAccounts::route('/'),
        ];
    }
}
