<?php

namespace App\Filament\Finance\Resources\BillPaymentResource\Pages;

use App\Filament\Finance\Resources\BillPaymentResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageBillPayments extends ManageRecords
{
    protected static string $resource = BillPaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
