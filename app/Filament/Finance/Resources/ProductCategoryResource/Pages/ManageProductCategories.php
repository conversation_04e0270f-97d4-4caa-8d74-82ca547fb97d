<?php

namespace App\Filament\Finance\Resources\ProductCategoryResource\Pages;

use App\Filament\Finance\Resources\ProductCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageProductCategories extends ManageRecords
{
    protected static string $resource = ProductCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('sm'),
        ];
    }
}
