<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\GeneralLedgerResource\Pages;
use App\Filament\Finance\Resources\GeneralLedgerResource\Widgets;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntryItem;
use Carbon\Carbon;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Illuminate\Support\Facades\DB;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class GeneralLedgerResource extends Resource
{
    protected static ?string $model = JournalEntryItem::class;

    protected static ?string $modelLabel = 'item';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationIcon = 'phosphor-book';

    protected static ?string $navigationLabel = 'Buku Besar';

    protected static ?int $navigationSort = 4;

    protected static ?string $slug = 'general-ledger';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query
                ->leftJoin('journal_entries', 'journal_entries.id', '=', 'journal_entry_items.entry_id')
                ->select([
                    'journal_entries.entry_date',
                    'journal_entries.details',
                    'journal_entry_items.*',
                    DB::raw("IF(type = 'd', amount, null) AS debit"),
                    DB::raw("IF(type = 'c', amount, null) AS credit"),
                ]))
            ->columns([
                Tables\Columns\TextColumn::make('entry_date')
                    ->label('Date')
                    ->sortable()
                    ->date(),
                Tables\Columns\TextColumn::make('details')
                    ->label('Details')
                    ->wrap()
                    ->searchable(),
                Tables\Columns\TextColumn::make('debit')
                    ->label('Debit')
                    ->summarize(
                        Sum::make()->currency('SAR', true)
                            ->label('Total')
                    )
                    ->currencyRight(),
                Tables\Columns\TextColumn::make('credit')
                    ->label('Credit')
                    ->summarize(
                        Sum::make()->currency('SAR', true)
                            ->label('Total')
                    )
                    ->currencyRight(),
            ])
            ->filters([
                DateRangeFilter::make('entry_date')
                    ->label('Date range')
                    ->withIndicator()
                    ->disableClear()
                    ->ranges(function ($component) {
                        $period = current_period();

                        return [
                            __('filament-daterangepicker-filter::message.today') => [$component->now(), $component->now()],
                            __('filament-daterangepicker-filter::message.yesterday') => [$component->now()->subDay(), $component->now()->subDay()],
                            __('filament-daterangepicker-filter::message.last_7_days') => [$component->now()->subDays(6), $component->now()],
                            __('filament-daterangepicker-filter::message.last_30_days') => [$component->now()->subDays(29), $component->now()],
                            __('filament-daterangepicker-filter::message.this_month') => [$component->now()->startOfMonth(), $component->now()->endOfMonth()],
                            __('filament-daterangepicker-filter::message.last_month') => [$component->now()->subMonth()->startOfMonth(), $component->now()->subMonth()->endOfMonth()],
                            __('filament-daterangepicker-filter::message.this_year') => [$component->now()->startOfYear(), $component->now()->endOfYear()],
                            __('filament-daterangepicker-filter::message.last_year') => [$component->now()->subYear()->startOfYear(), $component->now()->subYear()->endOfYear()],
                            'This Period' => [$period->date_start, $period->date_end],
                        ];
                    })
                    ->indicateUsing(function ($data, $filter) {
                        $datesString = data_get($data, 'entry_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Date range: {$datesString}";
                    })
                    ->columnSpan(2)
                    ->startDate(Carbon::today()->startOfMonth())
                    ->endDate(Carbon::today()->endOfMonth()),
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Account')
                    ->relationship('account', 'name', fn ($query) => $query->orderBy('code'))
                    ->default(CashAccount::orderBy('code')->first()->id)
                    ->selectablePlaceholder(false),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'd' => 'Debit',
                        'c' => 'Credit',
                    ]),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                // Tables\Actions\EditAction::make(),
                // Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->defaultSort('entry_date');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageGeneralLedgers::route('/'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            Widgets\GeneralLedgerOverview::class,
        ];
    }
}
