<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Resources\UserCashResource as ResourcesUserCashResource;

class UserCashResource extends ResourcesUserCashResource
{
    protected static ?string $navigationLabel = 'Advance Cash';

    protected static ?string $navigationGroup = 'Expenses';

    protected static ?int $navigationSort = 2;

    public static function getRouteBaseName(?string $panel = null): string
    {
        return parent::getRouteBaseName('finance');
    }
}
