<?php

namespace App\Filament\Finance\Resources;

use App\Enums\PaymentMethod;
use App\Filament\Actions\ActivityLogTimelineTableAction;
use App\Filament\Finance\Resources\InvoiceRefundResource\Pages;
use App\Models\Finance\CashAccount;
use App\Models\Finance\InvoiceRefund;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class InvoiceRefundResource extends Resource
{
    protected static ?string $model = InvoiceRefund::class;

    protected static ?string $modelLabel = 'refund';

    protected static ?string $navigationGroup = 'Sales';

    protected static ?int $navigationSort = 35;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('invoice_id')
                    ->label('Invoice')
                    ->relationship('invoice', 'invoice_number')
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Hidden::make('currency_code')
                    ->default('SAR'),
                Forms\Components\ToggleButtons::make('payment_method')
                    ->grouped()
                    ->options(PaymentMethod::class)
                    ->default(PaymentMethod::Cash->value)
                    ->live()
                    ->afterStateUpdated(function ($state, $set) {
                        if ($state === 'deposit') {
                            $set('cash_account_id', CashAccount::query()
                                ->where('code', config('finance.coa.customer_deposit'))
                                ->first()->id);
                        }
                    }),
                Forms\Components\Select::make('cash_account_id')
                    ->label('Account')
                    ->relationship('cash_account', 'name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->disabled(fn ($get) => $get('payment_method') === 'deposit'),
                Forms\Components\DateTimePicker::make('refunded_at')
                    ->label('Date')
                    ->default(today())
                    ->required(),
                Forms\Components\TextInput::make('description')
                    ->autocomplete('off')
                    ->maxLength(255),

                Forms\Components\TextInput::make('amount')
                    ->numeric()
                    ->required()
                    ->prefix(fn ($get) => $get('currency_code') ?? 'SAR'),

                Forms\Components\TextInput::make('exchange_rate')
                    ->required()
                    ->numeric()
                    ->live()
                    ->helperText(fn ($state, $get) => $state > 0 && $state < 1 ? 'SAR 1 = ' . $get('currency_code') . ' ' . round(1 / $state, 2) : null)
                    ->visible(fn ($get) => $get('currency_code') != 'SAR'),

                Forms\Components\FileUpload::make('attachment')
                    ->imageResizeTargetWidth('720')
                    ->imageResizeTargetHeight('720')
                    ->imageResizeMode('cover')
                    ->disk('s3')
                    ->directory('invoice/refunds')
                    ->visibility('public'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['invoice', 'cash_account']))
            ->columns([
                Tables\Columns\TextColumn::make('invoice.invoice_number')
                    ->label('Invoice'),
                Tables\Columns\TextColumn::make('id')
                    ->label('No.'),
                Tables\Columns\TextColumn::make('refunded_at')
                    ->label('Date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Via')
                    ->badge(),
                Tables\Columns\TextColumn::make('cash_account.name')
                    ->label('Account'),
                Tables\Columns\TextColumn::make('description')
                    ->wrap(),
                Tables\Columns\IconColumn::make('attachment')
                    ->attachment(),
                Tables\Columns\TextColumn::make('amount')
                    ->currencyAuto(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                    Tables\Actions\ActionGroup::make([
                        ActivityLogTimelineTableAction::make('history'),
                    ])
                        ->dropdown(false),
                ]),
            ])
            ->defaultSort('refunded_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageInvoiceRefunds::route('/'),
        ];
    }
}
