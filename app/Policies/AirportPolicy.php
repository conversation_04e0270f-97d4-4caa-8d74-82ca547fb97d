<?php

namespace App\Policies;

use App\Models\Airport;
use App\Models\User;

class AirportPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('airports.viewAny') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Airport $airport): bool
    {
        return $user->can('airports.view') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('airports.create') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Airport $airport): bool
    {
        return $user->can('airports.update') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Airport $airport): bool
    {
        return $user->can('airports.delete') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Airport $airport): bool
    {
        return $user->can('airports.restore') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Airport $airport): bool
    {
        return $user->can('airports.forceDelete') || $user->hasRole('Admin');
    }
}
