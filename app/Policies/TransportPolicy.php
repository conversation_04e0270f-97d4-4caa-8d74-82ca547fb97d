<?php

namespace App\Policies;

use App\Models\Transport;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TransportPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('transports.viewAny') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Transport $transport)
    {
        return $user->can('transports.view') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('transports.create') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Transport $transport)
    {
        return $user->can('transports.update') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Transport $transport)
    {
        return $user->can('transports.delete') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Transport $transport)
    {
        return $user->can('transports.restore') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Transport $transport)
    {
        return $user->can('transports.forceDelete') || $user->hasRole('Admin');
    }
}
