<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vendors\HotelBroker;
use Illuminate\Auth\Access\HandlesAuthorization;

class HotelBrokerPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('hotel-brokers.viewAny') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, HotelBroker $hotelBroker)
    {
        return $user->can('hotel-brokers.view') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('hotel-brokers.create') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, HotelBroker $hotelBroker)
    {
        return $user->can('hotel-brokers.update') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, HotelBroker $hotelBroker)
    {
        return $user->can('hotel-brokers.delete') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, HotelBroker $hotelBroker)
    {
        return $user->can('hotel-brokers.restore') || $user->hasRole('Admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, HotelBroker $hotelBroker)
    {
        return $user->can('hotel-brokers.forceDelete') || $user->hasRole('Admin');
    }
}
