<?php

namespace App\Policies;

use App\Models\Hotel;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class HotelPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('hotels.viewAny') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Hotel $hotel)
    {
        return $user->can('hotels.view') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('hotels.create') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Hotel $hotel)
    {
        return $user->can('hotels.update') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Hotel $hotel)
    {
        return $user->can('hotels.delete') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Hotel $hotel)
    {
        return $user->can('hotels.restore') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Hotel $hotel)
    {
        return $user->can('hotels.forceDelete') || $user->hasRole(['Admin']);
    }
}
