<?php

namespace App\Policies\Finance;

use App\Models\Finance\InvoicePayment;
use App\Models\User;

class InvoicePaymentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('invoice-payments.viewAny');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, InvoicePayment $invoicePayment): bool
    {
        return $user->can('invoice-payments.view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('invoice-payments.create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, InvoicePayment $invoicePayment): bool
    {
        return $user->can('invoice-payments.update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, InvoicePayment $invoicePayment): bool
    {
        return $user->can('invoice-payments.delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, InvoicePayment $invoicePayment): bool
    {
        return $user->can('invoice-payments.restore');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, InvoicePayment $invoicePayment): bool
    {
        return $user->can('invoice-payments.forceDelete');
    }
}
