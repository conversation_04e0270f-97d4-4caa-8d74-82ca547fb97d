<?php

namespace App\Policies\Finance;

use App\Models\Finance\CashAccount;
use App\Models\User;

class CashAccountPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('panel.finance');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CashAccount $cashAccount): bool
    {
        return $user->can('panel.finance');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('panel.finance');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CashAccount $cashAccount): bool
    {
        return ! $cashAccount->is_fixed;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CashAccount $cashAccount): bool
    {
        return ! $cashAccount->is_fixed;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CashAccount $cashAccount): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CashAccount $cashAccount): bool
    {
        return false;
    }
}
