<?php

namespace App\Policies;

use App\Models\GroupData;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class GroupDataPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('groups.viewData') || $user->hasRole(['Admin', 'Operator', 'Airport Handler']);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, GroupData $groupData)
    {
        return $user->can('groups.viewData') || $user->hasRole(['Admin', 'Operator', 'Airport Handler']);
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('groups.updateData') || $user->hasRole(['Admin', 'Operator']);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, GroupData $groupData)
    {
        return $user->can('groups.updateData') || $user->hasRole(['Admin', 'Operator']);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, GroupData $groupData)
    {
        return $user->can('groups.deleteData') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, GroupData $groupData)
    {
        return $user->can('groups.deleteData') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, GroupData $groupData)
    {
        return $user->can('groups.deleteData') || $user->hasRole(['Admin']);
    }
}
