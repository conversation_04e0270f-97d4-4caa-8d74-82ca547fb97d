<?php

namespace App\Policies;

use App\Models\Group;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class GroupPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->can('groups.viewAny') || $user->hasRole(['Admin', 'Operator', 'Mutawif', 'Airport Handler', 'Finance']);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Group $group)
    {
        if ($user->hasExactRoles('Customer')) {
            return $group->customer->user_id == $user->id;
        }
        if ($user->hasExactRoles('Airport Handler')) {
            $userVendorIds = $user->vendors()->pluck('id')->toArray();

            return $group->flights()->whereIn('handler_id', $userVendorIds)->exists();
        }
        // TODO
        if ($user->can('groups.view')) {
            return true;
        }

        return true;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->can('groups.create') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Group $group)
    {
        return $user->can('groups.update') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Group $group)
    {
        return $user->can('groups.delete') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Group $group)
    {
        return $user->can('groups.restore') || $user->hasRole(['Admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Group $group)
    {
        return $user->can('groups.forceDelete') || $user->hasRole(['Admin']);
    }
}
