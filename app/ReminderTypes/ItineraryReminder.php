<?php

namespace App\ReminderTypes;

use App\Models\Itinerary;
use Carbon\Carbon;
use Filament\Forms;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class ItineraryReminder extends ReminderType
{
    protected static string $model = Itinerary::class;

    protected static string $defaultTemplate = 'Reminder for group {group_name} ({customer_name}): {itinerary_description} ({itinerary_location}) is scheduled for {itinerary_date}.';

    protected static array $templateTags = [
        'group_name' => 'The name of the group associated with the itinerary.',
        'customer_name' => 'The name of the customer associated with the group.',
        'itinerary_description' => 'The description of the itinerary item.',
        'itinerary_date' => 'The date of the itinerary item.',
        'itinerary_location' => 'The location of the itinerary item.',
    ];

    public static function getAdditionalFormSchema(): array
    {
        return [
            Forms\Components\TextInput::make('itinerary_filter')
                ->label('Filter Itineraries')
                ->placeholder('Enter keyword to filter itineraries')
                ->helperText('Only itineraries with descriptions containing this keyword will be included'),
        ];
    }

    public static function findRelevantModels(int $interval, array $additionalData = []): Collection
    {
        $query = Itinerary::query()
            ->whereDate('date', '>=', now())
            ->whereDate('date', '<=', now()->addDays(abs($interval)));

        if (! empty($additionalData['itinerary_filter'])) {
            $query->where(function ($query) use ($additionalData) {
                $query->where('description', 'like', '%'.$additionalData['itinerary_filter'].'%')
                    ->orWhere('location', 'like', '%'.$additionalData['itinerary_filter'].'%');
            });
        }

        return $query->get();
    }

    public static function getEventDate(Model $model): ?Carbon
    {
        return $model->date;
    }

    public static function getTagsValues(Model $model): array
    {
        return [
            'group_name' => $model->group->name,
            'customer_name' => $model->customer->name,
            'itinerary_description' => $model->description,
            'itinerary_date' => $model->date->format('j F Y H:i'),
            'itinerary_location' => $model->location,
        ];
    }
}
