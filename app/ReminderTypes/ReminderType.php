<?php

namespace App\ReminderTypes;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

abstract class ReminderType
{
    protected static ?string $label = null;

    protected static string $model = Model::class;

    protected static string $defaultTemplate = '';

    protected static array $templateTags = [];

    public static function getLabel(): string
    {
        return static::$label ?? str(class_basename(static::class))->headline();
    }

    public static function getDefaultTemplate(): string
    {
        return static::$defaultTemplate;
    }

    public static function getTemplateTags(): array
    {
        return static::$templateTags;
    }

    public static function findRelevantModels(int $interval, array $additionalData = []): Collection
    {
        return new Collection();
    }

    public static function getEventDate(Model $model): ?Carbon
    {
        return null;
    }

    public static function getTagsValues(Model $model): array
    {
        return [];
    }

    public static function getModelClass(): string
    {
        return static::$model;
    }

    public static function getAdditionalFormSchema(): array
    {
        return [];
    }
}
