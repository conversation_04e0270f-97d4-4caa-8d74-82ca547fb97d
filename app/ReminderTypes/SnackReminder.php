<?php

namespace App\ReminderTypes;

use App\Models\Itinerary;
use Carbon\Carbon;
use Filament\Forms;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class SnackReminder extends ReminderType
{
    protected static string $model = Itinerary::class;

    protected static string $defaultTemplate = 'Reminder for group {group_name} ({customer_name}): {description} at {location}, {city} is scheduled for {date} at {time}. Total pax: {total_pax}. Hotels: {hotels}. For assistance, contact Mutawif at {mutawif_contact}.';

    protected static array $templateTags = [
        'group_name' => 'The name of the group associated with the itinerary.',
        'customer_name' => 'The name of the customer associated with the group.',
        'total_pax' => 'The total number of passengers in the group.',
        'description' => 'The description of the itinerary item.',
        'date' => 'The date of the itinerary item.',
        'time' => 'The time of the itinerary item.',
        'location' => 'The location of the itinerary item.',
        'city' => 'The city of the itinerary item.',
        'hotels' => 'The hotels associated with the itinerary item.',
        'mutawif_contact' => 'The contact of the mutawif associated with the itinerary.',
        'tour_leader_contact' => 'The contact of the tour leader associated with the itinerary.',
        'snack_type' => 'The snack type.',
    ];

    public static function getAdditionalFormSchema(): array
    {
        return [
            Forms\Components\Select::make('city')
                ->options([
                    'Makkah' => 'Makkah',
                    'Madinah' => 'Madinah',
                ]),
        ];
    }

    public static function findRelevantModels(int $interval, array $additionalData = []): Collection
    {
        $query = Itinerary::query()
            ->where('has_snack', true)
            ->whereDate('date', '>=', now())
            ->whereDate('date', '<=', now()->addDays(abs($interval)));

        if (! empty($additionalData['city'])) {
            $query->where(function ($query) use ($additionalData) {
                $query->where('city', $additionalData['city']);
            });
        }

        return $query->get();
    }

    public static function getEventDate(Model $model): ?Carbon
    {
        return $model->date;
    }

    public static function getTagsValues(Model $model): array
    {
        return [
            'group_name' => $model->group->name,
            'customer_name' => $model->customer->name,
            'total_pax' => $model->group->total_pax,
            'description' => $model->description,
            'date' => $model->date->format('j F Y H:i'),
            'time' => $model->date->format('H:i'),
            'location' => $model->location,
            'city' => $model->city,
            'hotels' => $model->group->hotels()
                ->when($model->city, fn ($query) => $query->where('city', $model->city))
                ->get()
                ->map(fn ($hotel) => $hotel->name)->implode(', '),
            'mutawif_contact' => $model->group->mutawifs
                ->map(fn ($mutawif) => "{$mutawif->name} ({$mutawif->phone})")
                ->implode(', '),
            'tour_leader_contact' => $model->group->tour_leader
                ? "{$model->group->tour_leader->name} ({$model->group->tour_leader->phone})"
                : '',
            'snack_type' => $model->snack_details,
        ];
    }
}
