<?php

namespace App\ReminderTypes;

use App\Models\GroupHotel;
use App\Models\Hotel;
use Carbon\Carbon;
use Filament\Forms;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class HotelCheckOut extends ReminderType
{
    protected static ?string $label = 'Hotel Check-Out';

    protected static string $model = GroupHotel::class;

    protected static string $defaultTemplate = 'Dear {recipient_name}, this is a reminder that the group {group_name} (associated with {customer_name}) is scheduled to check out from {hotel_name} on {check_out_date}.';

    protected static array $templateTags = [
        'recipient_name' => 'The name of the recipient receiving this notification.',
        'customer_name' => 'The name of the customer associated with the group.',
        'group_name' => 'The designated name of the group checking out.',
        'check_out_date' => 'The scheduled date for hotel check-out.',
        'hotel_name' => 'The name of the hotel where the group is checking out from.',
    ];

    public static function getAdditionalFormSchema(): array
    {
        return [
            Forms\Components\Select::make('city')
                ->options(Hotel::getCities())
                ->searchable(),
        ];
    }

    public static function findRelevantModels(int $interval, array $additionalData = []): Collection
    {
        $query = GroupHotel::query()
            ->whereHas('group', fn ($query) => $query->confirmed())
            ->whereDateBetween('check_out', [now(), now()->addDays(abs($interval))]);

        if (! empty($additionalData['city'])) {
            $query->whereHas('hotel', function ($query) use ($additionalData) {
                $query->where('city', $additionalData['city']);
            });
        }

        return $query->get();
    }

    public static function getEventDate(Model $model): ?Carbon
    {
        return $model->check_out;
    }

    public static function getTagsValues(Model $model): array
    {
        return [
            'customer_name' => $model->group?->customer?->name ?? '-',
            'group_name' => $model->group?->name ?? '-',
            'check_out_date' => $model->check_out?->format('j F Y') ?? 'N/A',
            'hotel_name' => $model->hotel?->fullname ?? 'N/A',
        ];
    }
}
