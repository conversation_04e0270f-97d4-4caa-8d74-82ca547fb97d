<?php

use App\Models\GroupFlight;
use App\Models\Itinerary;
use App\Models\Reminder;
use App\ReminderTypes\Departure;
use App\ReminderTypes\SnackReminder;
use App\Services\ReminderService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\Finder\SplFileInfo;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->reminderService = app(ReminderService::class);

    // Create a mock filesystem to control the reminder types that are found
    $this->filesystem = Mockery::mock(Filesystem::class);
    app()->instance(Filesystem::class, $this->filesystem);

    // Set up the mock to return our test reminder types
    mockReminderTypeFiles($this);
});

test('getReminderTypes returns array of reminder type classes', function () {
    $types = $this->reminderService->getReminderTypes();

    expect($types)
        ->toBeArray()
        ->toContain(Departure::class)
        ->toContain(SnackReminder::class);
});

test('getReminderTypesOptions returns array of reminder type options', function () {
    $options = $this->reminderService->getReminderTypesOptions();

    expect($options)
        ->toBeArray()
        ->toHaveKey('Departure')
        ->toHaveKey('SnackReminder');

    expect($options['Departure'])->toBe(Departure::getLabel());
    expect($options['SnackReminder'])->toBe(SnackReminder::getLabel());
});

test('getLabel returns correct label for reminder type', function () {
    $label = $this->reminderService->getLabel('Departure');

    expect($label)->toBe(Departure::getLabel());
});

test('getDefaultTemplate returns correct template for reminder type', function () {
    $template = $this->reminderService->getDefaultTemplate('Departure');

    expect($template)->toBe(Departure::getDefaultTemplate());
});

test('getTemplateTags returns correct tags for reminder type', function () {
    $tags = $this->reminderService->getTemplateTags('Departure');

    expect($tags)->toBe(Departure::getTemplateTags());
});

test('getModelClass returns correct model class for reminder type', function () {
    $modelClass = $this->reminderService->getModelClass('Departure');

    expect($modelClass)->toBe(GroupFlight::class);

    $modelClass = $this->reminderService->getModelClass('SnackReminder');

    expect($modelClass)->toBe(Itinerary::class);
});

test('findRelevantModels calls the correct reminder type method', function () {
    // Create a mock reminder type
    $mockReminderType = Mockery::mock('overload:' . app()->getNamespace() . 'ReminderTypes\TestType');
    $mockReminderType->shouldReceive('findRelevantModels')
        ->once()
        ->with(7, ['city' => 'Makkah'])
        ->andReturn(new Collection);

    $this->reminderService->findRelevantModels('TestType', 7, ['city' => 'Makkah']);
});

test('getEventDate calls the correct reminder type method', function () {
    // Create a mock reminder type
    $mockReminderType = Mockery::mock('overload:' . app()->getNamespace() . 'ReminderTypes\TestType');
    $mockReminderType->shouldReceive('getEventDate')
        ->once()
        ->andReturn(Carbon::now());

    $model = Mockery::mock(Model::class);
    $this->reminderService->getEventDate('TestType', $model);
});

test('getTagsValues calls the correct reminder type method', function () {
    // Create a mock reminder type
    $mockReminderType = Mockery::mock('overload:' . app()->getNamespace() . 'ReminderTypes\TestType');
    $mockReminderType->shouldReceive('getTagsValues')
        ->once()
        ->andReturn(['tag1' => 'value1']);

    $model = Mockery::mock(Model::class);
    $result = $this->reminderService->getTagsValues('TestType', $model);

    expect($result)->toBe(['tag1' => 'value1']);
});

test('generateContent replaces template tags with values', function () {
    // Create a mock reminder
    $reminder = new Reminder;
    $reminder->reminder_type = 'TestType';
    $reminder->template = 'Hello {tag1} and {tag2}!';

    // Create a mock model
    $model = Mockery::mock(Model::class);

    // Mock the getTagsValues method
    $this->reminderService = Mockery::mock(ReminderService::class)->makePartial();
    $this->reminderService->shouldReceive('getTagsValues')
        ->once()
        ->with('TestType', $model)
        ->andReturn([
            'tag1' => 'World',
            'tag2' => 'Universe',
        ]);

    $content = $this->reminderService->generateContent($reminder, $model);

    expect($content)->toBe('Hello World and Universe!');
});

test('getAdditionalFormSchema calls the correct reminder type method', function () {
    // Create a mock reminder type
    $mockReminderType = Mockery::mock('overload:' . app()->getNamespace() . 'ReminderTypes\TestType');
    $mockReminderType->shouldReceive('getAdditionalFormSchema')
        ->once()
        ->andReturn(['field1' => 'value1']);

    $result = $this->reminderService->getAdditionalFormSchema('TestType');

    expect($result)->toBe(['field1' => 'value1']);
});

// Helper function to mock the filesystem and return our test reminder types
function mockReminderTypeFiles($test)
{
    // Create mock SplFileInfo objects for our test reminder types
    $departureFile = Mockery::mock(SplFileInfo::class);
    $departureFile->shouldReceive('getRelativePathname')->andReturn('Departure.php');

    $snackReminderFile = Mockery::mock(SplFileInfo::class);
    $snackReminderFile->shouldReceive('getRelativePathname')->andReturn('SnackReminder.php');

    // Set up the filesystem mock to return our test files
    $test->filesystem->shouldReceive('allFiles')
        ->with(app_path('ReminderTypes'))
        ->andReturn([$departureFile, $snackReminderFile]);
}
