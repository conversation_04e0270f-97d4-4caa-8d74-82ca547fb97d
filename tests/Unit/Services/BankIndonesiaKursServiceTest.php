<?php

use App\Services\BankIndonesiaKursService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $this->service = new BankIndonesiaKursService;

    $this->sampleHtmlResponse = <<<'HTML'
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
    <html dir="ltr" lang="en-US">
        <head></head>
        <body>
            <form method="post" action="./default.aspx" onsubmit="javascript:return WebForm_OnSubmit();" id="aspnetForm">
                <div class="container">
                    <div id="content-wrapper" class="content-text">
                        <h2>KURS TRANSAKSI BANK INDONESIA</h2>
                        <div>
                            <div class="card-block" id="tableData">
                                <div class="card-body">
                                    <table class="table table-striped table-no-bordered table-lg">
                                        <thead>
                                            <tr>
                                                <th class="text-right" scope="col">Mata Uang</th>
                                                <th class="text-right" scope="col">Nilai</th>
                                                <th class="text-right" scope="col">Kurs Jual</th>
                                                <th class="text-right" scope="col">Kurs Beli</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="text-right">USD</td>
                                                <td class="text-right">1</td>
                                                <td class="text-right">16.500</td>
                                                <td class="text-right">16.400</td>
                                            </tr>
                                            <tr>
                                                <td class="text-right">EUR</td>
                                                <td class="text-right">1</td>
                                                <td class="text-right">17.200</td>
                                                <td class="text-right">17.000</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </body>
    </html>
    HTML;
});

test('service is enabled', function () {
    expect($this->service->isEnabled())->toBeTrue();
});

test('get supported currencies returns array of currencies', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response($this->sampleHtmlResponse, 200),
    ]);

    Cache::shouldReceive('remember')
        ->once()
        ->withArgs(function ($key, $ttl, $callback) {
            return $key === 'BI_supported_currency_codes' &&
                   $ttl->diffInMinutes(now()->addMonth()) < 5 &&
                   is_callable($callback);
        })
        ->andReturn(['USD', 'EUR']);

    $currencies = $this->service->getSupportedCurrencies();

    expect($currencies)
        ->toBeArray()
        ->toBe(['USD', 'EUR']);
});

test('get supported currencies returns null on failed request', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response('', 500),
    ]);

    Cache::shouldReceive('remember')
        ->once()
        ->andReturn(null);

    expect($this->service->getSupportedCurrencies())->toBeNull();
});

test('get exchange rates returns correct rates', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response($this->sampleHtmlResponse, 200),
    ]);

    Cache::shouldReceive('get')->once()->andReturn(null);
    Cache::shouldReceive('missing')->once()->andReturn(true);
    Cache::shouldReceive('put')->once();

    $rates = $this->service->getExchangeRates('IDR', ['USD', 'EUR']);

    expect($rates)
        ->toBeArray()
        ->toHaveKeys(['USD', 'EUR'])
        ->and($rates['USD'])->toBeFloat()
        ->toBeGreaterThanOrEqual(1 / 16450 - 0.00000001)
        ->toBeLessThanOrEqual(1 / 16450 + 0.00000001)
        ->and($rates['EUR'])->toBeFloat()
        ->toBeGreaterThanOrEqual(1 / 17100 - 0.00000001)
        ->toBeLessThanOrEqual(1 / 17100 + 0.00000001);
});

test('get exchange rates uses cached rates when available', function () {
    $cachedRates = ['USD' => 0.00006, 'EUR' => 0.00005];

    Cache::shouldReceive('get')->once()->andReturn($cachedRates);
    Cache::shouldReceive('missing')->once()->andReturn(false);

    $rates = $this->service->getExchangeRates('IDR', ['USD', 'EUR']);

    expect($rates)->toBe($cachedRates);
});

test('get cached exchange rates returns rates from cache', function () {
    $cachedRates = ['USD' => 0.00006];

    Cache::shouldReceive('get')->once()->andReturn($cachedRates);
    Cache::shouldReceive('missing')->once()->andReturn(false);

    $rates = $this->service->getCachedExchangeRates('IDR', ['USD']);

    expect($rates)->toBe($cachedRates);
});

test('get cached exchange rate returns single rate', function () {
    $cachedRates = ['USD' => 0.00006];

    Cache::shouldReceive('get')->once()->andReturn($cachedRates);
    Cache::shouldReceive('missing')->once()->andReturn(false);

    $rate = $this->service->getCachedExchangeRate('IDR', 'USD');

    expect($rate)->toBe(0.00006);
});

test('update currency rates cache updates cache and returns rates', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response($this->sampleHtmlResponse, 200),
    ]);

    Cache::shouldReceive('put')
        ->once()
        ->withArgs(function ($key, $value, $ttl) {
            return $key === 'BI_currency_rates_IDR' &&
                   is_array($value) &&
                   $ttl->diffInMinutes(now()->addDay()) < 5;
        });

    $rates = $this->service->updateCurrencyRatesCache('IDR');

    expect($rates)
        ->toBeArray()
        ->toHaveKey('USD');
});

test('handles malformed html gracefully', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response('<html><body>Invalid Data</body></html>', 200),
    ]);

    Cache::shouldReceive('get')->once()->andReturn(null);
    Cache::shouldReceive('missing')->once()->andReturn(true);

    expect($this->service->getExchangeRates('IDR', ['USD']))->toBeNull();
});

test('handles network errors gracefully', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response('', 500),
    ]);

    Cache::shouldReceive('get')->once()->andReturn(null);
    Cache::shouldReceive('missing')->once()->andReturn(true);

    expect($this->service->getExchangeRates('IDR', ['USD']))->toBeNull();
});

test('parses numbers correctly', function () {
    $method = new ReflectionMethod($this->service, 'parseNumber');
    $method->setAccessible(true);

    expect($method->invoke($this->service, '15.700'))->toBe(15700.0)
        ->and($method->invoke($this->service, '1.234,56'))->toBe(1234.56)
        ->and($method->invoke($this->service, 'invalid'))->toBeNull();
});

test('get exchange rates handles non-IDR base currency correctly', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response($this->sampleHtmlResponse, 200),
    ]);

    Cache::shouldReceive('get')->once()->andReturn(null);
    Cache::shouldReceive('missing')->once()->andReturn(true);
    Cache::shouldReceive('put')->once();

    $rates = $this->service->getExchangeRates('USD', ['EUR']);

    expect($rates)
        ->toBeArray()
        ->toHaveKey('EUR')
        ->and($rates['EUR'])->toBeFloat()
        ->toBeGreaterThanOrEqual(16450 / 17100 - 0.00001)
        ->toBeLessThanOrEqual(16450 / 17100 + 0.00001);
});

test('get exchange rates returns null for unsupported base currency', function () {
    Http::fake([
        'www.bi.go.id/*' => Http::response($this->sampleHtmlResponse, 200),
    ]);

    Cache::shouldReceive('get')->once()->andReturn(null);
    Cache::shouldReceive('missing')->once()->andReturn(true);

    expect($this->service->getExchangeRates('XXX', ['USD']))->toBeNull();
});

test('get exchange rates handles empty response table correctly', function () {
    $emptyTableHtml = str_replace(
        '<tbody>',
        '<tbody></tbody>',
        $this->sampleHtmlResponse
    );

    Http::fake([
        'www.bi.go.id/*' => Http::response($emptyTableHtml, 200),
    ]);

    Cache::shouldReceive('get')->once()->andReturn(null);
    Cache::shouldReceive('missing')->once()->andReturn(true);

    expect($this->service->getExchangeRates('IDR', ['USD']))->toBeNull();
});
