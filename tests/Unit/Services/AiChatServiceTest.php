<?php

use App\Services\AiChatService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Mock the configuration
    Config::set('services.openai.api_key', 'test-api-key');
    Config::set('services.openai.api_url', 'https://api.openai.com/v1/chat/completions');
    Config::set('ai-chat.allowed_tables', [
        'groups',
        'pilgrims',
        'customers',
        'group_flights',
        'flights',
        'group_pilgrim',
    ]);

    $this->service = app(AiChatService::class);
});

test('executeReadOnlyQuery handles simple single table query', function () {
    // Create test data with required fields
    DB::table('customers')->insert([
        'id' => 1,
        'uuid' => 'test-uuid-1',
        'name' => 'Test Customer',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('groups')->insert([
        'id' => 1,
        'name' => 'Test Group',
        'customer_id' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => ['id', 'name'],
        'conditions' => [
            ['field' => 'id', 'operator' => '=', 'value' => 1],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKey('id', 1)
        ->toHaveKey('name', 'Test Group');
});

test('executeReadOnlyQuery handles join queries', function () {
    // Create test data with required fields
    DB::table('customers')->insert([
        'id' => 1,
        'uuid' => 'test-uuid-1',
        'name' => 'Test Customer',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('groups')->insert([
        'id' => 1,
        'name' => 'Test Group',
        'customer_id' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => ['groups.id', 'groups.name', 'customers.name as customer_name'],
        'joins' => [
            [
                'table' => 'customers',
                'type' => 'inner',
                'on' => 'groups.customer_id = customers.id',
            ],
        ],
        'conditions' => [
            ['field' => 'groups.id', 'operator' => '=', 'value' => 1],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKey('id', 1)
        ->toHaveKey('name', 'Test Group')
        ->toHaveKey('customer_name', 'Test Customer');
});

test('executeReadOnlyQuery validates table permissions', function () {
    $queryIntent = [
        'table' => 'unauthorized_table',
        'fields' => ['*'],
        'limit' => 10,
    ];

    expect(fn () => $this->service->executeReadOnlyQuery($queryIntent))
        ->toThrow(Exception::class, "Table 'unauthorized_table' is not allowed for querying");
});

test('executeReadOnlyQuery validates join table permissions', function () {
    $queryIntent = [
        'table' => 'groups',
        'fields' => ['*'],
        'joins' => [
            [
                'table' => 'unauthorized_table',
                'type' => 'inner',
                'on' => 'groups.id = unauthorized_table.group_id',
            ],
        ],
        'limit' => 10,
    ];

    expect(fn () => $this->service->executeReadOnlyQuery($queryIntent))
        ->toThrow(Exception::class, "Join table 'unauthorized_table' is not allowed for querying");
});

test('parseJoinCondition parses valid join conditions', function () {
    $condition = 'groups.customer_id = customers.id';
    $result = $this->service->parseJoinCondition($condition);

    expect($result)
        ->toBeArray()
        ->toHaveKey('left', 'groups.customer_id')
        ->toHaveKey('operator', '=')
        ->toHaveKey('right', 'customers.id');
});

test('parseJoinCondition rejects invalid join conditions', function () {
    $invalidConditions = [
        'invalid condition',
        'groups.id',
        'groups.id = ',
        '= customers.id',
        'groups.id customers.id',
        'unauthorized_table.id = customers.id',
    ];

    foreach ($invalidConditions as $condition) {
        $result = $this->service->parseJoinCondition($condition);
        expect($result)->toBeNull();
    }
});

test('executeReadOnlyQuery validates join types', function () {
    $queryIntent = [
        'table' => 'groups',
        'fields' => ['*'],
        'joins' => [
            [
                'table' => 'customers',
                'type' => 'invalid_join_type',
                'on' => 'groups.customer_id = customers.id',
            ],
        ],
        'limit' => 10,
    ];

    expect(fn () => $this->service->executeReadOnlyQuery($queryIntent))
        ->toThrow(Exception::class, "Join type 'invalid_join_type' is not allowed");
});

test('executeReadOnlyQuery handles multiple joins', function () {
    // Create test data with required fields
    DB::table('customers')->insert([
        'id' => 1,
        'uuid' => 'test-uuid-1',
        'name' => 'Test Customer',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('groups')->insert([
        'id' => 1,
        'name' => 'Test Group',
        'customer_id' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('pilgrims')->insert([
        'id' => 1,
        'fullname' => 'Test Pilgrim',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('group_pilgrim')->insert([
        'id' => 1,
        'group_id' => 1,
        'pilgrim_id' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => ['groups.name', 'customers.name as customer_name', 'pilgrims.fullname as pilgrim_name'],
        'joins' => [
            [
                'table' => 'customers',
                'type' => 'inner',
                'on' => 'groups.customer_id = customers.id',
            ],
            [
                'table' => 'group_pilgrim',
                'type' => 'left',
                'on' => 'groups.id = group_pilgrim.group_id',
            ],
            [
                'table' => 'pilgrims',
                'type' => 'left',
                'on' => 'group_pilgrim.pilgrim_id = pilgrims.id',
            ],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKey('name', 'Test Group')
        ->toHaveKey('customer_name', 'Test Customer')
        ->toHaveKey('pilgrim_name', 'Test Pilgrim');
});

test('processQuery handles AI response with joins', function () {
    Http::fake([
        'https://api.openai.com/v1/chat/completions' => Http::sequence()
            ->push([
                'choices' => [
                    [
                        'message' => [
                            'content' => json_encode([
                                'table' => 'groups',
                                'fields' => ['groups.name', 'customers.name as customer_name'],
                                'joins' => [
                                    [
                                        'table' => 'customers',
                                        'type' => 'inner',
                                        'on' => 'groups.customer_id = customers.id',
                                    ],
                                ],
                                'limit' => 10,
                            ]),
                        ],
                    ],
                ],
            ])
            ->push([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'Here are the groups with their customers.',
                        ],
                    ],
                ],
            ]),
    ]);

    // Create test data with required fields
    DB::table('customers')->insert([
        'id' => 1,
        'uuid' => 'test-uuid-1',
        'name' => 'Test Customer',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('groups')->insert([
        'id' => 1,
        'name' => 'Test Group',
        'customer_id' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $result = $this->service->processQuery('Show me groups with their customers');

    expect($result)
        ->toBeArray()
        ->toHaveKey('success', true)
        ->toHaveKey('response', 'Here are the groups with their customers.')
        ->toHaveKey('data')
        ->and($result['data'])
        ->toBeArray()
        ->toHaveCount(1);
});

test('executeReadOnlyQuery handles raw fields', function () {
    // Create test data with required fields
    DB::table('customers')->insert([
        'id' => 1,
        'uuid' => 'test-uuid-1',
        'name' => 'Test Customer',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    DB::table('groups')->insert([
        'id' => 1,
        'name' => 'Test Group',
        'customer_id' => 1,
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => [
            'groups.name',
            ['raw' => 'groups.customer_id * 2 as doubled_id'],
            ['raw' => 'UPPER(groups.name) as upper_name'],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKey('name', 'Test Group')
        ->toHaveKey('doubled_id', 2)
        ->toHaveKey('upper_name', 'TEST GROUP');
});

test('executeReadOnlyQuery handles aggregate raw fields', function () {
    // Create test data with multiple groups
    DB::table('customers')->insert([
        ['id' => 1, 'uuid' => 'test-uuid-1', 'name' => 'Customer 1', 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'uuid' => 'test-uuid-2', 'name' => 'Customer 2', 'created_at' => now(), 'updated_at' => now()],
    ]);

    DB::table('groups')->insert([
        ['id' => 1, 'name' => 'Group 1', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'name' => 'Group 2', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 3, 'name' => 'Group 3', 'customer_id' => 2, 'created_at' => now(), 'updated_at' => now()],
    ]);

    // Test aggregate function without GROUP BY (should work for COUNT(*) alone)
    $simpleQueryIntent = [
        'table' => 'groups',
        'fields' => [
            ['raw' => 'COUNT(*) as total_groups'],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($simpleQueryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKey('total_groups', 3);
});

test('executeReadOnlyQuery validates raw field safety', function () {
    $queryIntent = [
        'table' => 'groups',
        'fields' => [
            ['raw' => 'DROP TABLE users; --'],
        ],
        'limit' => 10,
    ];

    expect(fn () => $this->service->executeReadOnlyQuery($queryIntent))
        ->toThrow(Exception::class, "Invalid raw expression: 'DROP TABLE users; --'");
});

test('isValidRawExpression validates safe expressions', function () {
    $safeExpressions = [
        'COUNT(*) as total',
        'SUM(amount) as total_amount',
        'groups.customer_id * 2 as doubled',
        'CONCAT(first_name, " ", last_name) as full_name',
        'UPPER(name) as upper_name',
        'DATE(created_at) as created_date',
    ];

    foreach ($safeExpressions as $expression) {
        $result = $this->service->isValidRawExpression($expression);
        expect($result)->toBeTrue("Expression should be valid: {$expression}");
    }
});

test('isValidRawExpression rejects dangerous expressions', function () {
    $dangerousExpressions = [
        'DROP TABLE users',
        'DELETE FROM groups',
        'INSERT INTO users VALUES (1)',
        'UPDATE groups SET name = "hacked"',
        'EXEC sp_executesql',
        'SELECT * FROM users INTO OUTFILE "/tmp/hack"',
    ];

    foreach ($dangerousExpressions as $expression) {
        $result = $this->service->isValidRawExpression($expression);
        expect($result)->toBeFalse("Expression should be invalid: {$expression}");
    }
});

test('executeReadOnlyQuery handles GROUP BY with aggregates', function () {
    // Create test data with multiple groups
    DB::table('customers')->insert([
        ['id' => 1, 'uuid' => 'test-uuid-1', 'name' => 'Customer 1', 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'uuid' => 'test-uuid-2', 'name' => 'Customer 2', 'created_at' => now(), 'updated_at' => now()],
    ]);

    DB::table('groups')->insert([
        ['id' => 1, 'name' => 'Group 1', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'name' => 'Group 2', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 3, 'name' => 'Group 3', 'customer_id' => 2, 'created_at' => now(), 'updated_at' => now()],
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => [
            'customer_id',
            ['raw' => 'COUNT(*) as group_count'],
        ],
        'group_by' => ['customer_id'],
        'order_by' => [
            ['field' => 'customer_id', 'direction' => 'asc'],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(2)
        ->and($result[0])
        ->toHaveKey('customer_id', 1)
        ->toHaveKey('group_count', 2)
        ->and($result[1])
        ->toHaveKey('customer_id', 2)
        ->toHaveKey('group_count', 1);
});

test('executeReadOnlyQuery handles HAVING clause', function () {
    // Create test data
    DB::table('customers')->insert([
        ['id' => 1, 'uuid' => 'test-uuid-1', 'name' => 'Customer 1', 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'uuid' => 'test-uuid-2', 'name' => 'Customer 2', 'created_at' => now(), 'updated_at' => now()],
    ]);

    DB::table('groups')->insert([
        ['id' => 1, 'name' => 'Group 1', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'name' => 'Group 2', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 3, 'name' => 'Group 3', 'customer_id' => 2, 'created_at' => now(), 'updated_at' => now()],
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => [
            'customer_id',
            ['raw' => 'COUNT(*) as group_count'],
        ],
        'group_by' => ['customer_id'],
        'having' => [
            ['field' => 'COUNT(*)', 'operator' => '>', 'value' => 1],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKey('customer_id', 1)
        ->toHaveKey('group_count', 2);
});

test('executeReadOnlyQuery handles ORDER BY with aggregates', function () {
    // Create test data
    DB::table('customers')->insert([
        ['id' => 1, 'uuid' => 'test-uuid-1', 'name' => 'Customer 1', 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'uuid' => 'test-uuid-2', 'name' => 'Customer 2', 'created_at' => now(), 'updated_at' => now()],
    ]);

    DB::table('groups')->insert([
        ['id' => 1, 'name' => 'Group 1', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 2, 'name' => 'Group 2', 'customer_id' => 1, 'created_at' => now(), 'updated_at' => now()],
        ['id' => 3, 'name' => 'Group 3', 'customer_id' => 2, 'created_at' => now(), 'updated_at' => now()],
    ]);

    $queryIntent = [
        'table' => 'groups',
        'fields' => [
            'customer_id',
            ['raw' => 'COUNT(*) as group_count'],
        ],
        'group_by' => ['customer_id'],
        'order_by' => [
            ['field' => 'COUNT(*)', 'direction' => 'desc'],
        ],
        'limit' => 10,
    ];

    $result = $this->service->executeReadOnlyQuery($queryIntent);

    expect($result)
        ->toBeArray()
        ->toHaveCount(2)
        ->and($result[0])
        ->toHaveKey('customer_id', 1)
        ->toHaveKey('group_count', 2)
        ->and($result[1])
        ->toHaveKey('customer_id', 2)
        ->toHaveKey('group_count', 1);
});
