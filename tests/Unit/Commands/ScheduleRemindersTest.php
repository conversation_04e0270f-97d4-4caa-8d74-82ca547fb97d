<?php

use App\Console\Commands\ScheduleReminders;
use App\Models\Group;
use App\Models\GroupFlight;
use App\Models\Itinerary;
use App\Models\Reminder;
use App\Models\ReminderSchedule;
use App\ReminderTypes\Departure;
use App\Services\ReminderService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->reminderService = Mockery::mock(ReminderService::class);
    $this->app->instance(ReminderService::class, $this->reminderService);

    // $this->command = new ScheduleReminders($this->reminderService);
});

test('scheduleReminders command schedules reminders for active reminders', function () {
    // Create an active reminder
    $reminder = Reminder::factory()->departure()->create();

    // Create a group flight that should be found by the reminder
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => Carbon::now()->addDays(5),
    ]);

    // Mock the reminderService methods
    $this->reminderService->shouldReceive('getModelClass')
        ->once()
        ->with($reminder->reminder_type)
        ->andReturn(GroupFlight::class);

    $this->reminderService->shouldReceive('findRelevantModels')
        ->once()
        ->with($reminder->reminder_type, $reminder->interval, $reminder->additional_data ?? [])
        ->andReturn(new Collection([$groupFlight]));

    $this->reminderService->shouldReceive('getEventDate')
        ->once()
        ->with($reminder->reminder_type, $groupFlight)
        ->andReturn($groupFlight->date_etd);

    // Run the command
    $this->artisan('reminders:schedule')
        ->assertSuccessful();

    // Check that a reminder schedule was created
    $this->assertDatabaseHas('reminder_schedules', [
        'reminder_id' => $reminder->id,
        'model_type' => Relation::getMorphAlias(GroupFlight::class),
        'model_id' => $groupFlight->id,
    ]);
});

test('scheduleReminders command does not schedule reminders for inactive reminders', function () {
    // Create an inactive reminder
    $reminder = Reminder::factory()->departure()->inactive()->create();

    // Run the command
    $this->artisan('reminders:schedule')
        ->assertSuccessful();

    // Check that no reminder schedules were created
    $this->assertDatabaseCount('reminder_schedules', 0);
});

test('scheduleReminders command does not create duplicate schedules', function () {
    // Create an active reminder
    $reminder = Reminder::factory()->departure()->create();

    // Create a group flight that should be found by the reminder
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => Carbon::now()->addDays(5),
    ]);

    // Create an existing schedule for this reminder and model
    $existingSchedule = ReminderSchedule::create([
        'reminder_id' => $reminder->id,
        'model_type' => Relation::getMorphAlias(GroupFlight::class),
        'model_id' => $groupFlight->id,
        'scheduled_at' => Carbon::now()->addDays(3),
    ]);

    // Mock the reminderService methods
    $this->reminderService->shouldReceive('getModelClass')
        ->once()
        ->with($reminder->reminder_type)
        ->andReturn(GroupFlight::class);

    $this->reminderService->shouldReceive('findRelevantModels')
        ->once()
        ->with($reminder->reminder_type, $reminder->interval, $reminder->additional_data ?? [])
        ->andReturn(new Collection([$groupFlight]));

    $this->reminderService->shouldReceive('getEventDate')
        ->once()
        ->with($reminder->reminder_type, $groupFlight)
        ->andReturn($groupFlight->date_etd);

    // Run the command
    $this->artisan('reminders:schedule')
        ->assertSuccessful();

    // Check that no new reminder schedules were created
    $this->assertDatabaseCount('reminder_schedules', 1);
});

test('scheduleReminders command handles multiple reminder types', function () {
    // Create a departure reminder
    $departureReminder = Reminder::factory()->departure()->create();

    // Create a snack reminder
    $snackReminder = Reminder::factory()->snackReminder()->create();

    // Create models that should be found by the reminders
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => Carbon::now()->addDays(5),
    ]);

    $itinerary = Itinerary::factory()->withSnack()->create([
        'date' => Carbon::now()->addDays(3),
    ]);

    // Mock the reminderService methods for departure reminder
    $this->reminderService->shouldReceive('getModelClass')
        ->once()
        ->with($departureReminder->reminder_type)
        ->andReturn(GroupFlight::class);

    $this->reminderService->shouldReceive('findRelevantModels')
        ->once()
        ->with($departureReminder->reminder_type, $departureReminder->interval, $departureReminder->additional_data ?? [])
        ->andReturn(new Collection([$groupFlight]));

    $this->reminderService->shouldReceive('getEventDate')
        ->once()
        ->with($departureReminder->reminder_type, $groupFlight)
        ->andReturn($groupFlight->date_etd);

    // Mock the reminderService methods for snack reminder
    $this->reminderService->shouldReceive('getModelClass')
        ->once()
        ->with($snackReminder->reminder_type)
        ->andReturn(Itinerary::class);

    $this->reminderService->shouldReceive('findRelevantModels')
        ->once()
        ->with($snackReminder->reminder_type, $snackReminder->interval, $snackReminder->additional_data ?? [])
        ->andReturn(new Collection([$itinerary]));

    $this->reminderService->shouldReceive('getEventDate')
        ->once()
        ->with($snackReminder->reminder_type, $itinerary)
        ->andReturn($itinerary->date);

    // Run the command
    $this->artisan('reminders:schedule')
        ->assertSuccessful();

    // Check that both reminder schedules were created
    $this->assertDatabaseHas('reminder_schedules', [
        'reminder_id' => $departureReminder->id,
        'model_type' => Relation::getMorphAlias(GroupFlight::class),
        'model_id' => $groupFlight->id,
    ]);

    $this->assertDatabaseHas('reminder_schedules', [
        'reminder_id' => $snackReminder->id,
        'model_type' => Relation::getMorphAlias(Itinerary::class),
        'model_id' => $itinerary->id,
    ]);
});
