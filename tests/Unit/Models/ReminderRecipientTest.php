<?php

use App\Enums\ReminderRecipientType;
use App\Models\Reminder;
use App\Models\ReminderRecipient;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

test('reminder recipient can be created with factory', function () {
    $recipient = ReminderRecipient::factory()->create();

    expect($recipient)->toBeInstanceOf(ReminderRecipient::class)
        ->and($recipient->id)->toBeGreaterThan(0)
        ->and($recipient->reminder_id)->toBeGreaterThan(0)
        ->and($recipient->recipient_type)->toBeIn(ReminderRecipientType::cases())
        ->and($recipient->recipient_id)->toBeGreaterThan(0);
});

test('reminder recipient has reminder relationship', function () {
    $reminder = Reminder::factory()->create();
    $recipient = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
    ]);

    expect($recipient->reminder)->toBeInstanceOf(Reminder::class)
        ->and($recipient->reminder->id)->toBe($reminder->id);
});

test('reminder recipient can be created for user', function () {
    $user = User::factory()->create();
    $recipient = ReminderRecipient::factory()->user()->create([
        'recipient_id' => $user->id,
    ]);

    expect($recipient->recipient_type)->toBe(ReminderRecipientType::User)
        ->and($recipient->recipient_id)->toBe($user->id);
});

test('reminder recipient can be created for role', function () {
    $role = Role::create(['name' => 'test-role']);
    $recipient = ReminderRecipient::factory()->role()->create([
        'recipient_id' => $role->id,
    ]);

    expect($recipient->recipient_type)->toBe(ReminderRecipientType::Role)
        ->and($recipient->recipient_id)->toBe($role->id);
});
