<?php

use App\Models\Finance\Invoice;
use App\Models\Finance\InvoiceItem;
use App\Models\Finance\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

uses(RefreshDatabase::class);

beforeEach(function () {
    Artisan::call('app:setup-finance-data');
});

test('invoice item can be created with factory', function () {
    $invoiceItem = InvoiceItem::factory()->create();

    expect($invoiceItem)->toBeInstanceOf(InvoiceItem::class)
        ->and($invoiceItem->id)->toBeGreaterThan(0)
        ->and($invoiceItem->name)->not->toBeEmpty()
        ->and($invoiceItem->unit_price)->toBeGreaterThan(0);
});

test('invoice item can be created for specific invoice', function () {
    $invoice = Invoice::factory()->create();
    $invoiceItem = InvoiceItem::factory()
        ->forInvoice($invoice)
        ->create();

    expect($invoiceItem->invoice_id)->toBe($invoice->id)
        ->and($invoiceItem->invoice)->toBeInstanceOf(Invoice::class)
        ->and($invoiceItem->invoice->id)->toBe($invoice->id);
});

test('invoice item can be created with product', function () {
    $product = Product::factory()->create();
    $invoiceItem = InvoiceItem::factory()
        ->forProduct($product)
        ->create();

    expect($invoiceItem->product_id)->toBe($product->id)
        ->and($invoiceItem->name)->toBe($product->name)
        ->and($invoiceItem->description)->toBe($product->description)
        ->and($invoiceItem->unit_price)->toBe($product->unit_price)
        ->and($invoiceItem->product)->toBeInstanceOf(Product::class);
});

test('invoice item can be created with new product', function () {
    $invoiceItem = InvoiceItem::factory()
        ->withProduct()
        ->create();

    expect($invoiceItem->product_id)->not->toBeNull()
        ->and($invoiceItem->product)->toBeInstanceOf(Product::class)
        ->and($invoiceItem->name)->toBe($invoiceItem->product->name)
        ->and($invoiceItem->unit_price)->toBe($invoiceItem->product->unit_price);
});

test('invoice item can be created with specific quantity', function () {
    $quantity = 5;
    $invoiceItem = InvoiceItem::factory()
        ->quantity($quantity)
        ->create();

    expect($invoiceItem->quantity)->toBe($quantity);
});

test('invoice item belongs to invoice', function () {
    $invoice = Invoice::factory()->create();
    $invoiceItem = InvoiceItem::factory()
        ->forInvoice($invoice)
        ->create();

    expect($invoiceItem->invoice)->toBeInstanceOf(Invoice::class)
        ->and($invoiceItem->invoice->id)->toBe($invoice->id);
});

test('invoice item belongs to product', function () {
    $product = Product::factory()->create();
    $invoiceItem = InvoiceItem::factory()
        ->forProduct($product)
        ->create();

    expect($invoiceItem->product)->toBeInstanceOf(Product::class)
        ->and($invoiceItem->product->id)->toBe($product->id);
});

test('invoice item is sortable within an invoice', function () {
    $invoice = Invoice::factory()->create();

    // Create multiple items for the same invoice
    $item1 = InvoiceItem::factory()->forInvoice($invoice)->create();
    $item2 = InvoiceItem::factory()->forInvoice($invoice)->create();
    $item3 = InvoiceItem::factory()->forInvoice($invoice)->create();

    // Refresh from database to get the order_column values
    $item1->refresh();
    $item2->refresh();
    $item3->refresh();

    // Check that order_column values are sequential
    expect($item1->order_column)->toBeLessThan($item2->order_column)
        ->and($item2->order_column)->toBeLessThan($item3->order_column);
});
