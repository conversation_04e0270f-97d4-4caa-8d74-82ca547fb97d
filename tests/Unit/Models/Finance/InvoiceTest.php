<?php

use App\Enums\InvoiceStatus;
use App\Models\Customer;
use App\Models\Finance\CashAccount;
use App\Models\Finance\Invoice;
use App\Models\Finance\InvoiceItem;
use App\Models\Finance\InvoicePayment;
use App\Models\Finance\InvoiceRefund;
use App\Models\Finance\JournalEntry;
use App\Models\Group;
use App\Models\Period;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

uses(RefreshDatabase::class);

beforeEach(function () {
    Artisan::call('app:setup-finance-data');
});

test('invoice can be created with factory', function () {
    $invoice = Invoice::factory()->create();

    expect($invoice)->toBeInstanceOf(Invoice::class)
        ->and($invoice->id)->toBeGreaterThan(0)
        ->and($invoice->invoice_number)->not->toBeEmpty()
        ->and($invoice->status)->toBeInstanceOf(InvoiceStatus::class);
});

test('invoice number is generated automatically when not provided', function () {
    Period::factory()->active()->create();
    $invoice = Invoice::factory()->create(['invoice_number' => null]);

    expect($invoice->invoice_number)->toMatch('/^INV-\d{4}\d{4}$/');
});

test('invoice belongs to a customer', function () {
    $customer = Customer::factory()->create();
    $invoice = Invoice::factory()->create(['customer_id' => $customer->id]);

    expect($invoice->customer)->toBeInstanceOf(Customer::class)
        ->and($invoice->customer->id)->toBe($customer->id);
});

test('invoice belongs to a group', function () {
    $group = Group::factory()->create();
    $invoice = Invoice::factory()->create(['group_id' => $group->id]);

    expect($invoice->group)->toBeInstanceOf(Group::class)
        ->and($invoice->group->id)->toBe($group->id);
});

test('invoice has many items', function () {
    $invoice = Invoice::factory()->withItems()->create();

    expect($invoice->items)->toHaveCount(3)
        ->and($invoice->items->first())->toBeInstanceOf(InvoiceItem::class);
});

test('invoice has many payments', function () {
    $invoice = Invoice::factory()->withPayments(2)->create();

    expect($invoice->payments)->toHaveCount(2)
        ->and($invoice->payments->first())->toBeInstanceOf(InvoicePayment::class);
});

test('invoice has many refunds', function () {
    $invoice = Invoice::factory()->withRefunds(2)->create();

    expect($invoice->refunds)->toHaveCount(2)
        ->and($invoice->refunds->first())->toBeInstanceOf(InvoiceRefund::class);
});

test('invoice has a journal entry', function () {
    $invoice = Invoice::factory()->create();

    // The journal entry should be created automatically by the model's saved event
    expect($invoice->journal_entry)->toBeInstanceOf(JournalEntry::class);
});

test('getTotalInvoice returns sum of item subtotals', function () {
    $invoice = Invoice::factory()->create();

    // Create invoice items with known values
    $item1 = InvoiceItem::factory()->forInvoice($invoice)->create([
        'quantity' => 2,
        'unit_price' => 100,
    ]);

    $item2 = InvoiceItem::factory()->forInvoice($invoice)->create([
        'quantity' => 3,
        'unit_price' => 50,
    ]);

    // Expected total: (2 * 100) + (3 * 50) = 200 + 150 = 350
    expect($invoice->getTotalInvoice())->toBe(350.0);
});

test('getTotalPayment returns sum of payment amounts', function () {
    $invoice = Invoice::factory()->create();

    // Create payments with known values
    InvoicePayment::factory()->forInvoice($invoice)->create([
        'amount' => 300,
    ]);

    InvoicePayment::factory()->forInvoice($invoice)->create([
        'amount' => 200,
    ]);

    // Expected total: 300 + 200 = 500
    expect($invoice->getTotalPayment())->toBe(500.0);
});

test('getStatus returns Cancelled when status is Cancelled', function () {
    $invoice = Invoice::factory()->create(['status' => InvoiceStatus::Cancelled]);

    expect($invoice->getStatus())->toBe(InvoiceStatus::Cancelled);
});

test('getStatus returns Paid when total equals paid amount', function () {
    $invoice = Invoice::factory()->withItems()->create();

    InvoicePayment::factory()->forInvoice($invoice)->amount($invoice->getTotalInvoice())->create();

    $invoice->save();

    expect($invoice->getStatus())->toBe(InvoiceStatus::Paid);
});

test('getStatus returns PaidPartial when paid is greater than zero but less than total', function () {
    $invoice = Invoice::factory()->withItems()->create([
        'due_date' => now()->addDays(5),
    ]);

    InvoicePayment::factory()->forInvoice($invoice)->amount($invoice->getTotalInvoice() / 2)->create();

    $invoice->save();

    expect($invoice->getStatus())->toBe(InvoiceStatus::PaidPartial);
});

test('getStatus returns Unpaid when paid is zero', function () {
    $invoice = Invoice::factory()->create([
        'due_date' => now()->addDays(5),
    ]);

    expect($invoice->getStatus())->toBe(InvoiceStatus::Unpaid);
});

test('getStatus returns Overdue when due date is past and not fully paid', function () {
    $invoice = Invoice::factory()->create([
        'total' => 1000,
        'paid' => 500,
        'due_date' => now()->subDays(1),
    ]);

    expect($invoice->getStatus())->toBe(InvoiceStatus::Overdue);
});

test('syncJournalEntry deletes journal entry when invoice is cancelled', function () {
    // Create an invoice with a journal entry
    $invoice = Invoice::factory()->create();
    $journalEntryId = $invoice->journal_entry->id;

    // Update the invoice to cancelled status
    $invoice->update(['status' => InvoiceStatus::Cancelled]);

    // The journal entry should be deleted
    expect(JournalEntry::find($journalEntryId))->toBeNull();
});

test('syncJournalEntry creates or updates journal entry for active invoice', function () {
    // Create an invoice with items
    $invoice = Invoice::factory()
        ->create([
            'invoice_number' => 'INV-20230001',
            'invoice_date' => now(),
            'exchange_rate' => 1.5,
        ]);
    InvoiceItem::factory()->forInvoice($invoice)->create(['quantity' => 1, 'unit_price' => 1000]);

    // Force sync the journal entry
    $invoice->save();

    // The journal entry should be created automatically
    $journalEntry = $invoice->journal_entry;
    expect($journalEntry)->toBeInstanceOf(JournalEntry::class);

    // The journal entry should have two items (debit and credit)
    expect($journalEntry->items)->toHaveCount(2);

    // Get the receivable account ID from config
    $receivableAccountId = CashAccount::query()
        ->where('code', config('finance.coa.receivable'))
        ->value('id');

    // Get the sales account ID from config
    $salesAccountId = CashAccount::query()
        ->where('code', config('finance.coa.sales'))
        ->value('id');

    // Check the debit item
    $debitItem = $journalEntry->items->firstWhere('type', 'd');
    expect($debitItem)->not->toBeNull()
        ->and($debitItem->account_id)->toBe($receivableAccountId)
        ->and($debitItem->amount)->toBe(1000 * 1.5);

    // Check the credit item
    $creditItem = $journalEntry->items->firstWhere('type', 'c');
    expect($creditItem)->not->toBeNull()
        ->and($creditItem->account_id)->toBe($salesAccountId)
        ->and($creditItem->amount)->toBe(1000 * 1.5);
});

test('delete method deletes journal entry before deleting invoice', function () {
    // Create an invoice
    $invoice = Invoice::factory()->create();

    // Get the journal entry ID
    $journalEntryId = $invoice->journal_entry->id;

    // Delete the invoice
    $invoice->delete();

    // The journal entry should be deleted
    expect(JournalEntry::find($journalEntryId))->toBeNull();
});

test('getNextInvoiceNumber generates correct invoice number format', function () {
    Period::factory()->active()->create(['name' => '1446 H']);
    Invoice::factory()->create(['invoice_number' => 'INV-********']);

    $nextNumber = Invoice::getNextInvoiceNumber();

    expect($nextNumber)->toBe('INV-********');
});

test('getNextInvoiceNumber handles first invoice in period', function () {
    Period::factory()->active()->create(['name' => '1446 H']);

    $nextNumber = Invoice::getNextInvoiceNumber();

    expect($nextNumber)->toBe('INV-14460001');
});
