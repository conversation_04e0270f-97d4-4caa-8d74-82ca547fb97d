<?php

use App\Models\Group;
use App\Models\GroupHotel;
use App\Models\Hotel;
use App\Models\Period;
use App\Models\Room;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->admin()->create();
    $this->actingAs($this->user);
});

test('group can be created with minimal attributes', function () {
    $group = Group::factory()->create();

    expect($group)->toBeInstanceOf(Group::class)
        ->and($group->id)->toBeGreaterThan(0);
});

test('group name is set to arrival date when not provided', function () {
    $arrivalDate = now()->addDays(5);
    $group = Group::factory()->create([
        'name' => null,
        'arrival_date' => $arrivalDate,
    ]);

    expect($group->name)->toBe($arrivalDate->format('j F Y'));
});

test('group name is set to current date when arrival date is not provided', function () {
    $group = Group::factory()->create([
        'name' => null,
        'arrival_date' => null,
    ]);

    expect($group->name)->toBe(now()->format('j F Y'));
});

test('mutawifs attribute returns collection of mutawifs', function () {
    $mutawif1 = User::factory()->mutawif()->create();
    $mutawif2 = User::factory()->mutawif()->create();
    $mutawif3 = User::factory()->mutawif()->create();

    $group = Group::factory()->create([
        'mutawif_id' => $mutawif1->id,
        'mutawif_2_id' => $mutawif2->id,
        'mutawif_3_id' => $mutawif3->id,
    ]);

    expect($group->mutawifs)->toBeCollection()
        ->toHaveCount(3)
        ->and($group->mutawifs->pluck('id')->toArray())
        ->toContain($mutawif1->id, $mutawif2->id, $mutawif3->id);
});

test('isInCurrentPeriod returns true when arrival date is within period', function () {
    $period = Period::factory()->create([
        'date_start' => now()->subDays(10),
        'date_end' => now()->addDays(10),
    ]);

    $group = Group::factory()->create([
        'arrival_date' => now(),
    ]);

    expect($group->isInCurrentPeriod())->toBeTrue();
});

test('isInCurrentPeriod returns false when arrival date is outside period', function () {
    $period = Period::factory()->create([
        'date_start' => now()->addDays(10),
        'date_end' => now()->addDays(20),
    ]);

    $group = Group::factory()->create([
        'arrival_date' => now(),
    ]);

    expect($group->isInCurrentPeriod())->toBeFalse();
});

test('isInCurrentPeriod returns true when arrival date is null', function () {
    $period = Period::factory()->create([
        'date_start' => now()->subDays(10),
        'date_end' => now()->addDays(10),
    ]);

    $group = Group::factory()->create([
        'arrival_date' => null,
    ]);

    expect($group->isInCurrentPeriod())->toBeTrue();
});

test('getPeriod returns correct period for arrival date', function () {
    $arrivalDate = now();
    $period = Period::factory()->create([
        'date_start' => $arrivalDate->copy()->subDays(5),
        'date_end' => $arrivalDate->copy()->addDays(5),
    ]);

    $group = Group::factory()->create([
        'arrival_date' => $arrivalDate,
    ]);

    expect($group->getPeriod()->id)->toBe($period->id);
});

test('duplicate creates a copy of the group with related data', function () {
    $group = Group::factory()->create();

    // Create related data
    $hotel = Hotel::factory()->create();
    $groupHotel = GroupHotel::factory()->create([
        'group_id' => $group->id,
        'hotel_id' => $hotel->id,
        'is_confirmed' => true,
    ]);

    $clone = $group->duplicate();

    expect($clone)->toBeInstanceOf(Group::class)
        ->and($clone->id)->not->toBe($group->id)
        ->and($clone->created_by_id)->toBe($this->user->id)
        ->and($clone->group_hotels)->toHaveCount(1)
        ->and($clone->group_hotels->first()->is_confirmed)->toBeFalse()
        ->and($clone->group_hotels->first()->broker_id)->toBeNull()
        ->and($clone->group_hotels->first()->confirmation_file)->toBeNull();
});

test('generateRooms creates rooms based on hotel room counts', function () {
    $group = Group::factory()->create();
    $hotel = Hotel::factory()->inMakkah()->create();

    // Create a group hotel with room counts
    GroupHotel::factory()->create([
        'group_id' => $group->id,
        'hotel_id' => $hotel->id,
        'room_single_count' => 2,
        'room_double_count' => 3,
        'room_triple_count' => 1,
        'room_quad_count' => 0,
        'room_quint_count' => 0,
    ]);

    $group->generateRooms();

    expect($group->rooms)->toHaveCount(6)
        ->and($group->rooms->where('capacity', 1))->toHaveCount(2)
        ->and($group->rooms->where('capacity', 2))->toHaveCount(3)
        ->and($group->rooms->where('capacity', 3))->toHaveCount(1);
});

test('logEvent creates activity log entry', function () {
    $group = Group::factory()->create();

    sleep(1);
    $group->logEvent('test_event', 'Test description');

    $activity = $group->activities()->latest()->first();

    expect($activity)->not->toBeNull()
        ->and($activity->event)->toBe('test_event')
        ->and($activity->description)->toBe('Test description')
        ->and($activity->causer_id)->toBe($this->user->id);
});

test('logEvent uses event name as description when description is null', function () {
    $group = Group::factory()->create();

    sleep(1);
    $group->logEvent('test_event');

    $activity = $group->activities()->latest()->first();

    expect($activity)->not->toBeNull()
        ->and($activity->event)->toBe('test_event')
        ->and($activity->description)->toBe('test_event');
});

test('scope current period filters groups within current period', function () {
    $period = Period::factory()->create([
        'date_start' => now()->subDays(10),
        'date_end' => now()->addDays(10),
    ]);

    $groupInPeriod = Group::factory()->create([
        'arrival_date' => now(),
    ]);

    $groupOutsidePeriod = Group::factory()->create([
        'arrival_date' => now()->addDays(20),
    ]);

    $groupNullDate = Group::factory()->create([
        'arrival_date' => null,
    ]);

    $groups = Group::currentPeriod()->get();

    expect($groups)->toHaveCount(2)
        ->and($groups->pluck('id'))->toContain($groupInPeriod->id, $groupNullDate->id)
        ->and($groups->pluck('id'))->not->toContain($groupOutsidePeriod->id);
});

test('scope current period with strict option excludes null arrival dates', function () {
    $period = Period::factory()->create([
        'date_start' => now()->subDays(10),
        'date_end' => now()->addDays(10),
    ]);

    $groupInPeriod = Group::factory()->create([
        'arrival_date' => now(),
    ]);

    $groupNullDate = Group::factory()->create([
        'arrival_date' => null,
    ]);

    $groups = Group::currentPeriod(true)->get();

    expect($groups)->toHaveCount(1)
        ->and($groups->pluck('id'))->toContain($groupInPeriod->id)
        ->and($groups->pluck('id'))->not->toContain($groupNullDate->id);
});
