<?php

use App\Models\Airline;
use App\Models\Group;
use App\Models\GroupFlight;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('group flight can be created with factory', function () {
    $groupFlight = GroupFlight::factory()->create();

    expect($groupFlight)->toBeInstanceOf(GroupFlight::class)
        ->and($groupFlight->id)->toBeGreaterThan(0)
        ->and($groupFlight->group_id)->toBeGreaterThan(0)
        ->and($groupFlight->airline_id)->toBeGreaterThan(0);
});

test('group flight belongs to a group', function () {
    $group = Group::factory()->create();
    $groupFlight = GroupFlight::factory()->create([
        'group_id' => $group->id,
    ]);

    expect($groupFlight->group)->toBeInstanceOf(Group::class)
        ->and($groupFlight->group->id)->toBe($group->id);
});

test('group flight belongs to an airline', function () {
    $airline = Airline::factory()->create();
    $groupFlight = GroupFlight::factory()->create([
        'airline_id' => $airline->id,
    ]);

    expect($groupFlight->airline)->toBeInstanceOf(Airline::class)
        ->and($groupFlight->airline->id)->toBe($airline->id);
});

test('total_pax returns pax when set', function () {
    $groupFlight = GroupFlight::factory()->create([
        'pax' => 42,
    ]);

    expect($groupFlight->total_pax)->toBe(42);
});

test('total_pax returns group total_pax when pax is null', function () {
    $group = Group::factory()->create(['total_pax' => 25]);
    $groupFlight = GroupFlight::factory()->create([
        'group_id' => $group->id,
        'pax' => null,
    ]);

    expect($groupFlight->total_pax)->toBe(25);
});

test('etd returns formatted time', function () {
    $date = now()->setHour(14)->setMinute(30);
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $date,
    ]);

    expect($groupFlight->etd)->toBe('14:30');
});

test('eta returns formatted time without day difference', function () {
    $etd = now()->setHour(10)->setMinute(0);
    $eta = now()->setHour(14)->setMinute(30);
    
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $etd,
        'date_eta' => $eta,
    ]);

    expect($groupFlight->eta)->toBe('14:30');
});

test('eta returns formatted time with day difference', function () {
    $etd = now()->setHour(10)->setMinute(0);
    $eta = now()->addDays(1)->setHour(14)->setMinute(30);
    
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $etd,
        'date_eta' => $eta,
    ]);

    expect($groupFlight->eta)->toBe('14:30+1');
});

test('etd_via returns dash when via_etd is null', function () {
    $groupFlight = GroupFlight::factory()->create([
        'via_etd' => null,
    ]);

    expect($groupFlight->etd_via)->toBe('-');
});

test('etd_via returns formatted time without day difference', function () {
    $etd = now()->setHour(10)->setMinute(0);
    $viaEtd = now()->setHour(14)->setMinute(30);
    
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $etd,
        'via_etd' => $viaEtd,
    ]);

    expect($groupFlight->etd_via)->toBe('14:30');
});

test('etd_via returns formatted time with day difference', function () {
    $etd = now()->setHour(10)->setMinute(0);
    $viaEtd = now()->addDays(1)->setHour(14)->setMinute(30);
    
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $etd,
        'via_etd' => $viaEtd,
    ]);

    expect($groupFlight->etd_via)->toBe('14:30+1');
});

test('eta_via returns dash when via_eta is null', function () {
    $groupFlight = GroupFlight::factory()->create([
        'via_eta' => null,
    ]);

    expect($groupFlight->eta_via)->toBe('-');
});

test('eta_via returns formatted time without day difference', function () {
    $etd = now()->setHour(10)->setMinute(0);
    $viaEta = now()->setHour(14)->setMinute(30);
    
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $etd,
        'via_eta' => $viaEta,
    ]);

    expect($groupFlight->eta_via)->toBe('14:30');
});

test('eta_via returns formatted time with day difference', function () {
    $etd = now()->setHour(10)->setMinute(0);
    $viaEta = now()->addDays(1)->setHour(14)->setMinute(30);
    
    $groupFlight = GroupFlight::factory()->create([
        'date_etd' => $etd,
        'via_eta' => $viaEta,
    ]);

    expect($groupFlight->eta_via)->toBe('14:30+1');
});

test('mealbox returns value from meta', function () {
    $groupFlight = GroupFlight::factory()->create([
        'meta' => ['mealbox' => 'Vegetarian'],
    ]);

    expect($groupFlight->mealbox)->toBe('Vegetarian');
});

test('mealbox returns null when not set in meta', function () {
    $groupFlight = GroupFlight::factory()->create([
        'meta' => ['other_key' => 'value'],
    ]);

    expect($groupFlight->mealbox)->toBeNull();
});