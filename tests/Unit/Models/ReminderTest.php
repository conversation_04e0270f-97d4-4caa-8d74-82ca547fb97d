<?php

use App\Enums\NotificationType;
use App\Models\Reminder;
use App\Models\ReminderRecipient;
use App\Models\ReminderSchedule;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('reminder can be created with factory', function () {
    $reminder = Reminder::factory()->create();

    expect($reminder)->toBeInstanceOf(Reminder::class)
        ->and($reminder->id)->toBeGreaterThan(0)
        ->and($reminder->reminder_type)->not->toBeEmpty()
        ->and($reminder->template)->not->toBeEmpty()
        ->and($reminder->interval)->toBeGreaterThan(0)
        ->and($reminder->active)->toBeTrue();
});

test('reminder has recipients relationship', function () {
    $reminder = Reminder::factory()->create();
    $recipient = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
    ]);

    expect($reminder->recipients)->toHaveCount(1)
        ->and($reminder->recipients->first()->id)->toBe($recipient->id);
});

test('reminder has schedules relationship', function () {
    $reminder = Reminder::factory()->create();
    $schedule = ReminderSchedule::factory()->forReminder($reminder)->create();

    expect($reminder->schedules)->toHaveCount(1)
        ->and($reminder->schedules->first()->id)->toBe($schedule->id);
});

test('isActive scope returns only active reminders', function () {
    $activeReminder = Reminder::factory()->create(['active' => true]);
    $inactiveReminder = Reminder::factory()->create(['active' => false]);

    $activeReminders = Reminder::isActive()->get();

    expect($activeReminders)->toHaveCount(1)
        ->and($activeReminders->first()->id)->toBe($activeReminder->id);
});

test('notification_type is cast to enum', function () {
    $reminder = Reminder::factory()->create([
        'notification_type' => NotificationType::Email,
    ]);

    expect($reminder->notification_type)->toBeInstanceOf(NotificationType::class)
        ->and($reminder->notification_type)->toBe(NotificationType::Email);
});

test('time is cast to datetime', function () {
    $time = now();
    $reminder = Reminder::factory()->create([
        'time' => $time,
    ]);

    expect($reminder->time)->toBeInstanceOf(Carbon\Carbon::class)
        ->and($reminder->time->timestamp)->toBe($time->timestamp);
});

test('active is cast to boolean', function () {
    $reminder = Reminder::factory()->create([
        'active' => 1,
    ]);

    expect($reminder->active)->toBeTrue();

    $reminder->active = 0;
    $reminder->save();

    expect($reminder->fresh()->active)->toBeFalse();
});

test('additional_data is cast to array', function () {
    $additionalData = ['city' => 'Makkah'];
    $reminder = Reminder::factory()->create([
        'additional_data' => $additionalData,
    ]);

    expect($reminder->additional_data)->toBeArray()
        ->and($reminder->additional_data)->toBe($additionalData);
});
