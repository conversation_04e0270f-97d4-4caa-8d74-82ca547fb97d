<?php

use App\Models\GroupFlight;
use App\Models\Reminder;
use App\Models\ReminderSchedule;
use App\Services\ReminderService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('reminder schedule can be created with factory', function () {
    $schedule = ReminderSchedule::factory()->forDeparture()->create();

    expect($schedule)->toBeInstanceOf(ReminderSchedule::class)
        ->and($schedule->id)->toBeGreaterThan(0)
        ->and($schedule->reminder_id)->toBeGreaterThan(0)
        ->and($schedule->model_type)->not->toBeEmpty()
        ->and($schedule->model_id)->toBeGreaterThan(0)
        ->and($schedule->scheduled_at)->toBeInstanceOf(Carbon\Carbon::class)
        ->and($schedule->sent_at)->toBeNull();
});

test('reminder schedule has reminder relationship', function () {
    $reminder = Reminder::factory()->create();
    $schedule = ReminderSchedule::factory()->forReminder($reminder)->create();

    expect($schedule->reminder)->toBeInstanceOf(Reminder::class)
        ->and($schedule->reminder->id)->toBe($reminder->id);
});

test('reminder schedule has model relationship', function () {
    $groupFlight = GroupFlight::factory()->create();
    $reminder = Reminder::factory()->departure()->create();
    $schedule = ReminderSchedule::factory()->forReminder($reminder, $groupFlight)->create();

    expect($schedule->model)->toBeInstanceOf(GroupFlight::class)
        ->and($schedule->model->id)->toBe($groupFlight->id);
});

test('getMessageContent returns generated content from reminder service', function () {
    $schedule = ReminderSchedule::factory()->forDeparture()->create();

    // Mock the ReminderService
    $reminderService = Mockery::mock(ReminderService::class);
    $this->app->instance(ReminderService::class, $reminderService);

    $reminderService->shouldReceive('generateContent')
        ->once()
        ->with($schedule->reminder, $schedule->model)
        ->andReturn('Test message content');

    expect($schedule->getMessageContent())->toBe('Test message content');
});

test('getTagsValues returns tags values from reminder service', function () {
    $schedule = ReminderSchedule::factory()->forDeparture()->create();

    // Mock the ReminderService
    $reminderService = Mockery::mock(ReminderService::class);
    $this->app->instance(ReminderService::class, $reminderService);

    $reminderService->shouldReceive('getTagsValues')
        ->once()
        ->with($schedule->reminder->reminder_type, $schedule->model)
        ->andReturn(['tag1' => 'value1']);

    expect($schedule->getTagsValues())->toBe(['tag1' => 'value1']);
});

test('scheduled_at is cast to datetime', function () {
    $scheduledAt = now();
    $schedule = ReminderSchedule::factory()->forDeparture()->create([
        'scheduled_at' => $scheduledAt,
    ]);

    expect($schedule->scheduled_at)->toBeInstanceOf(Carbon\Carbon::class)
        ->and($schedule->scheduled_at->timestamp)->toBe($scheduledAt->timestamp);
});

test('sent_at is cast to datetime', function () {
    $sentAt = now();
    $schedule = ReminderSchedule::factory()->forDeparture()->create([
        'sent_at' => $sentAt,
    ]);

    expect($schedule->sent_at)->toBeInstanceOf(Carbon\Carbon::class)
        ->and($schedule->sent_at->timestamp)->toBe($sentAt->timestamp);
});
