<?php

use App\Enums\HotelRoomType;
use App\Models\Group;
use App\Models\GroupHotel;
use App\Models\Hotel;
use App\Models\Vendors\HotelBroker;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('group hotel can be created with factory', function () {
    $groupHotel = GroupHotel::factory()->create();

    expect($groupHotel)->toBeInstanceOf(GroupHotel::class)
        ->and($groupHotel->id)->toBeGreaterThan(0)
        ->and($groupHotel->group_id)->toBeGreaterThan(0)
        ->and($groupHotel->hotel_id)->toBeGreaterThan(0)
        ->and($groupHotel->check_in)->toBeInstanceOf(\DateTime::class)
        ->and($groupHotel->check_out)->toBeInstanceOf(\DateTime::class)
        ->and($groupHotel->meta)->toBeArray()
        ->and($groupHotel->meta)->toHaveKey('room_type')
        ->and($groupHotel->meta)->toHaveKey('meal');
});

test('group hotel factory can set custom dates', function () {
    $checkIn = now()->addDays(5);
    $checkOut = now()->addDays(10);

    $groupHotel = GroupHotel::factory()
        ->dates($checkIn, $checkOut)
        ->create();

    expect($groupHotel->check_in->format('Y-m-d'))->toBe($checkIn->format('Y-m-d'))
        ->and($groupHotel->check_out->format('Y-m-d'))->toBe($checkOut->format('Y-m-d'));
});

test('group hotel factory can set custom room counts', function () {
    $groupHotel = GroupHotel::factory()
        ->rooms(2, 3, 4, 5, 1)
        ->create();

    expect($groupHotel->room_single_count)->toBe(2)
        ->and($groupHotel->room_double_count)->toBe(3)
        ->and($groupHotel->room_triple_count)->toBe(4)
        ->and($groupHotel->room_quad_count)->toBe(5)
        ->and($groupHotel->room_quint_count)->toBe(1);
});

test('group hotel factory can set confirmation status', function () {
    $groupHotel = GroupHotel::factory()
        ->confirmed(true, 'ABC123')
        ->create();

    expect($groupHotel->is_confirmed)->toBeTrue()
        ->and($groupHotel->confirmation_number)->toBe('ABC123');
});

test('group hotel factory can set room type', function () {
    $groupHotel = GroupHotel::factory()
        ->roomType(HotelRoomType::KabahView)
        ->create();

    expect($groupHotel->meta['room_type'])->toBe(HotelRoomType::KabahView->value);
});

test('group hotel factory can set meal plan', function () {
    $groupHotel = GroupHotel::factory()
        ->meal('Full Board')
        ->create();

    expect($groupHotel->meta['meal'])->toBe('Full Board');
});

test('group hotel factory can associate with a broker', function () {
    $groupHotel = GroupHotel::factory()
        ->withBroker()
        ->create();

    expect($groupHotel->broker_id)->not->toBeNull()
        ->and($groupHotel->broker)->toBeInstanceOf(HotelBroker::class);
});

test('group hotel belongs to a group', function () {
    $group = Group::factory()->create();
    $groupHotel = GroupHotel::factory()
        ->create([
            'group_id' => $group->id,
        ]);

    expect($groupHotel->group)->toBeInstanceOf(Group::class)
        ->and($groupHotel->group->id)->toBe($group->id);
});

test('group hotel belongs to a hotel', function () {
    $hotel = Hotel::factory()->create();
    $groupHotel = GroupHotel::factory()
        ->create([
            'hotel_id' => $hotel->id,
        ]);

    expect($groupHotel->hotel)->toBeInstanceOf(Hotel::class)
        ->and($groupHotel->hotel->id)->toBe($hotel->id);
});

test('hotel name with type returns correct format with room type', function () {
    $hotel = Hotel::factory()->create(['name' => 'Test Hotel']);
    $groupHotel = GroupHotel::factory()
        ->create([
            'hotel_id' => $hotel->id,
            'meta' => [
                'room_type' => HotelRoomType::KabahView->value,
            ],
        ]);

    expect($groupHotel->hotelNameWithType)->toContain('Test Hotel')
        ->and($groupHotel->hotelNameWithType)->toContain('KV');
});

test('hotel name with type returns hotel name when no room type', function () {
    $hotel = Hotel::factory()->create(['name' => 'Test Hotel']);
    $hotel->refresh();
    $groupHotel = GroupHotel::factory()
        ->create([
            'hotel_id' => $hotel->id,
            'meta' => [
                'meal' => 'Full Board',
            ],
        ]);

    expect($groupHotel->hotelNameWithType)->toBe($hotel->fullname);
});
