<?php

use App\Models\Group;
use App\Models\Hotel;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('hotel can be created with factory', function () {
    $hotel = Hotel::factory()->create();

    expect($hotel)->toBeInstanceOf(Hotel::class)
        ->and($hotel->id)->toBeGreaterThan(0)
        ->and($hotel->name)->not->toBeEmpty()
        ->and($hotel->city)->toBeIn(array_keys(Hotel::CITIES))
        ->and($hotel->stars)->toBeBetween(3, 5)
        ->and($hotel->distance)->toBeGreaterThan(0)
        ->and($hotel->price_quad)->toBeGreaterThan(0)
        ->and($hotel->price_triple)->toBeGreaterThan(0)
        ->and($hotel->price_double)->toBeGreaterThan(0);
});

test('hotel factory can set custom star rating', function () {
    $hotel = Hotel::factory()->stars(4)->create();

    expect($hotel->stars)->toBe(4);
});

test('hotel factory can set custom distance', function () {
    $hotel = Hotel::factory()->distance(1000)->create();

    expect($hotel->distance)->toBe(1000);
});

test('hotel factory can set location to Makkah', function () {
    $hotel = Hotel::factory()->inMakkah()->create();

    expect($hotel->city)->toBe('Makkah');
});

test('hotel factory can set location to Madinah', function () {
    $hotel = Hotel::factory()->inMadinah()->create();

    expect($hotel->city)->toBe('Madinah');
});

test('hotel factory can set custom prices', function () {
    $hotel = Hotel::factory()->prices(200, 300, 400)->create();

    expect($hotel->price_double)->toBe(200)
        ->and($hotel->price_triple)->toBe(300)
        ->and($hotel->price_quad)->toBe(400);
});

test('hotel can be associated with groups', function () {
    $hotel = Hotel::factory()->create();
    $group = Group::factory()->create();

    $hotel->groups()->attach($group, [
        'check_in' => now(),
        'check_out' => now()->addDays(5),
        'is_confirmed' => true,
    ]);

    expect($hotel->groups)->toHaveCount(1)
        ->and($hotel->groups->first()->id)->toBe($group->id);
});

test('hotel cannot be deleted when associated with groups', function () {
    $hotel = Hotel::factory()->create();
    $group = Group::factory()->create();

    $hotel->groups()->attach($group);

    $result = $hotel->delete();

    expect($result)->toBeNull()
        ->and(Hotel::find($hotel->id))->not->toBeNull();
});

test('hotel can be deleted when not associated with groups', function () {
    $hotel = Hotel::factory()->create();

    $result = $hotel->delete();

    expect($result)->not->toBeNull()
        ->and(Hotel::find($hotel->id))->toBeNull();
});
