<?php

namespace Tests\Feature;

use App\Livewire\AiChatAssistant;
use App\Services\AiChatService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Livewire\Livewire;
use Tests\TestCase;

class AiChatAssistantLivewireTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_render_the_component()
    {
        Livewire::test(AiChatAssistant::class)
            ->assertStatus(200)
            ->assertSee('AI Database Assistant')
            ->assertSee('Ask me anything about your database!');
    }

    /** @test */
    public function it_can_toggle_chat_open_and_closed()
    {
        Livewire::test(AiChatAssistant::class)
            ->assertSet('isOpen', false)
            ->call('toggleChat')
            ->assertSet('isOpen', true)
            ->call('toggleChat')
            ->assertSet('isOpen', false);
    }

    /** @test */
    public function it_can_open_chat_via_event()
    {
        Livewire::test(AiChatAssistant::class)
            ->assertSet('isOpen', false)
            ->dispatch('open-ai-chat')
            ->assertSet('isOpen', true);
    }

    /** @test */
    public function it_can_clear_conversation()
    {
        $component = Livewire::test(AiChatAssistant::class);
        
        // Add some conversation data
        $component->set('conversation', [
            [
                'role' => 'user',
                'content' => 'Test message',
                'timestamp' => now()->toDateTimeString(),
            ]
        ]);

        $component->call('clearConversation')
            ->assertSet('conversation', []);
    }

    /** @test */
    public function it_validates_empty_queries()
    {
        Livewire::test(AiChatAssistant::class)
            ->set('query', '')
            ->call('sendQuery')
            ->assertSet('isLoading', false)
            ->assertSet('isTyping', false);

        Livewire::test(AiChatAssistant::class)
            ->set('query', '   ')
            ->call('sendQuery')
            ->assertSet('isLoading', false)
            ->assertSet('isTyping', false);
    }

    /** @test */
    public function it_handles_send_query_flow()
    {
        $component = Livewire::test(AiChatAssistant::class)
            ->set('query', 'How many customers do we have?')
            ->call('sendQuery');

        // Check that loading state is set
        $component->assertSet('isLoading', true)
            ->assertSet('isTyping', false)
            ->assertSet('query', ''); // Query should be cleared

        // Check that user message was added to conversation
        $conversation = $component->get('conversation');
        $this->assertCount(2, $conversation); // User message + placeholder AI message
        $this->assertEquals('user', $conversation[0]['role']);
        $this->assertEquals('How many customers do we have?', $conversation[0]['content']);
        $this->assertEquals('assistant', $conversation[1]['role']);
        $this->assertEquals('', $conversation[1]['content']); // Placeholder
        $this->assertTrue($conversation[1]['streaming']);
    }

    /** @test */
    public function it_handles_ai_query_processing()
    {
        // Mock the AI service
        $this->mock(AiChatService::class, function ($mock) {
            $mock->shouldReceive('processQuery')
                ->once()
                ->with('Test query')
                ->andReturn([
                    'success' => true,
                    'response' => 'Test AI response',
                    'data' => ['test' => 'data']
                ]);
        });

        $component = Livewire::test(AiChatAssistant::class);
        
        // Set up initial state as if sendQuery was called
        $component->set('conversation', [
            [
                'role' => 'user',
                'content' => 'Test query',
                'timestamp' => now()->toDateTimeString(),
            ],
            [
                'role' => 'assistant',
                'content' => '',
                'data' => null,
                'timestamp' => now()->toDateTimeString(),
                'streaming' => true,
            ]
        ]);
        $component->set('currentMessageIndex', 1);
        $component->set('isTyping', true);

        // Process the AI query
        $component->call('processAiQuery', 'Test query');

        // Check that the response was processed correctly
        $conversation = $component->get('conversation');
        $this->assertEquals('Test AI response', $conversation[1]['content']);
        $this->assertEquals(['test' => 'data'], $conversation[1]['data']);
        $this->assertFalse($conversation[1]['streaming']);
        $component->assertSet('isTyping', false);
    }

    /** @test */
    public function it_handles_ai_service_errors()
    {
        // Mock the AI service to throw an exception
        $this->mock(AiChatService::class, function ($mock) {
            $mock->shouldReceive('processQuery')
                ->once()
                ->andThrow(new \Exception('Test error'));
        });

        $component = Livewire::test(AiChatAssistant::class);
        
        // Set up initial state
        $component->set('conversation', [
            [
                'role' => 'assistant',
                'content' => '',
                'data' => null,
                'timestamp' => now()->toDateTimeString(),
                'streaming' => true,
            ]
        ]);
        $component->set('currentMessageIndex', 0);
        $component->set('isTyping', true);

        // Process the AI query
        $component->call('processAiQuery', 'Test query');

        // Check that error was handled
        $conversation = $component->get('conversation');
        $this->assertStringContains('An error occurred: Test error', $conversation[0]['content']);
        $component->assertSet('isTyping', false);
    }

    /** @test */
    public function it_loads_conversation_from_session_on_mount()
    {
        $sessionConversation = [
            [
                'role' => 'user',
                'content' => 'Previous message',
                'timestamp' => now()->toDateTimeString(),
            ]
        ];

        session(['ai_chat_conversation' => $sessionConversation]);

        $component = Livewire::test(AiChatAssistant::class);
        $component->assertSet('conversation', $sessionConversation);
    }

    /** @test */
    public function it_has_responsive_properties()
    {
        $component = Livewire::test(AiChatAssistant::class);
        
        // Check that all responsive properties exist
        $component->assertPropertyExists('isOpen')
            ->assertPropertyExists('query')
            ->assertPropertyExists('conversation')
            ->assertPropertyExists('isLoading')
            ->assertPropertyExists('isTyping')
            ->assertPropertyExists('currentResponse')
            ->assertPropertyExists('currentMessageIndex');
    }
}
