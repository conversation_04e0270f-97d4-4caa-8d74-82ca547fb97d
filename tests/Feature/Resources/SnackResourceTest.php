<?php

use App\Enums\UserRole;
use App\Filament\Resources\SnackResource;
use App\Filament\Resources\SnackResource\Pages\ManageSnacks;
use App\Models\Group;
use App\Models\Itinerary;
use App\Models\Period;
use App\Models\User;
use App\Models\Vendors\SnackHandler;
use App\Settings\SnackSettings;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

use function Pest\Livewire\livewire;

uses(RefreshDatabase::class);

beforeEach(function () {
    Artisan::call('app:setup-roles');

    Period::factory()->currentYear()->create();

    $this->actingAs(User::factory()->admin()->create());
});

it('can render index page', function () {
    $this->get(SnackResource::getUrl('index'))->assertSuccessful();
});

it('can list snacks', function () {
    // Create a snack handler
    $snackHandler = SnackHandler::factory()->create();

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks
    $itineraries = Itinerary::factory()->count(3)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
    ]);

    livewire(ManageSnacks::class)
        ->assertCanSeeTableRecords($itineraries);
});

it('does not list itineraries without snacks', function () {
    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries without snacks
    $itineraries = Itinerary::factory()->count(3)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => false,
    ]);

    livewire(ManageSnacks::class)
        ->assertCanNotSeeTableRecords($itineraries);
});

it('does not list itineraries from unconfirmed groups', function () {
    // Create a snack handler
    $snackHandler = SnackHandler::factory()->create();

    // Create a group with unconfirmed status
    $group = Group::factory()->create();

    // Create itineraries with snacks
    $itineraries = Itinerary::factory()->count(3)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
    ]);

    livewire(ManageSnacks::class)
        ->assertCanNotSeeTableRecords($itineraries);
});

it('can filter by month', function () {
    // Create a snack handler
    $snackHandler = SnackHandler::factory()->create();

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks for current month
    $currentMonth = Carbon::now();
    $itinerariesCurrentMonth = Itinerary::factory()->count(2)->create([
        'date' => $currentMonth,
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
    ]);

    // Create itineraries with snacks for next month
    $nextMonth = Carbon::now()->addMonth();
    $itinerariesNextMonth = Itinerary::factory()->count(2)->create([
        'date' => $nextMonth,
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
    ]);

    // Test filtering by current month
    livewire(ManageSnacks::class)
        ->filterTable('month', ['month' => $currentMonth->format('Y-m')])
        ->assertCanSeeTableRecords($itinerariesCurrentMonth)
        ->assertCanNotSeeTableRecords($itinerariesNextMonth);

    // Test filtering by next month
    livewire(ManageSnacks::class)
        ->filterTable('month', ['month' => $nextMonth->format('Y-m')])
        ->assertCanSeeTableRecords($itinerariesNextMonth)
        ->assertCanNotSeeTableRecords($itinerariesCurrentMonth);
});

it('can filter by city', function () {
    // Create a snack handler
    $snackHandler = SnackHandler::factory()->create();

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks for Makkah
    $itinerariesMakkah = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
        'city' => 'Makkah',
    ]);

    // Create itineraries with snacks for Madinah
    $itinerariesMadinah = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
        'city' => 'Madinah',
    ]);

    // Test filtering by Makkah
    livewire(ManageSnacks::class)
        ->filterTable('city', 'Makkah')
        ->assertCanSeeTableRecords($itinerariesMakkah)
        ->assertCanNotSeeTableRecords($itinerariesMadinah);

    // Test filtering by Madinah
    livewire(ManageSnacks::class)
        ->filterTable('city', 'Madinah')
        ->assertCanSeeTableRecords($itinerariesMadinah)
        ->assertCanNotSeeTableRecords($itinerariesMakkah);
});

it('can filter by snack handler', function () {
    // Create snack handlers
    $snackHandler1 = SnackHandler::factory()->create();
    $snackHandler2 = SnackHandler::factory()->create();

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks for handler 1
    $itinerariesHandler1 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler1->id,
    ]);

    // Create itineraries with snacks for handler 2
    $itinerariesHandler2 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler2->id,
    ]);

    // Test filtering by handler 1
    livewire(ManageSnacks::class)
        ->filterTable('snack_handler_id', $snackHandler1->id)
        ->assertCanSeeTableRecords($itinerariesHandler1)
        ->assertCanNotSeeTableRecords($itinerariesHandler2);

    // Test filtering by handler 2
    livewire(ManageSnacks::class)
        ->filterTable('snack_handler_id', $snackHandler2->id)
        ->assertCanSeeTableRecords($itinerariesHandler2)
        ->assertCanNotSeeTableRecords($itinerariesHandler1);
});

it('restricts snack handler to only see their assigned snacks', function () {
    // Create snack handlers
    $snackHandler1 = SnackHandler::factory()->create();
    $snackHandler2 = SnackHandler::factory()->create();

    // Create users for each handler
    $handlerUser1 = User::factory()->role(UserRole::SnackHandler)->create();
    $handlerUser1->vendors()->attach($snackHandler1->id);

    $handlerUser2 = User::factory()->role(UserRole::SnackHandler)->create();
    $handlerUser2->vendors()->attach($snackHandler2->id);

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks for handler 1
    $itinerariesHandler1 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler1->id,
    ]);

    // Create itineraries with snacks for handler 2
    $itinerariesHandler2 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler2->id,
    ]);

    // Test handler 1 can only see their assigned snacks
    test()->actingAs($handlerUser1);
    livewire(ManageSnacks::class)
        ->assertCanSeeTableRecords($itinerariesHandler1)
        ->assertCanNotSeeTableRecords($itinerariesHandler2);

    // Test handler 2 can only see their assigned snacks
    test()->actingAs($handlerUser2);
    livewire(ManageSnacks::class)
        ->assertCanSeeTableRecords($itinerariesHandler2)
        ->assertCanNotSeeTableRecords($itinerariesHandler1);
});

it('allows admin to see all snacks', function () {
    // Create snack handlers
    $snackHandler1 = SnackHandler::factory()->create();
    $snackHandler2 = SnackHandler::factory()->create();

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks for handler 1
    $itinerariesHandler1 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler1->id,
    ]);

    // Create itineraries with snacks for handler 2
    $itinerariesHandler2 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler2->id,
    ]);

    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    // Test admin can see all snacks
    livewire(ManageSnacks::class)
        ->assertCanSeeTableRecords($itinerariesHandler1)
        ->assertCanSeeTableRecords($itinerariesHandler2);
});

it('allows operator to see all snacks', function () {
    // Create snack handlers
    $snackHandler1 = SnackHandler::factory()->create();
    $snackHandler2 = SnackHandler::factory()->create();

    // Create a group with confirmed status
    $group = Group::factory()->confirmed()->create();

    // Create itineraries with snacks for handler 1
    $itinerariesHandler1 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler1->id,
    ]);

    // Create itineraries with snacks for handler 2
    $itinerariesHandler2 = Itinerary::factory()->count(2)->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler2->id,
    ]);

    // Create and authenticate operator user
    $operatorUser = User::factory()->role(UserRole::Operator)->create();
    test()->actingAs($operatorUser);

    // Test operator can see all snacks
    livewire(ManageSnacks::class)
        ->assertCanSeeTableRecords($itinerariesHandler1)
        ->assertCanSeeTableRecords($itinerariesHandler2);
});

it('has settings action', function () {
    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    livewire(ManageSnacks::class)
        ->assertActionExists('settings');
});

it('has export actions', function () {
    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    livewire(ManageSnacks::class)
        ->assertActionExists('xlsx')
        ->assertActionExists('csv');
});

it('exports correct data format', function () {
    // Create test data
    $snackHandler = SnackHandler::factory()->create();
    $group = Group::factory()->confirmed()->create();
    $itinerary = Itinerary::factory()->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
        'city' => 'Makkah',
        'location' => 'Hotel Lobby',
        'description' => 'Evening snack',
        'snack_details' => 'Fruit Basket',
    ]);

    // Set snack prices
    $settings = app(SnackSettings::class);
    $settings->prices = ['Fruit Basket' => 15];
    $settings->save();

    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    $response = livewire(ManageSnacks::class)
        ->callAction('xlsx')
        ->assertFileDownloaded();

    // Get the temporary file path from the response
    $filePath = $response->json('download')['path'];

    // Verify the file exists and is readable
    $this->assertFileExists($filePath);
    $this->assertFileIsReadable($filePath);

    // Load and verify spreadsheet contents
    $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx;
    $spreadsheet = $reader->load($filePath);
    $sheet = $spreadsheet->getActiveSheet();

    // Verify headers
    $this->assertEquals('Date', $sheet->getCell('A1')->getValue());
    $this->assertEquals('Snack Price', $sheet->getCell('K1')->getValue());

    // Verify data
    $this->assertEquals('Fruit Basket', $sheet->getCell('J2')->getValue());
    $this->assertEquals(15, $sheet->getCell('K2')->getValue());

    // Clean up
    unlink($filePath);
});

it('exports correct CSV format', function () {
    // Create test data
    $snackHandler = SnackHandler::factory()->create();
    $group = Group::factory()->confirmed()->create();
    Itinerary::factory()->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
        'city' => 'Makkah',
        'snack_details' => 'Fruit Basket',
    ]);

    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    livewire(ManageSnacks::class)
        ->callAction('csv')
        ->assertFileDownloaded(function (string $content) {
            $lines = explode(PHP_EOL, $content);

            // Verify headers
            $this->assertStringContainsString('Date,Customer,Group ID,Group,Total Pax', $lines[0]);
            $this->assertStringContainsString('Snack Handler,Snack Type,Snack Price', $lines[0]);

            // Verify data contains expected values
            $this->assertStringContainsString('Fruit Basket', $lines[1]);

            return true;
        });
});

it('prevents unauthorized users from accessing snack resource', function () {
    // Create and authenticate a user with Customer role (not authorized)
    $user = User::factory()->role(UserRole::Customer)->create();
    test()->actingAs($user);

    test()->get(SnackResource::getUrl('index'))->assertForbidden();
});

it('handles invalid month filter format', function () {
    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    livewire(ManageSnacks::class)
        ->filterTable('month', ['month' => 'invalid-date'])
        ->assertCanSeeTableRecords([]);
});

it('handles empty city filter', function () {
    // Create test data
    $snackHandler = SnackHandler::factory()->create();
    $group = Group::factory()->confirmed()->create();
    $itinerary = Itinerary::factory()->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
        'city' => null,
    ]);

    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    livewire(ManageSnacks::class)
        ->filterTable('city', '')
        ->assertCanSeeTableRecords([$itinerary]);
});

it('handles snack handler with multiple vendors', function () {
    // Create snack handlers
    $snackHandler1 = SnackHandler::factory()->create();
    $snackHandler2 = SnackHandler::factory()->create();

    // Create user assigned to both handlers
    $handlerUser = User::factory()->role(UserRole::SnackHandler)->create();
    $handlerUser->vendors()->attach([$snackHandler1->id, $snackHandler2->id]);

    // Create groups and itineraries
    $group = Group::factory()->confirmed()->create();
    $itinerary1 = Itinerary::factory()->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler1->id,
    ]);
    $itinerary2 = Itinerary::factory()->create([
        'date' => today(),
        'group_id' => $group->id,
        'has_snack' => true,
        'snack_handler_id' => $snackHandler2->id,
    ]);

    // Test handler can see both assigned snacks
    test()->actingAs($handlerUser);
    livewire(ManageSnacks::class)
        ->assertCanSeeTableRecords([$itinerary1, $itinerary2]);
});

it('handles empty snack list', function () {
    // Create and authenticate admin user
    $adminUser = User::factory()->admin()->create();
    test()->actingAs($adminUser);

    livewire(ManageSnacks::class)
        ->assertCanNotSeeTableRecords([]);
});
