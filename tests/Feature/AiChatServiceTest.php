<?php

namespace Tests\Feature;

use App\Services\AiChatService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class AiChatServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AiChatService $aiChatService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->aiChatService = new AiChatService();
    }

    /** @test */
    public function it_can_get_database_schema_info()
    {
        // Clear any cached schema info
        $this->aiChatService->clearSchemaCache();

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($this->aiChatService);
        $method = $reflection->getMethod('getDatabaseSchemaInfo');
        $method->setAccessible(true);

        $schemaInfo = $method->invoke($this->aiChatService);

        $this->assertIsString($schemaInfo);
        $this->assertStringContainsString('Available Tables and Schema:', $schemaInfo);
        $this->assertStringContainsString('Domain Context:', $schemaInfo);
        $this->assertStringContainsString('Umrah service management system', $schemaInfo);
    }

    /** @test */
    public function it_caches_schema_info()
    {
        // Clear any cached schema info
        $this->aiChatService->clearSchemaCache();

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($this->aiChatService);
        $method = $reflection->getMethod('getDatabaseSchemaInfo');
        $method->setAccessible(true);

        // First call should cache the result
        $schemaInfo1 = $method->invoke($this->aiChatService);
        
        // Second call should return cached result
        $schemaInfo2 = $method->invoke($this->aiChatService);

        $this->assertEquals($schemaInfo1, $schemaInfo2);
        $this->assertTrue(Cache::has('ai_chat_schema_info'));
    }

    /** @test */
    public function it_can_clear_schema_cache()
    {
        // First, ensure there's something in the cache
        $reflection = new \ReflectionClass($this->aiChatService);
        $method = $reflection->getMethod('getDatabaseSchemaInfo');
        $method->setAccessible(true);
        $method->invoke($this->aiChatService);

        $this->assertTrue(Cache::has('ai_chat_schema_info'));

        // Clear the cache
        $this->aiChatService->clearSchemaCache();

        $this->assertFalse(Cache::has('ai_chat_schema_info'));
    }

    /** @test */
    public function it_validates_raw_expressions_correctly()
    {
        $validExpressions = [
            'COUNT(*) as total',
            'SUM(amount) as total_amount',
            'AVG(price) as average_price',
            'MAX(created_at) as latest',
            'MIN(birthdate) as youngest',
            'CONCAT(first_name, " ", last_name) as full_name',
            'users.name as user_name',
            'table.column',
            '*'
        ];

        $invalidExpressions = [
            'DROP TABLE users',
            'DELETE FROM users',
            'INSERT INTO users',
            'UPDATE users SET',
            'EXEC sp_something',
            'LOAD_FILE("/etc/passwd")',
            'BENCHMARK(1000000, MD5(1))',
            'SLEEP(10)'
        ];

        foreach ($validExpressions as $expression) {
            $this->assertTrue(
                $this->aiChatService->isValidRawExpression($expression),
                "Expression '{$expression}' should be valid"
            );
        }

        foreach ($invalidExpressions as $expression) {
            $this->assertFalse(
                $this->aiChatService->isValidRawExpression($expression),
                "Expression '{$expression}' should be invalid"
            );
        }
    }

    /** @test */
    public function it_parses_join_conditions_correctly()
    {
        $validConditions = [
            'users.id = groups.user_id' => [
                'left' => 'users.id',
                'operator' => '=',
                'right' => 'groups.user_id'
            ],
            'customers.id != invoices.customer_id' => [
                'left' => 'customers.id',
                'operator' => '!=',
                'right' => 'invoices.customer_id'
            ]
        ];

        $invalidConditions = [
            'invalid condition',
            'users.id',
            'users.id = ',
            '= groups.user_id',
            'id = user_id' // Missing table prefixes
        ];

        foreach ($validConditions as $condition => $expected) {
            $result = $this->aiChatService->parseJoinCondition($condition);
            $this->assertEquals($expected, $result, "Condition '{$condition}' should parse correctly");
        }

        foreach ($invalidConditions as $condition) {
            $result = $this->aiChatService->parseJoinCondition($condition);
            $this->assertNull($result, "Condition '{$condition}' should be invalid");
        }
    }
}
