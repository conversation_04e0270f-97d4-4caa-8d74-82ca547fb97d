import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    content: ['./resources/**/*.blade.php'],
    darkMode: 'class',
    theme: {
        extend: {
            colors: {
                armblue: 'rgba(26, 43, 72, <alpha-value>)',
                armyellow: 'rgba(204, 168, 100, <alpha-value>)',
            },
            fontFamily: {
                sans: ['Inter', ...defaultTheme.fontFamily.sans],
            },
        },
    },
    plugins: [forms, typography],
};
