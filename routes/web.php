<?php

use App\Filament\Resources\GroupResource;
use App\Http\Controllers\Admin;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\Finance;
use App\Livewire\PilgrimDetails;
use App\Models\Group;
use App\Models\GroupPilgrim;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect('/admin');
});

Route::get('/login', fn () => redirect('/admin/login'))
    ->name('login');

Route::middleware(['auth'])->group(function () {
    Route::get('admin/groups/{group}/print', [Admin\GroupController::class, 'print'])->name('admin.groups.print');

    Route::get('admin/groups/{group}/print/luggage-tags', [Admin\GroupController::class, 'printLuggageTags'])->name('admin.groups.print.luggage-tags');
    Route::get('admin/groups/{group}/print/roomlist', [Admin\GroupController::class, 'printRoomlist'])->name('admin.groups.print.roomlist');

    Route::get('admin/groups/{group}/file', [Admin\GroupController::class, 'downloadFile'])->name('admin.groups.file');

    Route::get('admin/group-hotel/{groupHotel}/confirmation', [Admin\GroupHotelController::class, 'downloadConfirmation'])->name('admin.group-hotel.confirmation');

    Route::get('finance/invoices/{invoice}/view', [Finance\InvoiceController::class, 'view']);
});

Route::get('groups/{group}/customer-view', [Admin\GroupController::class, 'customerView'])->name('groups.customer-view');

Route::get('customers/{customerUuid}/statements', [CustomerController::class, 'statements'])->name('customers.statements');
Route::get('customers/{customerUuid}/invoices/{invoice}', [CustomerController::class, 'invoice'])->name('customers.invoices.show');

Route::get('luggage-tag/{code}', function ($code) {
    $ids = explode(':', base64_decode($code));
    if (count($ids) === 2 && GroupPilgrim::where('group_id', $ids[0])->where('pilgrim_id', $ids[1])->exists()) {
        if (auth('web')->check()) {
            return redirect(GroupResource::getUrl('pilgrim-details', ['record' => $ids[0], 'pilgrim' => $ids[1]]));
        } else {
            $group = Group::find($ids[0]);
            $newCode = $group->token . ':' . $ids[0] . ':' . $ids[1];

            return redirect('/pd/' . base64_encode($newCode));
        }
    }

    return abort(404);
})->name('luggage-tag');
Route::get('pd/{code}', PilgrimDetails::class)->name('pilgrim-details');
