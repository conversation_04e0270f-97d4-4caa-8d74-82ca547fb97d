<?php

use Filament\Tables\Table;

return [
    'resources' => [
        'label' => 'Activity Log',
        'plural_label' => 'Activity Logs',
        'navigation_item' => true,
        'navigation_group' => 'System',
        'navigation_icon' => 'heroicon-o-newspaper',
        'navigation_sort' => 0,
        'default_sort_column' => 'id',
        'default_sort_direction' => 'desc',
        'navigation_count_badge' => false,
        'resource' => \Rmsramos\Activitylog\Resources\ActivitylogResource::class,
    ],
    'datetime_format' => Table::$defaultDateTimeDisplayFormat,
];
