<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    // Other service configurations...

    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'api_url' => env('OPENAI_API_URL', 'https://api.openai.com/v1/chat/completions'),
    ],

    'currency' => [
        'driver' => env('CURRENCY_DRIVER', 'bank_indonesia_v2'),
        'key' => env('CURRENCY_API_KEY'),
        'base_url' => 'https://v6.exchangerate-api.com/v6',
    ],

    'whatsapp' => [
        'driver' => env('WHATSAPP_DRIVER', 'wuzapi'),
    ],

    'pdf' => [
        'driver' => env('PDF_DRIVER', 'gotenberg'),
        'gotenberg' => [
            'url' => env('GOTENBERG_URL', 'http://localhost:3000'),
        ],
    ],

];
