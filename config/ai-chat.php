<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AI Chat Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for the AI chat feature.
    |
    */

    // Tables that the AI is allowed to query
    'allowed_tables' => [
        'agents',
        'airlines',
        'airport_handler',
        'airports',
        'bill_items',
        'bill_payments',
        'bills',
        'cash_accounts',
        'cash_categories',
        'cash_transactions',
        'contacts',
        'currencies',
        'customer_cashes',
        'customer_users',
        'customers',
        'estimate_items',
        'estimates',
        'flights',
        'group_cashes',
        'group_data',
        'group_flights',
        'group_hotel',
        'group_pilgrim',
        'group_vehicle',
        'groups',
        'hotels',
        'institutions',
        'invoice_items',
        'invoice_payments',
        'invoice_refunds',
        'invoices',
        'itineraries',
        'itinerary_contacts',
        'journal_entries',
        'journal_entry_items',
        'manasiks',
        'order_items',
        'orders',
        'periods',
        'pilgrims',
        'product_categories',
        'products',
        'purchase_order_items',
        'purchase_orders',
        'rooms',
        'transports',
        'user_cashes',
        'user_vendor',
        'users',
        'vehicles',
        'vendors',
    ],

    // Maximum number of records to return
    'max_results' => 100,
];
