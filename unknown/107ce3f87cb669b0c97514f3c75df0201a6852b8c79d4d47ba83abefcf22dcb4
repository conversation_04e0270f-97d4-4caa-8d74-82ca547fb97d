<?php

use App\Models\Customer;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Ramsey\Uuid\Uuid;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('uuid', 36)->nullable()->after('id');
        });

        Customer::query()
            ->get()
            ->each(function (Customer $customer) {
                $customer->update([
                    'uuid' => Uuid::uuid4(),
                ]);
            });

        Schema::table('customers', function (Blueprint $table) {
            $table->string('uuid', 36)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            //
        });
    }
};
