<?php

namespace App\ReminderTypes;

use App\Models\Airport;
use App\Models\GroupFlight;
use Carbon\Carbon;
use Filament\Forms;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class Departure extends ReminderType
{
    protected static ?string $label = 'Group Departure';

    protected static string $model = GroupFlight::class;

    protected static string $defaultTemplate = 'Dear {recipient_name}, we would like to inform you that the group {group_name} (associated with {customer_name}) with {total_pax} passengers is scheduled to depart from {departure_airport} on {departure_datetime}.';

    protected static array $templateTags = [
        'recipient_name' => 'The name of the recipient receiving this notification.',
        'customer_name' => 'The name of the customer associated with the group.',
        'group_name' => 'The designated name of the departing group.',
        'departure_datetime' => "The scheduled date and time of the group's departure.",
        'departure_airport' => 'The airport code from where the group will depart.',
        'mealbox' => 'The mealbox.',
        'total_pax' => 'Total number of passengers in the group.',
    ];

    public static function getAdditionalFormSchema(): array
    {
        return [
            Forms\Components\Select::make('airport_codes')
                ->label('Departure airport')
                ->options(Airport::all()->pluck('name', 'code'))
                ->searchable()
                ->multiple(),
        ];
    }

    public static function findRelevantModels(int $interval, array $additionalData = []): Collection
    {
        $airportCodes = filled($additionalData['airport_codes']) ? $additionalData['airport_codes'] : ['JED', 'MED'];

        return GroupFlight::query()
            ->with('group')
            ->where('type', 'departure')
            ->whereIn('from', $airportCodes)
            ->whereDate('date_etd', '>=', now())
            ->whereDate('date_etd', '<=', now()->addDays(abs($interval)))
            ->get()
            ->filter(fn (GroupFlight $groupFlight) => $groupFlight->group->hasService('handling') || $groupFlight->group->hasService('airport'))
            ->values();
    }

    public static function getEventDate(Model $model): ?Carbon
    {
        return $model->date_etd;
    }

    /**
     * @param  Model|GroupFlight  $model
     */
    public static function getTagsValues(Model $model): array
    {
        return [
            'customer_name' => $model->group->customer->name,
            'group_name' => $model->group->name,
            'departure_datetime' => $model->date_etd->format('j F Y H:i'),
            'departure_airport' => $model->from,
            'mealbox' => $model->mealbox ? implode(', ', $model->mealbox) : '-',
            'total_pax' => $model->total_pax,
        ];
    }
}
