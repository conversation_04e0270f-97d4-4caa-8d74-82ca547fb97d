<?php

namespace App\Filament\Clusters\ReminderCluster\Resources;

use App\Filament\Clusters\ReminderCluster;
use App\Filament\Clusters\ReminderCluster\Resources\ReminderScheduleResource\Pages;
use App\Models\ReminderSchedule;
use App\Services\ReminderService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ReminderScheduleResource extends Resource
{
    protected static ?string $model = ReminderSchedule::class;

    protected static ?string $modelLabel = 'schedule';

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?string $cluster = ReminderCluster::class;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Textarea::make('message')
                    ->autosize(),
                Forms\Components\KeyValue::make('tags_values')
                    ->label('Data'),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        $reminderService = app(ReminderService::class);

        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['reminder']))
            ->columns([
                Tables\Columns\TextColumn::make('reminder')
                    ->label('Reminder')
                    ->getStateUsing(fn ($record) => $record->reminder->subject ?? $reminderService->getLabel($record->reminder->reminder_type))
                    ->description(fn ($record) => $record->reminder->subject ? $reminderService->getLabel($record->reminder->reminder_type) : null),
                // TODO:
                Tables\Columns\TextColumn::make('model')
                    ->getStateUsing(fn ($record) => $record->model_type . ' #' . $record->model_id),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sent_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth('lg')
                    ->mutateRecordDataUsing(fn ($record) => ['message' => $record->getMessageContent(), 'tags_values' => $record->getTagsValues()]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('reminder')
                    ->relationship('reminder', 'subject')
                    ->searchable()
                    ->preload(),
                Tables\Filters\TernaryFilter::make('sent')
                    ->attribute('sent_at')
                    ->nullable(),
            ])
            ->defaultSort('scheduled_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageReminderSchedules::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }
}
