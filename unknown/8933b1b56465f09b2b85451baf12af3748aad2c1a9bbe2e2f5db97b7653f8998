<?php

use App\Console\Commands\SendReminders;
use App\Enums\NotificationType;
use App\Enums\ReminderRecipientType;
use App\Jobs\SendNotification;
use App\Models\GroupFlight;
use App\Models\Itinerary;
use App\Models\Reminder;
use App\Models\ReminderRecipient;
use App\Models\ReminderSchedule;
use App\Models\User;
use App\Models\Vendors\AirportHandler;
use App\Models\Vendors\SnackHandler;
use App\Services\ReminderService;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->reminderService = Mockery::mock(ReminderService::class);
    $this->app->instance(ReminderService::class, $this->reminderService);

    $this->command = new SendReminders($this->reminderService);

    // Set up Bus fake to capture dispatched jobs
    Bus::fake();
});

test('sendReminders command sends scheduled reminders', function () {
    // Create a reminder schedule that is due to be sent
    $reminderSchedule = ReminderSchedule::factory()->forDeparture()->scheduled()->create();
    $reminder = $reminderSchedule->reminder;
    $model = $reminderSchedule->model;

    // Add a recipient to the reminder
    $user = User::factory()->create();
    $recipient = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
        'recipient_type' => 'user',
        'recipient_id' => $user->id,
    ]);

    // Mock the reminderService generateContent method
    $this->reminderService->shouldReceive('generateContent')
        ->once()
        ->with(Mockery::type(Reminder::class), Mockery::type(GroupFlight::class))
        ->andReturn('Test reminder content');

    // Run the command
    $sentAt = now()->toDateTimeString();
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that the reminder schedule was marked as sent
    $this->assertDatabaseHas('reminder_schedules', [
        'id' => $reminderSchedule->id,
        'sent_at' => $sentAt,
    ]);

    // Check that the notification job was dispatched
    Bus::assertDispatched(SendNotification::class, function ($job) use ($user) {
        return $job->type === NotificationType::Email &&
               $job->recipient === $user->email &&
               $job->content === 'Test reminder content';
    });
});

test('sendReminders command does not send already sent reminders', function () {
    // Create a reminder schedule that has already been sent
    $reminderSchedule = ReminderSchedule::factory()->forDeparture()->sent()->create();

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that no notification jobs were dispatched
    Bus::assertNotDispatched(SendNotification::class);
});

test('sendReminders command does not send future reminders', function () {
    // Create a reminder schedule that is scheduled for the future
    $reminderSchedule = ReminderSchedule::factory()->forDeparture()->create([
        'scheduled_at' => now()->addDays(1),
    ]);

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that no notification jobs were dispatched
    Bus::assertNotDispatched(SendNotification::class);
});

test('sendReminders command handles multiple recipients', function () {
    // Create a reminder schedule that is due to be sent
    $reminderSchedule = ReminderSchedule::factory()->forDeparture()->scheduled()->create();
    $reminder = $reminderSchedule->reminder;
    $model = $reminderSchedule->model;

    // Add multiple recipients to the reminder
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    $recipient1 = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
        'recipient_type' => 'user',
        'recipient_id' => $user1->id,
    ]);

    $recipient2 = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
        'recipient_type' => 'user',
        'recipient_id' => $user2->id,
    ]);

    // Mock the reminderService generateContent method
    $this->reminderService->shouldReceive('generateContent')
        ->once()
        ->with(Mockery::type(Reminder::class), Mockery::type(GroupFlight::class))
        ->andReturn('Test reminder content');

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that notification jobs were dispatched for both users
    Bus::assertDispatched(SendNotification::class, function ($job) use ($user1) {
        return $job->recipient === $user1->email;
    });

    Bus::assertDispatched(SendNotification::class, function ($job) use ($user2) {
        return $job->recipient === $user2->email;
    });
});

test('sendReminders command handles role recipients', function () {
    // Create a role and assign users to it
    $role = Role::create(['name' => 'test-role']);
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $user1->assignRole($role);
    $user2->assignRole($role);

    // Create a reminder schedule that is due to be sent
    $reminderSchedule = ReminderSchedule::factory()->forDeparture()->scheduled()->create();
    $reminder = $reminderSchedule->reminder;
    $model = $reminderSchedule->model;

    // Add a role recipient to the reminder
    $recipient = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
        'recipient_type' => 'role',
        'recipient_id' => $role->id,
    ]);

    // Mock the reminderService generateContent method
    $this->reminderService->shouldReceive('generateContent')
        ->once()
        ->with(Mockery::type(Reminder::class), Mockery::type(GroupFlight::class))
        ->andReturn('Test reminder content');

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that notification jobs were dispatched for both users in the role
    Bus::assertDispatched(SendNotification::class, function ($job) use ($user1) {
        return $job->recipient === $user1->email;
    });

    Bus::assertDispatched(SendNotification::class, function ($job) use ($user2) {
        return $job->recipient === $user2->email;
    });
});

test('sendReminders command handles WhatsApp notifications', function () {
    // Create a reminder schedule with WhatsApp notification type
    $reminder = Reminder::factory()->departure()->whatsapp()->create();
    $reminderSchedule = ReminderSchedule::factory()->forReminder($reminder)->create([
        'scheduled_at' => now()->subHour(),
        'sent_at' => null,
    ]);
    $model = $reminderSchedule->model;

    // Add a recipient to the reminder
    $user = User::factory()->create();
    $recipient = ReminderRecipient::factory()->create([
        'reminder_id' => $reminder->id,
        'recipient_type' => ReminderRecipientType::User->value,
        'recipient_id' => $user->id,
    ]);

    // Mock the reminderService generateContent method
    $this->reminderService->shouldReceive('generateContent')
        ->once()
        ->with(Mockery::type(Reminder::class), Mockery::type(GroupFlight::class))
        ->andReturn('Test reminder content');

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that a WhatsApp notification job was dispatched
    Bus::assertDispatched(SendNotification::class, function ($job) use ($user) {
        return $job->type === NotificationType::WhatsApp &&
               $job->recipient === $user->phone;
    });
});

test('sendReminders command handles handler recipient type for GroupFlight', function () {
    // Create an airport handler with associated users
    $airportHandler = AirportHandler::factory()->create();
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $airportHandler->users()->attach([$user1->id, $user2->id]);

    // Create a group flight with the handler
    $groupFlight = GroupFlight::factory()->withMealbox()->create([
        'handler_id' => $airportHandler->id,
    ]);

    // Create a reminder with handler recipient type
    $reminder = Reminder::factory()->departure()->create();
    $reminderSchedule = ReminderSchedule::factory()->create([
        'reminder_id' => $reminder->id,
        'model_type' => Relation::getMorphAlias(GroupFlight::class),
        'model_id' => $groupFlight->id,
        'scheduled_at' => now()->subHour(),
        'sent_at' => null,
    ]);

    // Add a handler recipient to the reminder
    $recipient = ReminderRecipient::factory()->handler()->create([
        'reminder_id' => $reminder->id,
    ]);

    // Mock the reminderService generateContent method
    $this->reminderService->shouldReceive('generateContent')
        ->once()
        ->with(Mockery::type(Reminder::class), Mockery::type(GroupFlight::class))
        ->andReturn('Test reminder content');

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that notification jobs were dispatched for both handler users
    Bus::assertDispatched(SendNotification::class, function ($job) use ($user1) {
        return $job->type === NotificationType::Email &&
               $job->recipient === $user1->email;
    });

    Bus::assertDispatched(SendNotification::class, function ($job) use ($user2) {
        return $job->type === NotificationType::Email &&
               $job->recipient === $user2->email;
    });
});

test('sendReminders command handles handler recipient type for Itinerary', function () {
    // Create a snack handler with associated users
    $snackHandler = SnackHandler::factory()->create();
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    $snackHandler->users()->attach([$user1->id, $user2->id]);

    // Create an itinerary with the handler
    $itinerary = Itinerary::factory()->create([
        'has_snack' => true,
        'snack_handler_id' => $snackHandler->id,
    ]);

    // Create a reminder with handler recipient type
    $reminder = Reminder::factory()->snackReminder()->create();
    $reminderSchedule = ReminderSchedule::factory()->create([
        'reminder_id' => $reminder->id,
        'model_type' => Relation::getMorphAlias(Itinerary::class),
        'model_id' => $itinerary->id,
        'scheduled_at' => now()->subHour(),
        'sent_at' => null,
    ]);

    // Add a handler recipient to the reminder
    $recipient = ReminderRecipient::factory()->handler()->create([
        'reminder_id' => $reminder->id,
    ]);

    // Mock the reminderService generateContent method
    $this->reminderService->shouldReceive('generateContent')
        ->once()
        ->with(Mockery::type(Reminder::class), Mockery::type(Itinerary::class))
        ->andReturn('Test reminder content');

    // Run the command
    $this->artisan('reminders:send')
        ->assertSuccessful();

    // Check that notification jobs were dispatched for both handler users
    Bus::assertDispatched(SendNotification::class, function ($job) use ($user1) {
        return $job->type === NotificationType::Email &&
               $job->recipient === $user1->email;
    });

    Bus::assertDispatched(SendNotification::class, function ($job) use ($user2) {
        return $job->type === NotificationType::Email &&
               $job->recipient === $user2->email;
    });
});
