@php
    use App\Filament\Resources\GroupResource;
    use Umrahservice\Groups\Models\Group;
@endphp

<x-filament-widgets::widget>
    <x-filament-tables::container>
        <x-filament-tables::header heading="Top Services" :actions-position="null" />
        <div class="relative overflow-x-auto divide-y divide-gray-200 fi-ta-content dark:divide-white/10 dark:border-t-white/10">
            <x-filament-tables::table>
                <x-slot name="header">
                    <x-filament-tables::header-cell>
                        Service
                    </x-filament-tables::header-cell>
                    <x-filament-tables::header-cell alignment="end">
                        Groups
                    </x-filament-tables::header-cell>
                    <x-filament-tables::header-cell alignment="end">
                        Total Pax
                    </x-filament-tables::header-cell>
                    <th class="w-1"></th>
                </x-slot>
                @foreach ($services as $service)
                <x-filament-tables::row>
                    <x-filament-tables::cell>
                        <div class="px-3 py-3 text-sm">
                            {{ Group::GROUP_SERVICES[$service['name']] ?? $service['name'] }}
                        </div>
                    </x-filament-tables::cell>
                    <x-filament-tables::cell class="text-right">
                        <div class="px-3 py-3 text-sm">
                            {{ $service['groups_count'] }}
                        </div>
                    </x-filament-tables::cell>
                    <x-filament-tables::cell class="text-right">
                        <div class="px-3 py-3 text-sm">
                            {{ $service['total_pax'] }}
                        </div>
                    </x-filament-tables::cell>
                    <x-filament-tables::cell class="text-right">
                        <div class="px-3 py-3 text-sm">
                            @if ($service['key'] === 'full_la')
                            <x-filament::button
                                tag="a"
                                :href="GroupResource::getUrl('index', ['tableFilters' => ['services' => ['values' => ['handling', 'visa', 'hotel']]]])"
                                color="gray"
                                size="xs"
                                icon="heroicon-m-arrow-right"
                                icon-position="after"
                            >
                                Details
                            </x-filament::button>
                            @else
                            <x-filament::button
                                tag="a"
                                :href="GroupResource::getUrl('index', ['tableFilters' => ['services' => ['values' => [$service['key']]]]])"
                                color="gray"
                                size="xs"
                                icon="heroicon-m-arrow-right"
                                icon-position="after"
                            >
                                Details
                            </x-filament::button>
                            @endif
                        </div>
                    </x-filament-tables::cell>
                </x-filament-tables::row>
                @endforeach
            </x-filament-tables::table>
        </div>
    </x-filament-tables::container>
</x-filament-widgets::widget>
