<?php

namespace App\Models;

use App\Contracts\IsActivitySubject;
use App\Enums\HotelRoomType;
use App\Models\Traits\DefaultLogOptions;
use App\Models\Vendors\HotelBroker;
use Filament\Forms;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\EloquentSortable\SortableTrait;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class GroupHotel extends Pivot implements HasMedia, IsActivitySubject
{
    use HasFactory;
    use InteractsWithMedia;
    use SortableTrait;
    use Traits\DateScopes;
    use Traits\Metable;
    // use LogsActivity;
    // use DefaultLogOptions;

    protected $table = 'group_hotel';

    protected $fillable = [
        'group_id',
        'hotel_id',

        'check_in',
        'check_out',

        'room_single_count',
        'room_double_count',
        'room_triple_count',
        'room_quad_count',
        'room_quint_count',

        'is_confirmed',
        'confirmation_number',
        'confirmation_file',

        'broker_id',

        'notes',
        'more_info',

        'sort',

        'meta',
    ];

    protected $casts = [
        'check_in' => 'datetime:Y-m-d',
        'check_out' => 'datetime:Y-m-d',
        'is_confirmed' => 'boolean',
        'meta' => 'array',
    ];

    public $timestamps = false;

    public $incrementing = true;

    public function getActivitySubjectDescription(Activity $activity): string
    {
        return "Group Hotel #{$this->group_id}";
    }

    // protected static function booted()
    // {
    //     static::addGlobalScope('order', function (Builder $query) {
    //         $query->reorder('check_in', 'asc');
    //     });
    // }

    public function hotelNameWithType(): Attribute
    {
        $that = $this;

        return Attribute::make(
            get: function () use ($that) {
                $roomType = HotelRoomType::tryFrom($that->meta['room_type'] ?? '');

                return $roomType
                    ? $that->hotel->fullname . ' (' . $roomType->getShortLabel() . ')'
                    : $that->hotel->fullname;
            }
        );
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function hotel(): BelongsTo
    {
        return $this->belongsTo(Hotel::class);
    }

    public function broker(): BelongsTo
    {
        return $this->belongsTo(HotelBroker::class);
    }

    public function getConfirmationFileUrlAttribute(): string
    {
        return asset('storage/' . $this->confirmation_file);
    }

    public function scopeCurrentPeriod(Builder $query)
    {
        $period = current_period();

        $query->where(function (Builder $query) use ($period) {
            $query
                ->whereBetween('check_in', [
                    $period->date_start->format('Y-m-d') . ' 00:00:00',
                    $period->date_end->format('Y-m-d') . ' 23:59:59',
                ]);
        });
    }

    public static function getFormSchema($withGroup = true): array
    {
        return [
            Forms\Components\Tabs::make()
                ->schema([
                    Forms\Components\Tabs\Tab::make('Details')
                        ->schema([
                            ...($withGroup ? [Group::formFieldSelectGroup()
                                ->columnSpanFull()] : []),
                            Forms\Components\Select::make('hotel_id')
                                ->label('Hotel')
                                ->relationship('hotel', 'fullname', fn ($query) => $query->orderBy('fullname'))
                                ->searchable()
                                ->preload()
                                ->required()
                                ->columnSpanFull(),
                            Forms\Components\DatePicker::make('check_in')
                                ->label('Check In')
                                ->required()
                                ->columnSpan(['lg' => 2]),
                            Forms\Components\DatePicker::make('check_out')
                                ->label('Check Out')
                                ->required()
                                ->columnSpan(['lg' => 2]),
                            Forms\Components\Grid::make(5)
                                ->columnSpanFull()
                                ->schema([
                                    Forms\Components\TextInput::make('room_single_count')
                                        ->label('Single')
                                        ->numeric()
                                        ->required()
                                        ->default(0),
                                    Forms\Components\TextInput::make('room_double_count')
                                        ->label('Double')
                                        ->numeric()
                                        ->required()
                                        ->default(0),
                                    Forms\Components\TextInput::make('room_triple_count')
                                        ->label('Triple')
                                        ->numeric()
                                        ->required()
                                        ->default(0),
                                    Forms\Components\TextInput::make('room_quad_count')
                                        ->label('Quad')
                                        ->numeric()
                                        ->required()
                                        ->default(0),
                                    Forms\Components\TextInput::make('room_quint_count')
                                        ->label('Quint')
                                        ->numeric()
                                        ->required()
                                        ->default(0),
                                ]),
                            Forms\Components\Select::make('meta.room_type')
                                ->label('Room Type')
                                ->options(HotelRoomType::class)
                                ->native(false)
                                ->columnSpanFull(),
                            Forms\Components\TextInput::make('meta.meal')
                                ->label('Meal')
                                ->columnSpanFull(),
                        ])
                        ->columns(['lg' => 4]),
                    Forms\Components\Tabs\Tab::make('Confirmation')
                        ->schema([
                            Forms\Components\Select::make('broker_id')
                                ->label('Broker')
                                ->relationship('broker', 'company_name')
                                ->searchable()
                                ->preload(),
                            Forms\Components\TextInput::make('confirmation_number')
                                ->label('Confirmation No.'),
                            Forms\Components\FileUpload::make('confirmation_file')
                                ->previewable(false)
                                ->visible(fn ($record) => (bool) $record?->confirmation_file),
                            SpatieMediaLibraryFileUpload::make('confirmation_files')
                                ->collection('confirmation-files')
                                ->multiple()
                                ->disk('s3')
                                ->previewable(false),
                            Forms\Components\Toggle::make('is_confirmed')
                                ->label('Confirmed'),
                            Forms\Components\Textarea::make('more_info'),
                        ]),
                ]),
        ];
    }
}
