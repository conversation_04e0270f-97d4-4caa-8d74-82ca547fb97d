<?php

namespace Database\Factories;

use App\Models\Airline;
use App\Models\Group;
use App\Models\GroupFlight;
use Illuminate\Database\Eloquent\Factories\Factory;

class GroupFlightFactory extends Factory
{
    protected $model = GroupFlight::class;

    // Common IATA airport codes
    protected $airportCodes = [
        'JED', // Jeddah, Saudi Arabia
        'MED', // Madinah, Saudi Arabia
        'RUH', // Riyadh, Saudi Arabia
        'CGK', // Jakarta, Indonesia
        'SUB', // Surabaya, Indonesia
        'DPS', // Denpasar/Bali, Indonesia
        'KUL', // Kuala Lumpur, Malaysia
        'SIN', // Singapore
        'BKK', // Bangkok, Thailand
        'IST', // Istanbul, Turkey
        'DXB', // Dubai, UAE
        'AUH', // Abu Dhabi, UAE
        'DOH', // Doha, Qatar
        'CAI', // Cairo, Egypt
        'AMM', // Amman, Jordan
    ];

    public function definition()
    {
        $etd = now()->addDays(fake()->numberBetween(1, 30));
        $eta = $etd->copy()->addHours(fake()->numberBetween(2, 12));

        return [
            'airline_id' => Airline::factory(),
            'group_id' => Group::factory(),
            'type' => fake()->randomElement(['arrival', 'departure']),
            'flight_number' => strtoupper(fake()->randomLetter()) . fake()->numberBetween(100, 9999),
            'pax' => fake()->numberBetween(10, 100),
            'from' => $this->getRandomAirportCode(),
            'to' => $this->getRandomAirportCode(),
            'date_etd' => $etd,
            'date_eta' => $eta,
            'via_number' => null,
            'via' => null,
            'via_eta' => null,
            'via_etd' => null,
            'meta' => [],
        ];
    }

    /**
     * Get a random airport IATA code
     */
    protected function getRandomAirportCode()
    {
        return fake()->randomElement($this->airportCodes);
    }

    /**
     * Configure the model for Hajj/Umrah flights
     */
    public function hajjUmrah()
    {
        return $this->state(function (array $attributes) {
            $isArrival = $attributes['type'] === 'arrival';
            $saudiAirports = ['JED', 'MED'];

            return [
                'from' => $isArrival ? $this->getRandomAirportCode() : fake()->randomElement($saudiAirports),
                'to' => $isArrival ? fake()->randomElement($saudiAirports) : $this->getRandomAirportCode(),
            ];
        });
    }

    public function withVia()
    {
        return $this->state(function (array $attributes) {
            $etd = $attributes['date_etd'];
            $viaEtd = $etd->copy()->addHours(fake()->numberBetween(2, 6));
            $viaEta = $viaEtd->copy()->addHours(fake()->numberBetween(1, 3));

            // Ensure via is different from origin and destination
            $availableCodes = array_diff($this->airportCodes, [$attributes['from'], $attributes['to']]);

            return [
                'via_number' => strtoupper(fake()->randomLetter()) . fake()->numberBetween(100, 9999),
                'via' => fake()->randomElement($availableCodes),
                'via_etd' => $viaEtd,
                'via_eta' => $viaEta,
            ];
        });
    }

    public function withMealbox(?string $type = null)
    {
        return $this->state(function (array $attributes) use ($type) {
            $mealboxType = $type ?? fake()->randomElements(['Regular', 'Vegetarian', 'Halal', 'Kosher'], 2);
            $meta = $attributes['meta'] ?? [];
            $meta['mealbox'] = $mealboxType;

            return [
                'meta' => $meta,
            ];
        });
    }
}
