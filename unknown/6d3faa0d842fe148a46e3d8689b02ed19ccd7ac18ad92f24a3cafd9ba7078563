<?php

namespace App\Filament\Resources\CustomerResource\Pages;

use App\Filament\Resources\CustomerResource;
use App\Filament\Resources\CustomerResource\Widgets;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCustomer extends ViewRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderWidgets(): array
    {
        return [
            Widgets\CustomerOverview::class,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('statement')
                ->label('View Statement')
                ->url(route('customers.statements', ['customerUuid' => $this->record->uuid]))
                ->icon('heroicon-o-document-text')
                ->color('gray')
                ->openUrlInNewTab(),
            Actions\EditAction::make(),
        ];
    }
}
