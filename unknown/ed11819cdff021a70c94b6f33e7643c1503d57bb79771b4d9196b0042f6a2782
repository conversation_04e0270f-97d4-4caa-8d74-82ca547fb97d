<?php

namespace App\Filament\Finance\Resources\InvoiceResource\Pages;

use App\Actions\Invoice\GeneratePDF;
use App\Filament\Actions\ActivityLogTimelineAction;
use App\Filament\Finance\Resources\InvoiceResource;
use App\Models\Finance\Invoice;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoiceResource::class;

    protected static string $view = 'filament.finance.resources.invoice-resource.pages.view-invoice';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'View';

    protected function getHeaderActions(): array
    {
        return [
            ActivityLogTimelineAction::make('history'),
            Actions\Action::make('pdf')
                ->label('PDF')
                ->action(function ($record) {
                    return response()->download(GeneratePDF::run($record));
                })
                ->color('gray')
                ->icon('heroicon-o-arrow-down-tray'),
            Invoice::getSendPageAction(),
        ];
    }
}
