<?php

namespace App\Filament\Pages\Widgets;

use App\Models\Hotel;
use Filament\Support\Colors\Color;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class TopHotelsChart extends ChartWidget
{
    protected static ?string $heading = 'Top Hotels';

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = null;

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array | RawJs | null
    {
        return [
            'indexAxis' => 'y',
        ];
    }

    protected function getData(): array
    {
        $period = current_period();
        $hotels = Hotel::query()
            ->leftJoin('group_hotel', 'group_hotel.hotel_id', '=', 'hotels.id')
            ->leftJoin('groups', 'group_hotel.group_id', '=', 'groups.id')
            ->whereBetween('group_hotel.check_in', [
                $period->date_start->format('Y-m-d') . ' 00:00:00',
                $period->date_end->format('Y-m-d') . ' 23:59:59',
            ])
            ->select([
                'hotels.id',
                'hotels.name',
                'hotels.city',
                'hotels.fullname',
                DB::raw('count(`groups`.`id`) AS groups_count'),
                DB::raw('sum(`groups`.`total_pax`) AS pax_count'),
            ])
            ->groupBy(['hotels.id', 'hotels.name', 'hotels.city', 'hotels.fullname'])
            ->orderByDesc('pax_count')
            ->limit(10)
            ->get();

        $data = [
            'datasets' => [
                [
                    'label' => 'Groups',
                    'data' => $hotels->pluck('groups_count'),
                    'backgroundColor' => 'rgb(' . Color::Blue[500] . ')',
                    'borderColor' => 'transparent',
                ],
                [
                    'label' => 'Total Pax',
                    'data' => $hotels->pluck('pax_count'),
                    'backgroundColor' => 'rgb(' . Color::Amber[500] . ')',
                    'borderColor' => 'transparent',
                ],
            ],
            'labels' => $hotels->pluck('fullname'),
        ];
        // dd($data);

        return $data;
    }
}
