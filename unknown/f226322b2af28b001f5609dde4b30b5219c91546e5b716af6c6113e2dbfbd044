<x-layouts.document
    title="{{ $customer->name }} - Statement of Account"
    :autoPrint="false"
>
    <style>
        html {
            font-size: 10pt;
        }
    </style>

    <div class="border-t-4 border-blue-900"></div>

    <div class="flex justify-between mt-8">
        <div class="flex">
            @php
                $company = app(App\Settings\CompanySettings::class);
            @endphp
            <div class="mr-4">
                @inlinedImage(asset('images/logo-wide.svg'), 'w-auto h-[1.5cm]')
            </div>
            <div>
                <h3 class="font-bold">{{ $company->name }}</h3>
                <div class="text-sm">
                    {!! nl2br($company->address) !!}<br>
                    {{ $company->email }} | {{ $company->phone }}<br>
                    {{ $company->website }}
                </div>
            </div>
        </div>
        <div class="text-right">
            <h1 class="text-2xl font-bold">Statement of Account</h1>
            <p class="text-gray-600">Outstanding invoices</p>
        </div>
    </div>

    <div class="mt-16">
        <div class="flex justify-between">
            <div>
                <h3 class="text-gray-500">Bill to</h3>
                <p class="mt-2 font-bold">{{ $customer->name }}</p>
                <p>{{ $customer->owner_name }}</p>
            </div>
            <div class="text-right">
                <p class="text-gray-500">As of {{ today()->format('M d, Y') }}</p>
                <div class="pt-2 mt-2 space-y-1 border-t">
                    <div class="flex justify-between">
                        <span class="mr-8">Overdue</span>
                        <span class="font-bold tabular-nums">{{ money($overdueBalance, 'SAR', true) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="mr-8">Not yet due</span>
                        <span class="font-bold tabular-nums">{{ money($balance - $overdueBalance, 'SAR', true) }}</span>
                    </div>
                    <div class="flex justify-between pt-1 mt-1 border-t">
                        <span class="mr-8 font-bold">Outstanding balance</span>
                        <span class="font-bold tabular-nums">{{ money($balance, 'SAR', true) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8">
        <table class="w-full">
            <thead>
                <tr class="border-b border-gray-300">
                    <th class="py-2 text-left">Invoice #</th>
                    <th class="py-2 text-left">Invoice date</th>
                    <th class="py-2 text-left">Due date</th>
                    <th class="py-2 text-right">Total</th>
                    <th class="py-2 text-right">Paid</th>
                    <th class="py-2 text-right">Due</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoices as $invoice)
                <tr class="border-b border-gray-200">
                    <td class="py-3">
                        <a href="{{ route('customers.invoices.show', ['customerUuid' => $customer->uuid, 'invoice' => $invoice->id]) }}" class="text-blue-600 hover:underline">
                            {{ $invoice->invoice_number }}
                        </a>
                    </td>
                    <td class="py-3">{{ $invoice->invoice_date->format('M d, Y') }}</td>
                    <td class="py-3">
                        {{ $invoice->due_date ? $invoice->due_date->format('M d, Y') : $invoice->invoice_date->format('M d, Y') }}
                        @if($invoice->status->value === 'overdue')
                            <div class="text-sm text-red-600">Overdue</div>
                        @endif
                    </td>
                    <td class="py-3 text-right tabular-nums">{{ money($invoice->total, $invoice->currency_code, true) }}</td>
                    <td class="py-3 text-right tabular-nums">{{ money($invoice->paid, $invoice->currency_code, true) }}</td>
                    <td class="py-3 text-right tabular-nums">{{ money($invoice->total - $invoice->paid, $invoice->currency_code, true) }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr class="bg-gray-100">
                    <td colspan="5" class="py-3 font-bold text-right">Outstanding balance</td>
                    <td class="py-3 font-bold text-right">{{ money($balance, 'SAR', true) }}</td>
                </tr>
            </tfoot>
        </table>
    </div>

    <div class="mt-16 text-center">
        {{-- <p class="text-sm text-gray-600">
            Powered by
            <span class="inline-flex items-center">
                <span class="font-bold text-blue-600">wave</span>
            </span>
        </p> --}}
    </div>

    <div class="fixed top-4 right-4 print:hidden">
        <button onclick="window.print()" class="px-4 py-2 text-white bg-blue-600 rounded shadow hover:bg-blue-700">
            Print Statement
        </button>
    </div>
</x-layouts.document>
