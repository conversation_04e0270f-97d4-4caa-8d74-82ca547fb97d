<?php

namespace App\Filament\Resources\GroupResource\Widgets;

use App\Enums\Enums\Gender;
use App\Models\Group;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ManifestOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected static bool $isLazy = false;

    public ?Group $group;

    protected function getStats(): array
    {
        $pilgrims = $this->group->pilgrims;

        $maleCount = $pilgrims->where('gender', Gender::Male)->count();
        $femaleCount = $pilgrims->where('gender', Gender::Female)->count();
        $infantCount = $pilgrims->filter(fn ($pilgrim) => $pilgrim->age < 2)->count();

        return [
            Stat::make('Male', $maleCount),
            Stat::make('Female', $femaleCount),
            Stat::make('Infant', $infantCount)
                ->description('< 2 years old'),
        ];
    }
}
