<?php

namespace App\ReminderTypes;

use App\Models\Airport;
use App\Models\GroupFlight;
use Carbon\Carbon;
use Filament\Forms;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class Arrival extends ReminderType
{
    protected static ?string $label = 'Group Arrival';

    protected static string $model = GroupFlight::class;

    protected static string $defaultTemplate = 'Greetings {recipient_name}, we are pleased to inform you that the group {group_name} (associated with {customer_name}) with {total_pax} passengers is scheduled to arrive at {arrival_airport} on {arrival_datetime}.';

    protected static array $templateTags = [
        'recipient_name' => 'The name of the recipient receiving this notification.',
        'customer_name' => 'The name of the customer associated with the group.',
        'group_name' => 'The designated name of the arriving group.',
        'arrival_datetime' => "The anticipated date and time of the group's arrival.",
        'arrival_airport' => 'The airport code where the group will arrive.',
        'mealbox' => 'The mealbox.',
        'total_pax' => 'Total number of passengers in the group.',
    ];

    public static function getAdditionalFormSchema(): array
    {
        return [
            Forms\Components\Select::make('airport_codes')
                ->label('Arrival airport')
                ->options(Airport::all()->pluck('name', 'code'))
                ->searchable()
                ->multiple(),
        ];
    }

    public static function findRelevantModels(int $interval, array $additionalData = []): Collection
    {
        $airportCodes = filled($additionalData['airport_codes']) ? $additionalData['airport_codes'] : ['JED', 'MED'];

        return GroupFlight::query()
            ->with('group')
            ->where('type', 'arrival')
            ->whereIn('to', $airportCodes)
            ->whereDate('date_eta', '>=', now())
            ->whereDate('date_eta', '<=', now()->addDays(abs($interval)))
            ->get()
            ->filter(fn (GroupFlight $groupFlight) => $groupFlight->group->hasService('handling') || $groupFlight->group->hasService('airport'))
            ->values();
    }

    public static function getEventDate(Model $model): ?Carbon
    {
        return $model->date_eta;
    }

    /**
     * @param  Model|GroupFlight  $model
     */
    public static function getTagsValues(Model $model): array
    {
        return [
            'customer_name' => $model->group->customer->name,
            'group_name' => $model->group->name,
            'arrival_datetime' => $model->date_eta->format('j F Y H:i'),
            'arrival_airport' => $model->to,
            'mealbox' => $model->mealbox ? implode(', ', $model->mealbox) : '-',
            'total_pax' => $model->total_pax,
        ];
    }
}
