<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

trait DateScopes
{
    public function scopeWhereDateBetween(Builder $query, $column, $dates)
    {
        return $query
            ->where(function (Builder $query) use ($column, $dates) {
                return $query
                    ->whereDate($column, '>=', $dates[0])
                    ->whereDate($column, '<=', $dates[1]);
            });
    }
}
