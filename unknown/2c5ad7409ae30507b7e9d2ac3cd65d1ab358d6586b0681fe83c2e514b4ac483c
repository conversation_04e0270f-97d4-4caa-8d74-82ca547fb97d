@props([
    'title' => 'Document',
    'size' => 'A4',
    'orientation' => 'portrait',
    'margin' => '1cm',
    'autoPrint' => false,
])

@php
    [$width, $height] = match($size) {
        default => str_contains($size, ' ') ? explode(' ', $size) : ['21cm', '29.7cm']
    };
    if (str_contains($size, ' ')) {
        $orientation = '';
    }
@endphp

<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

        <title>{{ $title ?? 'Document' }}</title>

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet" />

        @filamentStyles
        <style>
            :root {
                --font-family: 'Plus Jakarta Sans';
            }
            {!! Vite::content('resources/css/filament/admin/theme.css') !!}

            @page {
                size: {{ $size ?? 'A4' }} {{ $orientation }};
                margin: {{ $margin ?? '1cm' }};
            }
        </style>
        <style media="screen">
            html {
                background: #ccc;
                font-size: 10pt;
            }
            body {
                background: white;
                box-shadow: 0.1cm 0.1cm 0.2cm rgba(0, 0, 0, 0.25);
                margin: 0.5cm auto;
                padding: {{ $margin ?? '1cm' }};
                width: {{ $orientation === 'landscape' ? $height : $width }};
                min-height: {{ $orientation === 'landscape' ? $width : $height }};
            }
        </style>
    </head>
    <body>
        {{ $slot }}
        @if ($autoPrint)
            <script>window.onload = () => window.print();</script>
        @endif
    </body>
</html>
