<?php

namespace App\Filament\Pages\Widgets;

use App\Filament\Resources\GroupResource;
use App\Models\Group;
use Carbon\Carbon;
use Filament\Support\Colors\Color;
use Filament\Support\RawJs;
use Filament\Widgets\ChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;

class MonthlyPax extends ChartWidget
{
    protected static ?string $heading = 'Monthly Stats';

    protected int | string | array $columnSpan = 'full';

    protected static bool $isLazy = false;

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array | RawJs | null
    {
        $groupsResourceUrl = GroupResource::getUrl('index');

        return RawJs::make(<<<JS
        {
            onHover: (ev, el) => {
            ev.native.target.style.cursor = el[0] ? 'pointer' : 'default';
            },
            onClick: (ev, els) => {
            if (els.length === 0) return;

            const { dataIndex, raw } = els[0].element.\$context;
            const barLabel = ev.chart.data.labels[dataIndex]; // e.g.: 'Aug-24'

            // Parse month and year from label (format: MMM-YY)
            const [monthStr, yearStr] = barLabel.split('-');
            const month = new Date(`\${monthStr} 1, 20\${yearStr}`).getMonth();
            const year = 2000 + parseInt(yearStr);

            // Create date objects for start and end of month
            const startDate = new Date(Date.UTC(year, month, 1));
            const endDate = new Date(Date.UTC(year, month + 1, 0));

            // Format dates as dd/mm/yyyy
            const formatDate = (date) => {
                const day = date.getUTCDate().toString().padStart(2, '0');
                const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
                const year = date.getUTCFullYear();
                return `\${day}/\${month}/\${year}`;
            };

            const dateRange = [
                formatDate(startDate),
                formatDate(endDate)
            ];

            window.location = '{$groupsResourceUrl}' + `?tableFilters[arrival_date][arrival_date]=\${dateRange[0]} - \${dateRange[1]}`;
            },
            responsive: true,
            stacked: false,
            interaction: {
            mode: 'index',
            intersect: false
            },
            scales: {
            x: {
                display: true,
                title: {
                display: true,
                text: 'Month'
                }
            },
            y: {
                display: true,
                position: 'left',
                title: {
                display: true,
                text: 'Total pax'
                }
            },
            y1: {
                display: true,
                position: 'right',
                title: {
                display: true,
                text: 'Groups'
                },
                border: {
                display: false
                },
                grid: {
                display: false,
                drawOnChartArea: false,
                drawTicks: false
                }
            }
            }
        }
        JS);
    }

    protected function getData(): array
    {
        $period = current_period();
        $totalPax = Trend::model(Group::class)
            ->between(
                $period->date_start,
                $period->date_end,
            )
            ->dateColumn('arrival_date')
            ->perMonth()
            ->sum('total_pax');
        $groups = Trend::model(Group::class)
            ->between(
                $period->date_start,
                $period->date_end,
            )
            ->dateColumn('arrival_date')
            ->perMonth()
            ->count();

        return [
            'datasets' => [
                [
                    'label' => 'Groups',
                    'data' => $groups->map(fn (TrendValue $value) => $value->aggregate),
                    'pointBackgroundColor' => 'rgb(' . Color::Blue[500] . ')',
                    'borderColor' => 'rgb(' . Color::Blue[500] . ')',
                    'tension' => 0.25,
                    'yAxisID' => 'y1',
                ],
                [
                    'label' => 'Total pax',
                    'data' => $totalPax->map(fn (TrendValue $value) => $value->aggregate),
                    'pointBackgroundColor' => 'rgb(' . Color::Amber[500] . ')',
                    'borderColor' => 'rgb(' . Color::Amber[500] . ')',
                    'tension' => 0.25,
                    'yAxisID' => 'y',
                ],
            ],
            'labels' => $totalPax->map(fn (TrendValue $value) => Carbon::createFromFormat('Y-m-d', $value->date . '-01')->format('M-y')),
        ];
    }
}
